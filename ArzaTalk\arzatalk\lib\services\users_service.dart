import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user_model.dart';

/// خدمة إدارة المستخدمين
class UsersService extends ChangeNotifier {
  static final UsersService _instance = UsersService._internal();
  factory UsersService() => _instance;
  UsersService._internal();

  final List<UserModel> _registeredUsers = [];
  final Map<String, UserModel> _usersCache = {};

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadRegisteredUsers();
    await _addRealUsersIfEmpty();
  }

  /// إضافة المستخدمين الحقيقيين إذا كانت القائمة فارغة
  Future<void> _addRealUsersIfEmpty() async {
    if (_registeredUsers.isEmpty) {
      debugPrint('📱 Adding real users data...');

      final realUsers = [
        // الأصدقاء الحقيقيون
        UserModel(
          phoneNumber: '+212716233701',
          name: 'محمد',
          gender: Gender.male,
          age: 20,
          country: 'المغرب',
          city: 'زاوية البئر',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
        UserModel(
          phoneNumber: '+212638813823',
          name: 'سعيد',
          gender: Gender.male,
          age: 28,
          country: 'المغرب',
          city: 'زاوية البئر',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
        UserModel(
          phoneNumber: '+212681626960',
          name: 'معاد',
          gender: Gender.male,
          age: 28,
          country: 'المغرب',
          city: 'زاوية البئر',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
        UserModel(
          phoneNumber: '+212646380642',
          name: 'أشرف',
          gender: Gender.male,
          age: 18,
          country: 'المغرب',
          city: 'زاوية البئر',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),

        // مستخدمون إضافيون لإثراء التطبيق
        UserModel(
          phoneNumber: '+212600111001',
          name: 'فاطمة الزهراء',
          gender: Gender.female,
          age: 25,
          country: 'المغرب',
          city: 'الدار البيضاء',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
        UserModel(
          phoneNumber: '+212600111002',
          name: 'عبد الرحمن',
          gender: Gender.male,
          age: 32,
          country: 'المغرب',
          city: 'الرباط',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
        UserModel(
          phoneNumber: '+212600111003',
          name: 'عائشة',
          gender: Gender.female,
          age: 29,
          country: 'المغرب',
          city: 'فاس',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
        UserModel(
          phoneNumber: '+212600111004',
          name: 'يوسف',
          gender: Gender.male,
          age: 26,
          country: 'المغرب',
          city: 'مراكش',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
        UserModel(
          phoneNumber: '+212600111005',
          name: 'خديجة',
          gender: Gender.female,
          age: 31,
          country: 'المغرب',
          city: 'أكادير',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
        UserModel(
          phoneNumber: '+212600111006',
          name: 'حسام',
          gender: Gender.male,
          age: 27,
          country: 'المغرب',
          city: 'طنجة',
          lastSeen: DateTime.now(),
          registrationDate: DateTime.now(),
        ),
      ];

      for (final user in realUsers) {
        _registeredUsers.add(user);
        _usersCache[user.phoneNumber] = user;
      }

      await _saveRegisteredUsers();
      debugPrint('✅ Added ${realUsers.length} real users');
    }
  }

  /// الحصول على مستخدم بالرقم
  Future<UserModel?> getUserByPhone(String phoneNumber) async {
    // البحث في الكاش أولاً
    if (_usersCache.containsKey(phoneNumber)) {
      return _usersCache[phoneNumber];
    }

    // البحث في المستخدمين المسجلين
    try {
      final user = _registeredUsers.firstWhere(
        (u) => u.phoneNumber == phoneNumber,
      );
      _usersCache[phoneNumber] = user;
      return user;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على مستخدم بالاسم
  Future<UserModel?> getUserByName(String name) async {
    try {
      return _registeredUsers.firstWhere(
        (u) => u.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// الحصول على جميع المستخدمين المسجلين
  List<UserModel> getAllUsers() {
    return List.from(_registeredUsers);
  }

  /// البحث في المستخدمين
  List<UserModel> searchUsers(String query) {
    if (query.trim().isEmpty) return getAllUsers();

    final lowerQuery = query.toLowerCase();
    return _registeredUsers.where((user) {
      return user.name.toLowerCase().contains(lowerQuery) ||
             user.phoneNumber.contains(query) ||
             user.city.toLowerCase().contains(lowerQuery) ||
             user.country.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// إضافة مستخدم جديد (عند التسجيل)
  Future<bool> addUser(UserModel user) async {
    try {
      // التحقق من عدم وجود المستخدم مسبقاً
      final existingUser = await getUserByPhone(user.phoneNumber);
      if (existingUser != null) {
        return false; // المستخدم موجود مسبقاً
      }

      _registeredUsers.add(user);
      _usersCache[user.phoneNumber] = user;
      await _saveRegisteredUsers();
      notifyListeners();

      debugPrint('👤 User added: ${user.name} (${user.phoneNumber})');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding user: $e');
      return false;
    }
  }

  /// تحديث بيانات مستخدم
  Future<bool> updateUser(UserModel updatedUser) async {
    try {
      final index = _registeredUsers.indexWhere(
        (u) => u.phoneNumber == updatedUser.phoneNumber,
      );

      if (index == -1) return false;

      _registeredUsers[index] = updatedUser;
      _usersCache[updatedUser.phoneNumber] = updatedUser;
      await _saveRegisteredUsers();
      notifyListeners();

      debugPrint('👤 User updated: ${updatedUser.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating user: $e');
      return false;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(String phoneNumber) async {
    try {
      _registeredUsers.removeWhere((u) => u.phoneNumber == phoneNumber);
      _usersCache.remove(phoneNumber);
      await _saveRegisteredUsers();
      notifyListeners();

      debugPrint('👤 User deleted: $phoneNumber');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting user: $e');
      return false;
    }
  }

  /// تحديث حالة الاتصال
  Future<void> updateUserOnlineStatus(String phoneNumber, bool isOnline) async {
    final user = await getUserByPhone(phoneNumber);
    if (user != null) {
      final updatedUser = user.copyWith(
        isOnline: isOnline,
        lastSeen: isOnline ? null : DateTime.now(),
      );
      await updateUser(updatedUser);
    }
  }

  /// الحصول على المستخدمين المتصلين
  List<UserModel> getOnlineUsers() {
    return _registeredUsers.where((u) => u.isOnline).toList();
  }

  /// الحصول على المستخدمين حسب البلد
  List<UserModel> getUsersByCountry(String country) {
    return _registeredUsers.where((u) =>
        u.country.toLowerCase() == country.toLowerCase()).toList();
  }

  /// الحصول على المستخدمين حسب المدينة
  List<UserModel> getUsersByCity(String city) {
    return _registeredUsers.where((u) =>
        u.city.toLowerCase() == city.toLowerCase()).toList();
  }

  /// الحصول على المستخدمين حسب الجنس
  List<UserModel> getUsersByGender(Gender gender) {
    return _registeredUsers.where((u) => u.gender == gender).toList();
  }

  /// الحصول على المستخدمين حسب الفئة العمرية
  List<UserModel> getUsersByAgeRange(int minAge, int maxAge) {
    return _registeredUsers.where((u) =>
        u.age >= minAge && u.age <= maxAge).toList();
  }

  /// إنشاء UserModel من بيانات المنشور
  UserModel createUserFromPost(String authorId, String authorName) {
    return UserModel(
      phoneNumber: authorId,
      name: authorName,
      gender: Gender.male, // افتراضي
      age: 25, // افتراضي
      country: 'Unknown',
      city: 'Unknown',
      isOnline: false,
      lastSeen: DateTime.now(),
      registrationDate: DateTime.now(),
    );
  }

  /// التحقق من صحة بيانات المستخدم
  bool validateUser(UserModel user) {
    return user.phoneNumber.isNotEmpty &&
           user.name.isNotEmpty &&
           user.age > 0 &&
           user.country.isNotEmpty &&
           user.city.isNotEmpty;
  }

  /// تحميل المستخدمين المسجلين
  Future<void> _loadRegisteredUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString('registered_users');

      if (usersJson != null) {
        final usersList = json.decode(usersJson) as List;
        _registeredUsers.clear();
        _usersCache.clear();

        for (final userMap in usersList) {
          final user = UserModel.fromMap(userMap);
          _registeredUsers.add(user);
          _usersCache[user.phoneNumber] = user;
        }
      }

      debugPrint('👥 Loaded ${_registeredUsers.length} registered users');
    } catch (e) {
      debugPrint('❌ Error loading registered users: $e');
    }
  }

  /// حفظ المستخدمين المسجلين
  Future<void> _saveRegisteredUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersList = _registeredUsers.map((u) => u.toMap()).toList();
      final usersJson = json.encode(usersList);
      await prefs.setString('registered_users', usersJson);
    } catch (e) {
      debugPrint('❌ Error saving registered users: $e');
    }
  }

  /// إحصائيات المستخدمين
  Map<String, dynamic> getUsersStats() {
    final stats = <String, dynamic>{};

    stats['total'] = _registeredUsers.length;
    stats['online'] = getOnlineUsers().length;
    stats['offline'] = _registeredUsers.length - getOnlineUsers().length;

    // إحصائيات الجنس
    stats['male'] = getUsersByGender(Gender.male).length;
    stats['female'] = getUsersByGender(Gender.female).length;

    // إحصائيات الفئات العمرية
    stats['teens'] = getUsersByAgeRange(13, 19).length;
    stats['twenties'] = getUsersByAgeRange(20, 29).length;
    stats['thirties'] = getUsersByAgeRange(30, 39).length;
    stats['older'] = getUsersByAgeRange(40, 100).length;

    // أكثر البلدان
    final countryStats = <String, int>{};
    for (final user in _registeredUsers) {
      countryStats[user.country] = (countryStats[user.country] ?? 0) + 1;
    }
    stats['countries'] = countryStats;

    // أكثر المدن
    final cityStats = <String, int>{};
    for (final user in _registeredUsers) {
      cityStats[user.city] = (cityStats[user.city] ?? 0) + 1;
    }
    stats['cities'] = cityStats;

    return stats;
  }

  /// تصدير بيانات المستخدمين
  String exportUsersData() {
    final exportData = {
      'timestamp': DateTime.now().toIso8601String(),
      'total_users': _registeredUsers.length,
      'users': _registeredUsers.map((u) => u.toMap()).toList(),
      'stats': getUsersStats(),
    };

    return json.encode(exportData);
  }

  /// استيراد بيانات المستخدمين
  Future<bool> importUsersData(String jsonData) async {
    try {
      final data = json.decode(jsonData);
      final usersList = data['users'] as List;

      _registeredUsers.clear();
      _usersCache.clear();

      for (final userMap in usersList) {
        final user = UserModel.fromMap(userMap);
        _registeredUsers.add(user);
        _usersCache[user.phoneNumber] = user;
      }

      await _saveRegisteredUsers();
      notifyListeners();

      debugPrint('👥 Imported ${_registeredUsers.length} users');
      return true;
    } catch (e) {
      debugPrint('❌ Error importing users data: $e');
      return false;
    }
  }

  // Getters
  int get totalUsers => _registeredUsers.length;
  int get onlineUsers => getOnlineUsers().length;
  List<UserModel> get allUsers => List.from(_registeredUsers);
}
