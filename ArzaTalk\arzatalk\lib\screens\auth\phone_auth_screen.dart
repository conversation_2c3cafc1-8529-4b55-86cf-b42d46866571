import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import 'verification_code_screen.dart';

class PhoneAuthScreen extends StatefulWidget {
  const PhoneAuthScreen({super.key});

  @override
  State<PhoneAuthScreen> createState() => _PhoneAuthScreenState();
}

class _PhoneAuthScreenState extends State<PhoneAuthScreen> {
  final _phoneController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);

      // إرسال رمز التحقق الحقيقي عبر Firebase Auth
      final success = await authService.sendVerificationCode(_phoneController.text.trim());

      if (success && mounted) {
        // الانتقال إلى شاشة إدخال رمز التحقق
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => VerificationCodeScreen(
              phoneNumber: _phoneController.text.trim(),
            ),
          ),
        );
      } else {
        // عرض رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to send verification code. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFD32F2F),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const SizedBox(height: 60),

              // شعار التطبيق
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(60),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.chat,
                  size: 60,
                  color: Color(0xFFD32F2F),
                ),
              ),

              const SizedBox(height: 30),

              // عنوان التطبيق
              const Text(
                'ArzaTalk',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 10),

              const Text(
                'Simple Chat Application',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),

              const SizedBox(height: 60),

              // نموذج تسجيل الدخول
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const Text(
                        'Sign In',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFFD32F2F),
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 30),

                      // Phone number field
                      TextFormField(
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        textAlign: TextAlign.left,
                        decoration: InputDecoration(
                          labelText: 'Phone Number',
                          prefixIcon: const Icon(Icons.phone),
                          hintText: '+1234567890',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: const BorderSide(
                              color: Color(0xFFD32F2F),
                              width: 2,
                            ),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter your phone number';
                          }

                          // تنظيف رقم الهاتف
                          String cleanedPhone = value.replaceAll(RegExp(r'[^\d+]'), '');

                          // التحقق من وجود رمز الدولة
                          if (!cleanedPhone.startsWith('+')) {
                            return 'Phone number must start with country code (e.g., +212)';
                          }

                          // إزالة رمز + للتحقق من الطول
                          String numbersOnly = cleanedPhone.substring(1);

                          // التحقق من الطول (يجب أن يكون بين 10-15 رقم)
                          if (numbersOnly.length < 10 || numbersOnly.length > 15) {
                            return 'Invalid phone number length';
                          }

                          // التحقق من أن جميع الأحرف أرقام
                          if (!RegExp(r'^\d+$').hasMatch(numbersOnly)) {
                            return 'Phone number can only contain digits';
                          }

                          // التحقق من رموز الدول المقبولة
                          List<String> validCountryCodes = [
                            '212', // المغرب
                            '966', // السعودية
                            '971', // الإمارات
                            '965', // الكويت
                            '974', // قطر
                            '973', // البحرين
                            '968', // عمان
                            '962', // الأردن
                            '961', // لبنان
                            '963', // سوريا
                            '20',  // مصر
                            '213', // الجزائر
                            '216', // تونس
                            '218', // ليبيا
                            '249', // السودان
                            '967', // اليمن
                            '964', // العراق
                            '98',  // إيران
                            '90',  // تركيا
                            '1',   // أمريكا/كندا
                            '44',  // بريطانيا
                            '33',  // فرنسا
                            '49',  // ألمانيا
                          ];

                          bool validCountryCode = false;
                          for (String code in validCountryCodes) {
                            if (numbersOnly.startsWith(code)) {
                              validCountryCode = true;
                              break;
                            }
                          }

                          if (!validCountryCode) {
                            return 'Unsupported country code';
                          }

                          return null;
                        },
                      ),

                      const SizedBox(height: 30),

                      // زر تسجيل الدخول
                      ElevatedButton(
                        onPressed: _isLoading ? null : _login,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFD32F2F),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text(
                                'Sign In',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),

                      const SizedBox(height: 20),

                      // Note
                      Text(
                        'Enter your phone number to sign in.\nNew users will be redirected to registration.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 10),

                      // Demo users info
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Demo Users (for testing):',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[800],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '• +212638813823 (محمد)\n• +212771878802 (سعيد)\n• +212612345678 (معاد)',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.blue[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
