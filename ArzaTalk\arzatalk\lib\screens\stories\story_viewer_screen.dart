import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../../models/story_model.dart';
import '../../services/story_service.dart';
import '../../services/auth_service.dart';

/// شاشة عرض القصص
class StoryViewerScreen extends StatefulWidget {
  final UserStoriesModel userStories;
  final int initialIndex;

  const StoryViewerScreen({
    super.key,
    required this.userStories,
    this.initialIndex = 0,
  });

  @override
  State<StoryViewerScreen> createState() => _StoryViewerScreenState();
}

class _StoryViewerScreenState extends State<StoryViewerScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _progressController;
  Timer? _storyTimer;
  
  int _currentIndex = 0;
  bool _isPaused = false;
  
  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
    _progressController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );
    
    _startStoryTimer();
    _markCurrentStoryAsViewed();
  }

  @override
  void dispose() {
    _storyTimer?.cancel();
    _progressController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  /// بدء مؤقت القصة
  void _startStoryTimer() {
    _progressController.reset();
    _progressController.forward();
    
    _storyTimer?.cancel();
    _storyTimer = Timer(const Duration(seconds: 5), () {
      _nextStory();
    });
  }

  /// إيقاف مؤقت القصة
  void _pauseStoryTimer() {
    _isPaused = true;
    _storyTimer?.cancel();
    _progressController.stop();
  }

  /// استئناف مؤقت القصة
  void _resumeStoryTimer() {
    if (_isPaused) {
      _isPaused = false;
      _progressController.forward();
      
      final remainingTime = Duration(
        milliseconds: ((1 - _progressController.value) * 5000).round(),
      );
      
      _storyTimer = Timer(remainingTime, () {
        _nextStory();
      });
    }
  }

  /// الانتقال للقصة التالية
  void _nextStory() {
    if (_currentIndex < widget.userStories.stories.length - 1) {
      setState(() {
        _currentIndex++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _startStoryTimer();
      _markCurrentStoryAsViewed();
    } else {
      Navigator.of(context).pop();
    }
  }

  /// الانتقال للقصة السابقة
  void _previousStory() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _startStoryTimer();
    }
  }

  /// تحديد القصة الحالية كمشاهدة
  void _markCurrentStoryAsViewed() {
    final currentStory = widget.userStories.stories[_currentIndex];
    final authService = Provider.of<AuthService>(context, listen: false);
    final storyService = Provider.of<StoryService>(context, listen: false);
    
    if (authService.currentUser != null) {
      storyService.markStoryAsViewed(
        currentStory.id,
        authService.currentUser!.phoneNumber,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentStory = widget.userStories.stories[_currentIndex];
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTapDown: (details) {
          _pauseStoryTimer();
        },
        onTapUp: (details) {
          _resumeStoryTimer();
          
          // تحديد الجانب المضغوط
          final screenWidth = MediaQuery.of(context).size.width;
          if (details.globalPosition.dx < screenWidth / 2) {
            _previousStory();
          } else {
            _nextStory();
          }
        },
        onTapCancel: () {
          _resumeStoryTimer();
        },
        child: Stack(
          children: [
            // محتوى القصة
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
                _startStoryTimer();
                _markCurrentStoryAsViewed();
              },
              itemCount: widget.userStories.stories.length,
              itemBuilder: (context, index) {
                final story = widget.userStories.stories[index];
                return _buildStoryContent(story);
              },
            ),
            
            // شريط التقدم
            Positioned(
              top: MediaQuery.of(context).padding.top + 10,
              left: 10,
              right: 10,
              child: _buildProgressBar(),
            ),
            
            // معلومات المستخدم
            Positioned(
              top: MediaQuery.of(context).padding.top + 50,
              left: 16,
              right: 16,
              child: _buildUserInfo(),
            ),
            
            // زر الإغلاق
            Positioned(
              top: MediaQuery.of(context).padding.top + 10,
              right: 16,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 28,
                ),
              ),
            ),
            
            // معلومات القصة (الوقت والمشاهدات)
            Positioned(
              bottom: 50,
              left: 16,
              right: 16,
              child: _buildStoryInfo(currentStory),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء محتوى القصة
  Widget _buildStoryContent(StoryModel story) {
    switch (story.type) {
      case StoryType.text:
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: story.backgroundColor ?? const Color(0xFFD32F2F),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Text(
                story.content ?? '',
                style: TextStyle(
                  color: story.textColor ?? Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.w500,
                  fontFamily: story.fontFamily ?? 'Roboto',
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
        
      case StoryType.image:
        return SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: story.mediaUrl != null
              ? Image.network(
                  story.mediaUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[800],
                      child: const Center(
                        child: Icon(
                          Icons.broken_image,
                          color: Colors.white,
                          size: 64,
                        ),
                      ),
                    );
                  },
                )
              : Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.image,
                      color: Colors.white,
                      size: 64,
                    ),
                  ),
                ),
        );
        
      case StoryType.video:
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.grey[800],
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.play_circle_outline,
                  color: Colors.white,
                  size: 80,
                ),
                SizedBox(height: 16),
                Text(
                  'Video Story',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ),
        );
    }
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar() {
    return Row(
      children: List.generate(
        widget.userStories.stories.length,
        (index) => Expanded(
          child: Container(
            height: 3,
            margin: EdgeInsets.only(
              right: index < widget.userStories.stories.length - 1 ? 4 : 0,
            ),
            child: LinearProgressIndicator(
              value: index < _currentIndex
                  ? 1.0
                  : index == _currentIndex
                      ? _progressController.value
                      : 0.0,
              backgroundColor: Colors.white.withOpacity(0.3),
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء معلومات المستخدم
  Widget _buildUserInfo() {
    return Row(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundImage: widget.userStories.userProfileImage != null
              ? NetworkImage(widget.userStories.userProfileImage!)
              : null,
          backgroundColor: Colors.grey[300],
          child: widget.userStories.userProfileImage == null
              ? Text(
                  widget.userStories.userName.isNotEmpty
                      ? widget.userStories.userName[0].toUpperCase()
                      : '?',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                )
              : null,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.userStories.userName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                _getTimeAgo(widget.userStories.stories[_currentIndex].createdAt),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء معلومات القصة
  Widget _buildStoryInfo(StoryModel story) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${story.viewCount} views',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
          Text(
            'Expires in ${story.timeRemainingText}',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على النص الزمني
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
