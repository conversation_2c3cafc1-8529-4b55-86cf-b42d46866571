import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/note_model.dart';
import '../services/notes_service.dart';
import '../services/auth_service.dart';

/// ويدجت عرض الملاحظة فوق الملف الشخصي (مثل Facebook)
class UserNoteWidget extends StatelessWidget {
  final NoteModel note;
  final VoidCallback? onTap;
  final bool showViewers;

  const UserNoteWidget({
    super.key,
    required this.note,
    this.onTap,
    this.showViewers = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => _showNoteDetails(context),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Column(
          children: [
            // صورة المستخدم مع حدود الملاحظة
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [
                    note.backgroundColor,
                    note.backgroundColor.withValues(alpha: 0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: note.backgroundColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Container(
                margin: const EdgeInsets.all(3),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Container(
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[300],
                  ),
                  child: note.userProfileImage != null
                      ? ClipOval(
                          child: Image.network(
                            note.userProfileImage!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                _buildDefaultAvatar(),
                          ),
                        )
                      : _buildDefaultAvatar(),
                ),
              ),
            ),
            
            const SizedBox(height: 4),
            
            // اسم المستخدم
            SizedBox(
              width: 70,
              child: Text(
                note.userName,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1C1E21),
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            // عدد المشاهدين (اختياري)
            if (showViewers && note.viewersCount > 0) ...[
              const SizedBox(height: 2),
              Text(
                '${note.viewersCount} views',
                style: const TextStyle(
                  fontSize: 10,
                  color: Color(0xFF65676B),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء الصورة الافتراضية
  Widget _buildDefaultAvatar() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: note.backgroundColor.withValues(alpha: 0.1),
      ),
      child: Icon(
        Icons.person,
        color: note.backgroundColor,
        size: 30,
      ),
    );
  }

  /// إظهار تفاصيل الملاحظة
  void _showNoteDetails(BuildContext context) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => _NoteDetailsDialog(note: note),
    );
  }
}

/// حوار عرض تفاصيل الملاحظة
class _NoteDetailsDialog extends StatefulWidget {
  final NoteModel note;

  const _NoteDetailsDialog({required this.note});

  @override
  State<_NoteDetailsDialog> createState() => _NoteDetailsDialogState();
}

class _NoteDetailsDialogState extends State<_NoteDetailsDialog> {
  @override
  void initState() {
    super.initState();
    _markAsViewed();
  }

  /// تسجيل المشاهدة
  void _markAsViewed() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final notesService = Provider.of<NotesService>(context, listen: false);
    final currentUser = authService.currentUser;
    
    if (currentUser != null && currentUser.phoneNumber != widget.note.userId) {
      notesService.addNoteViewer(widget.note.id, currentUser.phoneNumber);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          color: widget.note.backgroundColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            // الهيدر
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // صورة المستخدم
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.2),
                    ),
                    child: widget.note.userProfileImage != null
                        ? ClipOval(
                            child: Image.network(
                              widget.note.userProfileImage!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Icon(Icons.person, color: Colors.white),
                            ),
                          )
                        : const Icon(Icons.person, color: Colors.white),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // معلومات المستخدم
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.note.userName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          widget.note.timeAgo,
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // زر الإغلاق
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // محتوى الملاحظة
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Center(
                  child: Text(
                    widget.note.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            
            // معلومات المشاهدة
            if (widget.note.viewersCount > 0)
              Container(
                padding: const EdgeInsets.all(16),
                child: Text(
                  '${widget.note.viewersCount} people viewed this note',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
