import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/social_feed_service.dart';
import '../../services/auth_service.dart';
import '../../services/post_management_service.dart';
import '../../services/media_service.dart';
import '../../models/post_model.dart';
import '../../models/user_model.dart';
import '../../widgets/post_widget.dart';
import '../../test_firebase.dart';
import 'create_post_screen.dart';
import 'live_stream_screen.dart';

import '../../widgets/comment_widget.dart';
import '../../widgets/comment_input_widget.dart';

/// شاشة الشبكة الاجتماعية
class SocialFeedScreen extends StatefulWidget {
  const SocialFeedScreen({super.key});

  @override
  State<SocialFeedScreen> createState() => _SocialFeedScreenState();
}

class _SocialFeedScreenState extends State<SocialFeedScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;
  final Map<String, TextEditingController> _commentControllers = {};

  // متغيرات فرز التعليقات
  final Map<String, String> _commentSortTypes = {}; // postId -> sortType
  String? _replyingToCommentId; // معرف التعليق الذي يتم الرد عليه

  @override
  void initState() {
    super.initState();
    _initializeFeed();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    // تنظيف متحكمات التعليقات
    for (final controller in _commentControllers.values) {
      controller.dispose();
    }
    _commentControllers.clear();
    super.dispose();
  }

  /// تهيئة الخلاصة
  void _initializeFeed() async {
    setState(() => _isLoading = true);

    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    await socialService.initialize();

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5), // Facebook background color
      // إضافة AppBar مع زر البحث مثل Facebook
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            // شعار ArzaTalk
            const Text(
              'ArzaTalk',
              style: TextStyle(
                color: Color(0xFF1877F2),
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            // زر البحث مثل Facebook
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFFF0F2F5),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: _showSearchDialog,
                icon: const Icon(
                  Icons.search,
                  color: Color(0xFF1C1E21),
                  size: 20,
                ),
              ),
            ),
            const SizedBox(width: 8),
            // زر الإضافة (+) مثل Facebook
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFFF0F2F5),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: _showCreateOptionsMenu,
                icon: const Icon(
                  Icons.add,
                  color: Color(0xFF1C1E21),
                  size: 20,
                ),
              ),
            ),
            const SizedBox(width: 8),
            // زر القائمة الشاملة
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFFF0F2F5),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: _showAppMenu,
                icon: const Icon(
                  Icons.menu,
                  color: Color(0xFF1C1E21),
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // منطقة إنشاء منشور سريع
                _buildQuickPostArea(),

                // قائمة المنشورات
                Expanded(
                  child: Consumer2<SocialFeedService, PostManagementService>(
                    builder: (context, socialService, postManagement, child) {
                      final allPosts = socialService.getAllPosts();

                      // فلترة المنشورات حسب الإعدادات
                      final filteredPosts = postManagement.filterPosts(
                        allPosts,
                        (post) => post.id,
                        (post) => post.authorId,
                      );

                      if (filteredPosts.isEmpty) {
                        return _buildEmptyState();
                      }

                      return RefreshIndicator(
                        onRefresh: _refreshFeed,
                        backgroundColor: Colors.white,
                        color: const Color(0xFF1877F2), // Facebook blue
                        strokeWidth: 3.0,
                        displacement: 60.0, // مسافة أكبر لإظهار الأيقونة
                        child: CustomScrollView(
                          controller: _scrollController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          slivers: [
                            // Header مخصص مع أيقونة المنزل
                            SliverToBoxAdapter(
                              child: SizedBox(
                                height: 0, // مخفي افتراضياً
                                child: const Center(
                                  child: Icon(
                                    Icons.home,
                                    size: 32,
                                    color: Color(0xFF1877F2),
                                  ),
                                ),
                              ),
                            ),
                            // قائمة المنشورات
                            SliverList(
                              delegate: SliverChildBuilderDelegate(
                                (context, index) {
                                  final post = filteredPosts[index];
                                  return PostWidget(
                                    post: post,
                                    onReaction: (emoji) => _handleReaction(post.id, emoji),
                                    onComment: () => _showCommentsSheet(post),
                                    onShare: () => _handleShare(post.id),
                                    onEdit: () => _editPost(post),
                                    onDelete: () => _deletePost(post.id),
                                  );
                                },
                                childCount: filteredPosts.length,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _createNewPost(),
        backgroundColor: const Color(0xFF1877F2), // Facebook blue
        elevation: 4,
        child: const Icon(Icons.edit, color: Colors.white, size: 24),
      ),
    );
  }

  /// بناء منطقة إنشاء منشور سريع مثل Facebook
  Widget _buildQuickPostArea() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final currentUser = authService.currentUser;
        if (currentUser == null) return const SizedBox.shrink();

        return Container(
          margin: const EdgeInsets.fromLTRB(8, 8, 8, 4), // مساحة أفضل مثل Facebook
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // منطقة النص الرئيسية - محسنة مثل Facebook
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // صورة المستخدم مع تحسين
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: const Color(0xFF1877F2).withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: CircleAvatar(
                        radius: 22,
                        backgroundColor: const Color(0xFF1877F2),
                        child: Text(
                          currentUser.name.isNotEmpty ? currentUser.name[0].toUpperCase() : '?',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // شريط النص المحسن
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _createNewPost(),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF0F2F5),
                            borderRadius: BorderRadius.circular(30),
                            border: Border.all(
                              color: const Color(0xFFE4E6EA),
                              width: 1.5,
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'What\'s on your mind, ${currentUser.name.split(' ').first}?',
                                  style: const TextStyle(
                                    color: Color(0xFF65676B),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                              const Icon(
                                Icons.photo_camera,
                                color: Color(0xFF45BD62),
                                size: 20,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // خط فاصل
              Container(
                height: 1,
                color: const Color(0xFFE4E6EA),
              ),

              // أزرار الأدوات السفلية مع ملصقات احترافية مثل Facebook
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Live Video - ملصق احترافي LinkedIn-style
                    _buildLinkedInStyleButton(
                      icon: Icons.videocam,
                      label: 'Live',
                      color: const Color(0xFFE42645),
                      onTap: () => _startLiveStream(),
                    ),

                    // Photo/Video - ملصق احترافي LinkedIn-style
                    _buildLinkedInStyleButton(
                      icon: Icons.photo_camera,
                      label: 'Photo',
                      color: const Color(0xFF45BD62),
                      onTap: () => _createNewPost(withImage: true),
                    ),

                    // Video Upload - ملصق احترافي LinkedIn-style
                    _buildLinkedInStyleButton(
                      icon: Icons.video_library,
                      label: 'Video',
                      color: const Color(0xFF1877F2),
                      onTap: () => _uploadVideo(),
                    ),

                    // Feeling/Activity - ملصق احترافي LinkedIn-style
                    _buildLinkedInStyleButton(
                      icon: Icons.emoji_emotions,
                      label: 'Feeling',
                      color: const Color(0xFFF7B928),
                      onTap: () => _showFeelingSelector(),
                    ),

                    // Check-in/Location - ملصق احترافي LinkedIn-style
                    _buildLinkedInStyleButton(
                      icon: Icons.location_on,
                      label: 'Check-in',
                      color: const Color(0xFF9C27B0),
                      onTap: () => _createNewPost(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء زر LinkedIn احترافي مع ملصقات مثل التفاعلات
  Widget _buildLinkedInStyleButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // ملصق احترافي مثل LinkedIn مع خلفية دائرية
              Container(
                width: 28,
                height: 28,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: color.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Icon(
                    icon,
                    size: 16,
                    color: color,
                  ),
                ),
              ),
              const SizedBox(width: 6),
              // النص مع تحسين احترافي
              Flexible(
                child: Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر إجراء سريع (احتياطي)
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: const TextStyle(
                  color: Color(0xFF65676B),
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.feed_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No posts yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Be the first to share something!',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _createNewPost(),
            icon: const Icon(Icons.add),
            label: const Text('Create Post'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD32F2F),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إنشاء منشور جديد
  void _createNewPost({
    bool withImage = false,
    bool withVideo = false,
    bool withFeeling = false,
    String? selectedFeeling,
  }) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreatePostScreen(
          withImage: withImage,
          withVideo: withVideo,
          withFeeling: withFeeling,
          selectedFeeling: selectedFeeling,
        ),
      ),
    );
  }

  /// بدء البث المباشر الحقيقي
  void _startLiveStream() async {
    try {
      // إظهار تأكيد البث المباشر
      final shouldStart = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.live_tv, color: Color(0xFFE42645)),
              SizedBox(width: 8),
              Text('Start Live Stream'),
            ],
          ),
          content: const Text(
            'Are you ready to go live? Your followers will be notified.',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE42645),
                foregroundColor: Colors.white,
              ),
              child: const Text('Go Live'),
            ),
          ],
        ),
      );

      if (shouldStart == true && mounted) {
        // فتح الكاميرا للبث المباشر
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const LiveStreamScreen(),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error starting live stream: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ Failed to start live stream'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض محدد المشاعر مثل Facebook
  void _showFeelingSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE4E6EA)),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                  const Expanded(
                    child: Text(
                      'How are you feeling?',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 48), // للتوازن
                ],
              ),
            ),

            // قائمة المشاعر
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: _buildFeelingsList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة المشاعر مثل Facebook
  List<Widget> _buildFeelingsList() {
    final feelings = [
      {'emoji': '😊', 'text': 'happy', 'color': const Color(0xFFFFC107)},
      {'emoji': '😢', 'text': 'sad', 'color': const Color(0xFF607D8B)},
      {'emoji': '😡', 'text': 'angry', 'color': const Color(0xFFFF5722)},
      {'emoji': '😍', 'text': 'loved', 'color': const Color(0xFFE91E63)},
      {'emoji': '😂', 'text': 'amused', 'color': const Color(0xFFFFC107)},
      {'emoji': '😮', 'text': 'surprised', 'color': const Color(0xFF9C27B0)},
      {'emoji': '😴', 'text': 'sleepy', 'color': const Color(0xFF607D8B)},
      {'emoji': '🤔', 'text': 'thoughtful', 'color': const Color(0xFF795548)},
      {'emoji': '😎', 'text': 'cool', 'color': const Color(0xFF1877F2)},
      {'emoji': '🥳', 'text': 'excited', 'color': const Color(0xFFFF9800)},
      {'emoji': '😌', 'text': 'blessed', 'color': const Color(0xFF4CAF50)},
      {'emoji': '😤', 'text': 'frustrated', 'color': const Color(0xFFFF5722)},
      {'emoji': '🤗', 'text': 'grateful', 'color': const Color(0xFF4CAF50)},
      {'emoji': '😇', 'text': 'peaceful', 'color': const Color(0xFF03DAC6)},
      {'emoji': '🤯', 'text': 'mind blown', 'color': const Color(0xFF9C27B0)},
      {'emoji': '😋', 'text': 'hungry', 'color': const Color(0xFFFF9800)},
    ];

    return feelings.map((feeling) {
      return ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: (feeling['color'] as Color).withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              feeling['emoji'] as String,
              style: const TextStyle(fontSize: 20),
            ),
          ),
        ),
        title: Text(
          'feeling ${feeling['text']}',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        onTap: () {
          Navigator.of(context).pop();
          _createNewPost(
            withFeeling: true,
            selectedFeeling: '${feeling['emoji']} feeling ${feeling['text']}',
          );
        },
      );
    }).toList();
  }

  /// رفع فيديو مباشرة مع شريط تقدم
  void _uploadVideo() async {
    try {
      final mediaService = MediaService();
      final videoPath = await mediaService.pickVideo();

      if (videoPath != null && mounted) {
        // إظهار شريط التقدم
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD32F2F)),
                ),
                const SizedBox(height: 16),
                const Text('🎥 Uploading video...'),
                const SizedBox(height: 8),
                Text(
                  'Please wait while we upload your video to Firebase',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );

        final authService = Provider.of<AuthService>(context, listen: false);
        final socialService = Provider.of<SocialFeedService>(context, listen: false);
        final currentUser = authService.currentUser;

        if (currentUser != null) {
          // إنشاء منشور فيديو مباشرة
          final success = await socialService.createPostAdvanced(
            authorId: currentUser.phoneNumber,
            authorName: currentUser.name,
            content: '🎥 Shared a video',
            videos: [videoPath],
            type: PostType.video,
          );

          // إغلاق شريط التقدم
          if (mounted) Navigator.of(context).pop();

          if (success && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('🎥 Video uploaded successfully!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Failed to upload video'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } else {
          // إغلاق شريط التقدم
          if (mounted) Navigator.of(context).pop();
        }
      }
    } catch (e) {
      debugPrint('❌ Error uploading video: $e');
      // إغلاق شريط التقدم في حالة الخطأ
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ Error uploading video'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// التعامل مع التفاعل
  void _handleReaction(String postId, String emoji) {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      socialService.addPostReaction(
        postId: postId,
        emoji: emoji,
        userId: currentUser.phoneNumber,
      );
    }
  }

  /// عرض ورقة التعليقات
  void _showCommentsSheet(PostModel post) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.7,
          maxChildSize: 0.9,
          minChildSize: 0.5,
          builder: (context, scrollController) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: Column(
              children: [
                // مقبض السحب
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // عنوان مع فرز التعليقات
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
                  ),
                  child: Column(
                    children: [
                      // العنوان الرئيسي
                      Row(
                        children: [
                          const Icon(Icons.comment, color: Color(0xFFD32F2F)),
                          const SizedBox(width: 8),
                          Text(
                            'Comments (${post.commentsCount})',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // فرز التعليقات مثل Facebook
                      _buildCommentSortOptions(post.id),
                    ],
                  ),
                ),

                // قائمة التعليقات
                Expanded(
                  child: Consumer<SocialFeedService>(
                    builder: (context, socialService, child) {
                      final sortType = _commentSortTypes[post.id] ?? 'newest';
                      final comments = socialService.getPostComments(post.id, sortBy: sortType);

                      if (comments.isEmpty) {
                        return const Center(
                          child: Text(
                            'No comments yet.\nBe the first to comment!',
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.grey),
                          ),
                        );
                      }

                      return ListView.builder(
                        controller: scrollController,
                        itemCount: comments.length,
                        itemBuilder: (context, index) {
                          final comment = comments[index];
                          return CommentWidget(
                            comment: comment,
                            postId: post.id,
                            onReaction: (emoji) {
                              // التفاعل مع التعليق
                              final authService = Provider.of<AuthService>(context, listen: false);
                              final currentUser = authService.currentUser;
                              if (currentUser != null) {
                                socialService.addCommentReaction(
                                  commentId: comment.id,
                                  emoji: emoji,
                                  userId: currentUser.phoneNumber,
                                );
                              }
                            },
                            onReply: (commentId) => _showReplyDialog(post.id, commentId),
                            onEdit: (commentId) => _showEditCommentDialog(commentId),
                            onDelete: (commentId) => _deleteComment(commentId),
                            onBlock: (userId) => _blockUser(userId),
                            onReport: (commentId) => _reportComment(commentId),
                          );
                        },
                      );
                    },
                  ),
                ),

                // منطقة إضافة تعليق مع الوسائط
                CommentInputWidget(
                  postId: post.id,
                  onCommentAdded: () {
                    // تحديث التعليقات عند إضافة تعليق جديد
                    setState(() {});
                  },
                ),

                // منطقة إضافة رد (إذا كان هناك تعليق محدد للرد عليه)
                if (_replyingToCommentId != null) ...[
                  Container(
                    color: const Color(0xFFF0F2F5),
                    padding: const EdgeInsets.all(8),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.reply,
                          size: 16,
                          color: Color(0xFF1877F2),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Replying to comment...',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF65676B),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _replyingToCommentId = null;
                            });
                          },
                          icon: const Icon(
                            Icons.close,
                            size: 16,
                            color: Color(0xFF65676B),
                          ),
                        ),
                      ],
                    ),
                  ),
                  CommentInputWidget(
                    postId: post.id,
                    parentCommentId: _replyingToCommentId,
                    hintText: 'Write a reply...',
                    onCommentAdded: () {
                      setState(() {
                        _replyingToCommentId = null;
                      });
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء حقل إدخال التعليق
  Widget _buildCommentInput(String postId) {
    // الحصول على أو إنشاء متحكم للمنشور
    if (!_commentControllers.containsKey(postId)) {
      _commentControllers[postId] = TextEditingController();
    }
    final commentController = _commentControllers[postId]!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: commentController,
              decoration: InputDecoration(
                hintText: 'Write a comment...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              maxLines: null,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () => _addComment(postId, commentController),
            icon: const Icon(Icons.send, color: Color(0xFFD32F2F)),
          ),
        ],
      ),
    );
  }

  /// إضافة تعليق
  Future<void> _addComment(String postId, TextEditingController controller) async {
    if (controller.text.trim().isEmpty) return;

    debugPrint('🔥 DEBUG: Adding comment to post $postId');
    debugPrint('🔥 DEBUG: Comment content: "${controller.text.trim()}"');

    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    debugPrint('🔥 DEBUG: Current user: ${currentUser?.name}');

    if (currentUser != null) {
      debugPrint('🔥 DEBUG: Calling socialService.addComment...');
      final success = await socialService.addComment(
        postId: postId,
        authorId: currentUser.phoneNumber,
        authorName: currentUser.name,
        content: controller.text.trim(),
        authorAvatar: currentUser.profileImageUrl,
      );

      debugPrint('🔥 DEBUG: addComment result: $success');

      if (mounted) {
        if (success) {
          controller.clear();
          debugPrint('🔥 DEBUG: Comment added successfully!');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Comment added!'),
              duration: Duration(seconds: 1),
            ),
          );
        } else {
          debugPrint('🔥 DEBUG: Failed to add comment!');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to add comment'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else {
      debugPrint('🔥 DEBUG: No current user found!');
    }
  }

  /// إظهار حوار الرد على التعليق
  void _showReplyDialog(String postId, String parentCommentId) {
    final replyController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reply to Comment'),
        content: TextField(
          controller: replyController,
          decoration: const InputDecoration(
            hintText: 'Write your reply...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (replyController.text.trim().isNotEmpty) {
                Navigator.pop(context);
                await _addReply(postId, parentCommentId, replyController.text.trim());
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1877F2),
              foregroundColor: Colors.white,
            ),
            child: const Text('Reply'),
          ),
        ],
      ),
    );
  }

  /// إضافة رد على تعليق
  Future<void> _addReply(String postId, String parentCommentId, String content) async {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      final success = await socialService.addComment(
        postId: postId,
        authorId: currentUser.phoneNumber,
        authorName: currentUser.name,
        content: content,
        authorAvatar: currentUser.profileImageUrl,
        parentCommentId: parentCommentId,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reply added!'),
              backgroundColor: Color(0xFF42B883),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to add reply'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// إظهار حوار تعديل التعليق
  void _showEditCommentDialog(String commentId) {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final comment = socialService.getCommentById(commentId);

    if (comment == null) return;

    final editController = TextEditingController(text: comment.content);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Comment'),
        content: TextField(
          controller: editController,
          decoration: const InputDecoration(
            hintText: 'Edit your comment...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (editController.text.trim().isNotEmpty) {
                Navigator.pop(context);
                await _editComment(commentId, editController.text.trim());
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1877F2),
              foregroundColor: Colors.white,
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  /// تعديل تعليق
  Future<void> _editComment(String commentId, String newContent) async {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);

    final success = await socialService.editComment(commentId, newContent);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment updated!'),
            backgroundColor: Color(0xFF42B883),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to update comment'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// حذف تعليق
  Future<void> _deleteComment(String commentId) async {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);

    final success = await socialService.deleteComment(commentId);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Comment deleted!'),
            backgroundColor: Color(0xFF42B883),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to delete comment'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// حظر مستخدم
  void _blockUser(String userId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: const Text('Are you sure you want to block this user? You won\'t see their posts or comments anymore.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('User blocked successfully'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Block'),
          ),
        ],
      ),
    );
  }

  /// الإبلاغ عن تعليق
  void _reportComment(String commentId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Comment'),
        content: const Text('Thank you for helping keep our community safe. We\'ll review this comment.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Comment reported. Thank you!'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Report'),
          ),
        ],
      ),
    );
  }

  /// التعامل مع المشاركة
  void _handleShare(String postId) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Share Post',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.link, color: Color(0xFFD32F2F)),
              title: const Text('Copy Link'),
              subtitle: const Text('Copy post link to clipboard'),
              onTap: () {
                Navigator.of(context).pop();
                _copyPostLink(postId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.message, color: Color(0xFFD32F2F)),
              title: const Text('Send Message'),
              subtitle: const Text('Share via ArzaTalk message'),
              onTap: () {
                Navigator.of(context).pop();
                _shareViaMessage(postId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share, color: Color(0xFFD32F2F)),
              title: const Text('Share to Feed'),
              subtitle: const Text('Repost to your timeline'),
              onTap: () {
                Navigator.of(context).pop();
                _shareToFeed(postId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.report, color: Colors.orange),
              title: const Text('Report Post'),
              subtitle: const Text('Report inappropriate content'),
              onTap: () {
                Navigator.of(context).pop();
                _reportPost(postId);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// نسخ رابط المنشور
  void _copyPostLink(String postId) {
    final postLink = 'https://arzatalk.com/post/$postId';
    // في التطبيق الحقيقي، استخدم Clipboard.setData
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Post link copied: $postLink'),
        action: SnackBarAction(
          label: 'OK',
          onPressed: () {},
        ),
      ),
    );
  }

  /// مشاركة عبر رسالة
  void _shareViaMessage(String postId) {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final post = socialService.getPostById(postId);

    if (post != null) {
      // في التطبيق الحقيقي، افتح شاشة اختيار جهة الاتصال
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Sharing: "${post.content.substring(0, post.content.length > 30 ? 30 : post.content.length)}..."'),
          action: SnackBarAction(
            label: 'Send',
            onPressed: () {
              // تنفيذ الإرسال
            },
          ),
        ),
      );
    }
  }

  /// مشاركة في الخلاصة
  void _shareToFeed(String postId) {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      socialService.sharePost(
        postId: postId,
        userId: currentUser.phoneNumber,
      ).then((success) {
        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Post shared to your timeline!')),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to share post'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      });
    }
  }

  /// الإبلاغ عن منشور
  void _reportPost(String postId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Post'),
        content: const Text('Why are you reporting this post?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Post reported. Thank you!')),
              );
            },
            child: const Text('Report', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// تعديل منشور
  void _editPost(PostModel post) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreatePostScreen(editPost: post),
      ),
    );
  }

  /// حذف منشور
  void _deletePost(String postId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Post'),
        content: const Text('Are you sure you want to delete this post?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              final socialService = Provider.of<SocialFeedService>(context, listen: false);
              socialService.deletePost(postId);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    final searchController = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // شريط البحث
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: searchController,
                        decoration: InputDecoration(
                          hintText: 'Search posts, people, hashtags...',
                          prefixIcon: const Icon(Icons.search, color: Color(0xFFD32F2F)),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: const BorderSide(color: Color(0xFFD32F2F)),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: const BorderSide(color: Color(0xFFD32F2F), width: 2),
                          ),
                        ),
                        onChanged: (query) => _performSearch(query, scrollController),
                        onSubmitted: (query) => _performSearch(query, scrollController),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              // نتائج البحث
              Expanded(
                child: Consumer2<SocialFeedService, AuthService>(
                  builder: (context, socialService, authService, child) {
                    if (searchController.text.trim().isEmpty) {
                      return _buildSearchSuggestions();
                    }

                    final query = searchController.text.trim();
                    final currentUser = authService.currentUser;

                    return FutureBuilder<List<UserModel>>(
                      future: AuthService.getAllUsersStatic(),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.waiting) {
                          return const Center(child: CircularProgressIndicator());
                        }

                        final allUsers = snapshot.data ?? [];
                        final userResults = allUsers.where((user) =>
                          user.phoneNumber != currentUser?.phoneNumber &&
                          (user.name.toLowerCase().contains(query.toLowerCase()) ||
                           user.city.toLowerCase().contains(query.toLowerCase()) ||
                           user.country.toLowerCase().contains(query.toLowerCase()))
                        ).toList();

                        final postResults = socialService.searchPosts(query);

                        if (userResults.isEmpty && postResults.isEmpty) {
                          return const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.search_off, size: 60, color: Colors.grey),
                                SizedBox(height: 16),
                                Text(
                                  'No results found',
                                  style: TextStyle(fontSize: 16, color: Colors.grey),
                                ),
                              ],
                            ),
                          );
                        }

                        return ListView(
                          controller: scrollController,
                          children: [
                            // نتائج المستخدمين
                            if (userResults.isNotEmpty) ...[
                              Padding(
                                padding: const EdgeInsets.all(16),
                                child: Row(
                                  children: [
                                    const Icon(Icons.people, color: Color(0xFF1877F2), size: 20),
                                    const SizedBox(width: 8),
                                    Text(
                                      'People (${userResults.length})',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF1C1E21),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ...userResults.map((user) => Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                child: _buildFriendSuggestion(user),
                              )),
                              const SizedBox(height: 16),
                            ],

                            // نتائج المنشورات
                            if (postResults.isNotEmpty) ...[
                              Padding(
                                padding: const EdgeInsets.all(16),
                                child: Row(
                                  children: [
                                    const Icon(Icons.article, color: Color(0xFF1877F2), size: 20),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Posts (${postResults.length})',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF1C1E21),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              ...postResults.map((post) => PostWidget(
                                post: post,
                                onReaction: (emoji) => _handleReaction(post.id, emoji),
                                onComment: () => _showCommentsSheet(post),
                                onShare: () => _handleShare(post.id),
                                onEdit: () => _editPost(post),
                                onDelete: () => _deletePost(post.id),
                              )),
                            ],
                          ],
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء اقتراحات البحث مثل Facebook
  Widget _buildSearchSuggestions() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people, size: 60, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Search for people and posts',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// بناء اقتراح صديق مثل Facebook
  Widget _buildFriendSuggestion(UserModel user) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE4E6EA)),
      ),
      child: Row(
        children: [
          // صورة المستخدم
          CircleAvatar(
            radius: 25,
            backgroundColor: const Color(0xFF1877F2),
            backgroundImage: user.profileImageUrl != null
                ? NetworkImage(user.profileImageUrl!)
                : null,
            child: user.profileImageUrl == null
                ? Text(
                    user.name.isNotEmpty ? user.name[0].toUpperCase() : '?',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 12),

          // معلومات المستخدم
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1C1E21),
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 14, color: Color(0xFF65676B)),
                    const SizedBox(width: 4),
                    Text(
                      '${user.city}, ${user.country}',
                      style: const TextStyle(
                        fontSize: 13,
                        color: Color(0xFF65676B),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // أزرار الإجراءات
          Column(
            children: [
              // زر المراسلة
              SizedBox(
                width: 80,
                height: 32,
                child: ElevatedButton(
                  onPressed: () => _startChatWithUser(user),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1877F2),
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  child: const Text(
                    'Message',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
              const SizedBox(height: 6),
              // زر عرض الملف الشخصي
              SizedBox(
                width: 80,
                height: 32,
                child: OutlinedButton(
                  onPressed: () => _viewUserProfile(user),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF1877F2),
                    side: const BorderSide(color: Color(0xFF1877F2)),
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  child: const Text(
                    'Profile',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بدء محادثة مع المستخدم
  void _startChatWithUser(UserModel user) {
    Navigator.of(context).pushNamed(
      '/chat',
      arguments: {
        'user': user,
        'chatId': '${Provider.of<AuthService>(context, listen: false).currentUser!.phoneNumber}_${user.phoneNumber}',
      },
    );
  }

  /// عرض الملف الشخصي للمستخدم
  void _viewUserProfile(UserModel user) {
    Navigator.of(context).pushNamed(
      '/social_user_profile',
      arguments: user,
    );
  }

  /// إظهار قائمة خيارات الإنشاء مثل Facebook
  void _showCreateOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'Create',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1C1E21),
                ),
              ),
            ),

            // الخيارات
            _buildCreateOption(
              icon: Icons.article,
              title: 'Post',
              subtitle: 'Share a thought',
              color: const Color(0xFF1877F2),
              onTap: () {
                Navigator.pop(context);
                _createNewPost();
              },
            ),

            _buildCreateOption(
              icon: Icons.videocam,
              title: 'Live Video',
              subtitle: 'Go live now',
              color: const Color(0xFFE42645),
              onTap: () {
                Navigator.pop(context);
                _startLiveStream();
              },
            ),

            _buildCreateOption(
              icon: Icons.video_library,
              title: 'Video',
              subtitle: 'Upload a video',
              color: const Color(0xFF1877F2),
              onTap: () {
                Navigator.pop(context);
                _uploadVideo();
              },
            ),



            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// بناء خيار إنشاء
  Widget _buildCreateOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: Color(0xFF1C1E21),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: Color(0xFF65676B),
          fontSize: 13,
        ),
      ),
      onTap: onTap,
    );
  }

  /// إظهار قائمة التطبيق الشاملة
  void _showAppMenu() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                'ArzaTalk Features',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1C1E21),
                ),
              ),
            ),

            // قائمة الميزات
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  // قسم الدردشة
                  _buildMenuSection(
                    title: 'Chat & Messaging',
                    icon: Icons.chat,
                    color: const Color(0xFF1877F2),
                    items: [
                      _buildMenuItem(
                        icon: Icons.message,
                        title: 'Messages',
                        subtitle: 'Chat with friends',
                        onTap: () => _navigateToSection('/chat'),
                      ),
                      _buildMenuItem(
                        icon: Icons.group,
                        title: 'Groups',
                        subtitle: 'Group conversations',
                        onTap: () => _navigateToSection('/groups'),
                      ),
                      _buildMenuItem(
                        icon: Icons.contacts,
                        title: 'Contacts',
                        subtitle: 'Manage your contacts',
                        onTap: () => _navigateToSection('/contacts'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // قسم الشبكة الاجتماعية
                  _buildMenuSection(
                    title: 'Social Media',
                    icon: Icons.public,
                    color: const Color(0xFF42B883),
                    items: [
                      _buildMenuItem(
                        icon: Icons.feed,
                        title: 'News Feed',
                        subtitle: 'Social posts and updates',
                        onTap: () => Navigator.pop(context),
                      ),
                      _buildMenuItem(
                        icon: Icons.auto_stories,
                        title: 'Stories',
                        subtitle: '24-hour stories',
                        onTap: () => _navigateToSection('/stories'),
                      ),
                      _buildMenuItem(
                        icon: Icons.video_library,
                        title: 'Videos',
                        subtitle: 'Watch and share videos',
                        onTap: () => _showVideoSection(),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // قسم المكالمات
                  _buildMenuSection(
                    title: 'Calls & Communication',
                    icon: Icons.call,
                    color: const Color(0xFFE42645),
                    items: [
                      _buildMenuItem(
                        icon: Icons.call,
                        title: 'Voice Calls',
                        subtitle: 'Make voice calls',
                        onTap: () => _navigateToSection('/calls'),
                      ),
                      _buildMenuItem(
                        icon: Icons.videocam,
                        title: 'Video Calls',
                        subtitle: 'Make video calls',
                        onTap: () => _navigateToSection('/calls'),
                      ),
                      _buildMenuItem(
                        icon: Icons.live_tv,
                        title: 'Live Streaming',
                        subtitle: 'Go live with friends',
                        onTap: () {
                          Navigator.pop(context);
                          _startLiveStream();
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // قسم الإعدادات والحساب
                  _buildMenuSection(
                    title: 'Account & Settings',
                    icon: Icons.settings,
                    color: const Color(0xFF9C27B0),
                    items: [
                      _buildMenuItem(
                        icon: Icons.person,
                        title: 'Profile',
                        subtitle: 'Edit your profile',
                        onTap: () => _navigateToSection('/profile'),
                      ),
                      _buildMenuItem(
                        icon: Icons.settings,
                        title: 'Settings',
                        subtitle: 'App preferences',
                        onTap: () => _navigateToSection('/settings'),
                      ),
                      _buildMenuItem(
                        icon: Icons.privacy_tip,
                        title: 'Privacy',
                        subtitle: 'Privacy controls',
                        onTap: () => _navigateToSection('/settings/privacy'),
                      ),
                      _buildMenuItem(
                        icon: Icons.security,
                        title: 'Security',
                        subtitle: 'Account security',
                        onTap: () => _navigateToSection('/settings/security'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // قسم الأدوات والميزات
                  _buildMenuSection(
                    title: 'Tools & Features',
                    icon: Icons.build,
                    color: const Color(0xFFF39C12),
                    items: [
                      _buildMenuItem(
                        icon: Icons.search,
                        title: 'Search',
                        subtitle: 'Find people and content',
                        onTap: () {
                          Navigator.pop(context);
                          _showSearchDialog();
                        },
                      ),
                      _buildMenuItem(
                        icon: Icons.share,
                        title: 'Share App',
                        subtitle: 'Invite friends to ArzaTalk',
                        onTap: () => _shareApp(),
                      ),
                      _buildMenuItem(
                        icon: Icons.help,
                        title: 'Help & Support',
                        subtitle: 'Get help and support',
                        onTap: () => _showHelpDialog(),
                      ),
                      _buildMenuItem(
                        icon: Icons.info,
                        title: 'About ArzaTalk',
                        subtitle: 'App information',
                        onTap: () => _showAboutDialog(),
                      ),
                    ],
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم في القائمة
  Widget _buildMenuSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1C1E21),
                ),
              ),
            ],
          ),
        ),

        // العناصر
        ...items,
      ],
    );
  }

  /// بناء عنصر في القائمة
  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFF1877F2)),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: Color(0xFF1C1E21),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: Color(0xFF65676B),
          fontSize: 13,
        ),
      ),
      onTap: onTap,
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Color(0xFF65676B),
      ),
    );
  }

  /// التنقل إلى قسم معين
  void _navigateToSection(String route) {
    Navigator.pop(context); // إغلاق القائمة أولاً

    switch (route) {
      case '/chat':
        Navigator.pushReplacementNamed(context, '/home', arguments: 0);
        break;
      case '/groups':
        Navigator.pushReplacementNamed(context, '/home', arguments: 1);
        break;
      case '/contacts':
        Navigator.pushReplacementNamed(context, '/home', arguments: 2);
        break;
      case '/stories':
        Navigator.pushReplacementNamed(context, '/home', arguments: 3);
        break;
      case '/calls':
        Navigator.pushNamed(context, '/calls');
        break;
      case '/profile':
        Navigator.pushNamed(context, '/profile');
        break;
      case '/settings':
        Navigator.pushNamed(context, '/settings');
        break;
      case '/settings/privacy':
        Navigator.pushNamed(context, '/settings/privacy');
        break;
      case '/settings/security':
        Navigator.pushNamed(context, '/settings/security');
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Feature coming soon: $route')),
        );
    }
  }

  /// إظهار قسم الفيديوهات
  void _showVideoSection() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Video section coming soon!'),
        backgroundColor: Color(0xFF1877F2),
      ),
    );
  }

  /// مشاركة التطبيق
  void _shareApp() {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share feature coming soon!'),
        backgroundColor: Color(0xFF42B883),
      ),
    );
  }

  /// إظهار حوار المساعدة
  void _showHelpDialog() {
    Navigator.pop(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const Text(
          'Welcome to ArzaTalk!\n\n'
          'For support, contact us at:\n'
          '📧 <EMAIL>\n'
          '📞 +212638813823\n\n'
          'Visit our website for more information.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// إظهار حوار حول التطبيق
  void _showAboutDialog() {
    Navigator.pop(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About ArzaTalk'),
        content: const Text(
          'ArzaTalk v1.0.0\n\n'
          'A comprehensive chat and social media app.\n\n'
          'Features:\n'
          '• Instant messaging\n'
          '• Group chats\n'
          '• Social media feed\n'
          '• Stories\n'
          '• Voice & video calls\n'
          '• Live streaming\n\n'
          'Developed by Arzapress\n'
          '© 2024 All rights reserved',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// بناء خيارات فرز التعليقات مثل Facebook
  Widget _buildCommentSortOptions(String postId) {
    final currentSort = _commentSortTypes[postId] ?? 'newest';

    return SizedBox(
      height: 40,
      child: Row(
        children: [
          // زر الفرز الرئيسي
          Expanded(
            child: PopupMenuButton<String>(
              onSelected: (value) {
                setState(() {
                  _commentSortTypes[postId] = value;
                });
              },
              itemBuilder: (context) => [
                PopupMenuItem<String>(
                  value: 'most_relevant',
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            currentSort == 'most_relevant' ? Icons.check : Icons.trending_up,
                            size: 16,
                            color: const Color(0xFF1877F2),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Most relevant',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 24, top: 2),
                        child: Text(
                          'See comments from friends and most reacted first',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF65676B),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'newest',
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            currentSort == 'newest' ? Icons.check : Icons.access_time,
                            size: 16,
                            color: const Color(0xFF1877F2),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Newest',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 24, top: 2),
                        child: Text(
                          'See all comments with newest first',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF65676B),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'all_comments',
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            currentSort == 'all_comments' ? Icons.check : Icons.comment,
                            size: 16,
                            color: const Color(0xFF1877F2),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'All comments',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                      const Padding(
                        padding: EdgeInsets.only(left: 24, top: 2),
                        child: Text(
                          'See all comments including low-quality or potential spam',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF65676B),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF0F2F5),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: const Color(0xFFE4E6EA)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getSortIcon(currentSort),
                      size: 16,
                      color: const Color(0xFF1877F2),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      _getSortLabel(currentSort),
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1C1E21),
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.keyboard_arrow_down,
                      size: 16,
                      color: Color(0xFF65676B),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الفرز
  IconData _getSortIcon(String sortType) {
    switch (sortType) {
      case 'most_relevant':
        return Icons.trending_up;
      case 'newest':
        return Icons.access_time;
      case 'all_comments':
        return Icons.comment;
      default:
        return Icons.access_time;
    }
  }

  /// الحصول على تسمية الفرز
  String _getSortLabel(String sortType) {
    switch (sortType) {
      case 'most_relevant':
        return 'Most relevant';
      case 'newest':
        return 'Newest';
      case 'all_comments':
        return 'All comments';
      default:
        return 'Newest';
    }
  }

  /// تنفيذ البحث
  void _performSearch(String query, ScrollController? scrollController) {
    if (query.trim().isEmpty) return;
    debugPrint('Searching for: $query');
  }

  /// تحديث الخلاصة من Firebase
  Future<void> _refreshFeed() async {
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    await socialService.refreshFromFirebase();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🔄 Feed refreshed from Firebase'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }
}
