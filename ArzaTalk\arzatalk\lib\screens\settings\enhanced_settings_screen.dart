import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import 'notifications_settings_screen.dart';
import 'language_region_settings_screen.dart';
import 'media_settings_screen.dart';
import 'stories_settings_screen.dart';
import 'blocked_users_screen.dart';
import 'activity_status_screen.dart';
import 'privacy_policy_screen.dart';

/// شاشة الإعدادات المحسنة مثل Facebook
class EnhancedSettingsScreen extends StatefulWidget {
  const EnhancedSettingsScreen({super.key});

  @override
  State<EnhancedSettingsScreen> createState() => _EnhancedSettingsScreenState();
}

class _EnhancedSettingsScreenState extends State<EnhancedSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Settings',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF1877F2),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // معلومات المستخدم
            _buildUserProfileCard(),
            
            const SizedBox(height: 16),

            // قسم الإشعارات
            _buildSettingsSection(
              title: 'Notifications',
              icon: Icons.notifications,
              color: const Color(0xFFFF6B35),
              onTap: () => _navigateToNotifications(),
            ),

            const SizedBox(height: 12),

            // قسم اللغة والمنطقة
            _buildSettingsSection(
              title: 'Language & Region',
              icon: Icons.language,
              color: const Color(0xFF1877F2),
              onTap: () => _navigateToLanguageRegion(),
            ),

            const SizedBox(height: 12),

            // قسم الوسائط
            _buildSettingsSection(
              title: 'Media & Storage',
              icon: Icons.perm_media,
              color: const Color(0xFF42B883),
              onTap: () => _navigateToMedia(),
            ),

            const SizedBox(height: 12),

            // قسم القصص
            _buildSettingsSection(
              title: 'Stories Settings',
              icon: Icons.auto_awesome_motion,
              color: const Color(0xFF9C27B0),
              onTap: () => _navigateToStories(),
            ),

            const SizedBox(height: 12),

            // قسم المستخدمين المحظورين
            _buildSettingsSection(
              title: 'Blocked Users',
              icon: Icons.block,
              color: const Color(0xFFFF5722),
              onTap: () => _navigateToBlockedUsers(),
            ),

            const SizedBox(height: 12),

            // قسم حالة النشاط
            _buildSettingsSection(
              title: 'Activity Status',
              icon: Icons.circle,
              color: const Color(0xFF4CAF50),
              onTap: () => _navigateToActivityStatus(),
            ),

            const SizedBox(height: 12),

            // قسم الخصوصية والشروط
            _buildSettingsSection(
              title: 'Privacy & Terms',
              icon: Icons.privacy_tip,
              color: const Color(0xFF607D8B),
              onTap: () => _navigateToPrivacyPolicy(),
            ),

            const SizedBox(height: 24),

            // زر تسجيل الخروج
            _buildLogoutButton(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الملف الشخصي
  Widget _buildUserProfileCard() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final currentUser = authService.currentUser;
        if (currentUser == null) return const SizedBox.shrink();

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: const Color(0xFF1877F2),
                child: Text(
                  currentUser.name.isNotEmpty ? currentUser.name[0].toUpperCase() : '?',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      currentUser.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      currentUser.phoneNumber,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.edit,
                color: Colors.grey[600],
                size: 20,
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء قسم إعدادات
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        onTap: onTap,
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: Colors.grey[400],
          size: 16,
        ),
      ),
    );
  }

  /// بناء زر تسجيل الخروج
  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: ListTile(
        onTap: _showLogoutDialog,
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.logout,
            color: Colors.red,
            size: 24,
          ),
        ),
        title: const Text(
          'Logout',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.red,
          ),
        ),
      ),
    );
  }

  /// التنقل إلى الإشعارات
  void _navigateToNotifications() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const NotificationsSettingsScreen(),
      ),
    );
  }

  /// التنقل إلى اللغة والمنطقة
  void _navigateToLanguageRegion() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const LanguageRegionSettingsScreen(),
      ),
    );
  }

  /// التنقل إلى الوسائط
  void _navigateToMedia() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const MediaSettingsScreen(),
      ),
    );
  }

  /// التنقل إلى القصص
  void _navigateToStories() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const StoriesSettingsScreen(),
      ),
    );
  }

  /// التنقل إلى المستخدمين المحظورين
  void _navigateToBlockedUsers() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const BlockedUsersScreen(),
      ),
    );
  }

  /// التنقل إلى حالة النشاط
  void _navigateToActivityStatus() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ActivityStatusScreen(),
      ),
    );
  }

  /// التنقل إلى الخصوصية والشروط
  void _navigateToPrivacyPolicy() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PrivacyPolicyScreen(),
      ),
    );
  }

  /// عرض حوار تسجيل الخروج
  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  /// تسجيل الخروج
  void _logout() {
    final authService = Provider.of<AuthService>(context, listen: false);
    authService.logout();
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }
}
