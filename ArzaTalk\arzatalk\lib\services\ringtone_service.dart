import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:vibration/vibration.dart';
import '../models/ringtone_model.dart';

class RingtoneService extends ChangeNotifier {
  static const String _messageRingtoneKey = 'selected_message_ringtone';
  static const String _callRingtoneKey = 'selected_call_ringtone';

  String _selectedMessageRingtone = 'msg_1'; // Default message ringtone
  String _selectedCallRingtone = 'call_1'; // Default call ringtone

  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;

  String get selectedMessageRingtone => _selectedMessageRingtone;
  String get selectedCallRingtone => _selectedCallRingtone;
  bool get isPlaying => _isPlaying;

  RingtoneService() {
    _loadRingtoneSettings();
  }

  // Load saved ringtone settings
  Future<void> _loadRingtoneSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _selectedMessageRingtone = prefs.getString(_messageRingtoneKey) ?? 'msg_1';
      _selectedCallRingtone = prefs.getString(_callRingtoneKey) ?? 'call_1';
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading ringtone settings: $e');
    }
  }

  // Set message ringtone
  Future<void> setMessageRingtone(String ringtoneId) async {
    try {
      _selectedMessageRingtone = ringtoneId;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_messageRingtoneKey, ringtoneId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting message ringtone: $e');
    }
  }

  // Set call ringtone
  Future<void> setCallRingtone(String ringtoneId) async {
    try {
      _selectedCallRingtone = ringtoneId;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_callRingtoneKey, ringtoneId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting call ringtone: $e');
    }
  }

  // Get selected message ringtone model
  RingtoneModel? getSelectedMessageRingtone() {
    return RingtoneData.getRingtoneById(_selectedMessageRingtone);
  }

  // Get selected call ringtone model
  RingtoneModel? getSelectedCallRingtone() {
    return RingtoneData.getRingtoneById(_selectedCallRingtone);
  }

  // Play ringtone preview
  Future<void> playRingtone(String ringtoneId) async {
    try {
      final ringtone = RingtoneData.getRingtoneById(ringtoneId);
      if (ringtone == null) {
        debugPrint('Ringtone not found: $ringtoneId');
        return;
      }

      // Stop any currently playing sound
      await stopRingtone();

      _isPlaying = true;
      notifyListeners();

      // Play actual audio file
      try {
        debugPrint('Attempting to play: sounds/${ringtone.fileName}');
        await _audioPlayer.play(AssetSource('sounds/${ringtone.fileName}'));
        debugPrint('Successfully started playing: ${ringtone.fileName}');

        // Auto-stop after duration
        Future.delayed(Duration(seconds: ringtone.duration.clamp(1, 10)), () {
          if (_isPlaying) {
            stopRingtone();
          }
        });
      } catch (e) {
        // If audio file doesn't exist, use vibration as fallback
        debugPrint('Audio file not found, using vibration fallback: ${ringtone.fileName}');
        debugPrint('Error details: $e');

        // Vibrate as fallback
        try {
          await Vibration.vibrate(duration: 500);
          debugPrint('Vibration fallback successful');
        } catch (vibError) {
          debugPrint('Vibration fallback failed: $vibError');
        }

        await Future.delayed(Duration(seconds: ringtone.duration.clamp(1, 3)));
        _isPlaying = false;
        notifyListeners();
      }
    } catch (e) {
      _isPlaying = false;
      notifyListeners();
      debugPrint('Error playing ringtone: $e');
    }
  }

  // Stop ringtone
  Future<void> stopRingtone() async {
    try {
      await _audioPlayer.stop();
      _isPlaying = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error stopping ringtone: $e');
    }
  }

  // Play message notification sound
  Future<void> playMessageNotification() async {
    try {
      final ringtone = getSelectedMessageRingtone();
      if (ringtone != null) {
        try {
          debugPrint('Playing message notification: ${ringtone.fileName}');
          await _audioPlayer.play(AssetSource('sounds/${ringtone.fileName}'));
          _isPlaying = true;
          notifyListeners();
          debugPrint('Message notification started successfully');

          // Auto-stop after 2 seconds for message sounds
          Future.delayed(const Duration(seconds: 2), () {
            if (_isPlaying) {
              stopRingtone();
            }
          });
        } catch (e) {
          debugPrint('Message ringtone file not found: ${ringtone.fileName}');
          debugPrint('Error details: $e');

          // Fallback to vibration
          try {
            await Vibration.vibrate(duration: 500);
            debugPrint('Message notification vibration fallback successful');
          } catch (vibError) {
            debugPrint('Message notification vibration fallback failed: $vibError');
          }
        }
      } else {
        debugPrint('No message ringtone selected, using vibration');
        try {
          await Vibration.vibrate(duration: 500);
        } catch (vibError) {
          debugPrint('Default message vibration failed: $vibError');
        }
      }
    } catch (e) {
      debugPrint('Error playing message notification: $e');
    }
  }

  // Play call ringtone
  Future<void> playCallRingtone() async {
    try {
      final ringtone = getSelectedCallRingtone();
      if (ringtone != null) {
        try {
          debugPrint('Playing call ringtone: ${ringtone.fileName}');
          await _audioPlayer.play(AssetSource('sounds/${ringtone.fileName}'));
          _isPlaying = true;
          notifyListeners();
          debugPrint('Call ringtone started successfully');
        } catch (e) {
          debugPrint('Call ringtone file not found: ${ringtone.fileName}');
          debugPrint('Error details: $e');

          // Fallback to vibration
          try {
            await Vibration.vibrate(duration: 1000);
            debugPrint('Call ringtone vibration fallback successful');
          } catch (vibError) {
            debugPrint('Call ringtone vibration fallback failed: $vibError');
          }
        }
      } else {
        debugPrint('No call ringtone selected, using vibration');
        try {
          await Vibration.vibrate(duration: 1000);
        } catch (vibError) {
          debugPrint('Default call vibration failed: $vibError');
        }
      }
    } catch (e) {
      debugPrint('Error playing call ringtone: $e');
    }
  }

  // Get all message ringtones
  List<RingtoneModel> getMessageRingtones() {
    return RingtoneData.getRingtonesByType(RingtoneType.message);
  }

  // Get all call ringtones
  List<RingtoneModel> getCallRingtones() {
    return RingtoneData.getRingtonesByType(RingtoneType.call);
  }

  // Check if ringtone is selected
  bool isRingtoneSelected(String ringtoneId, RingtoneType type) {
    switch (type) {
      case RingtoneType.message:
        return _selectedMessageRingtone == ringtoneId;
      case RingtoneType.call:
        return _selectedCallRingtone == ringtoneId;
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
