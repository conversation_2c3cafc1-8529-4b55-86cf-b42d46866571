name: arzatalk
description: "ArzaTalk - Professional Chat App with Social Media Features"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2

  # State Management
  provider: ^6.1.2

  # Media & File Handling
  image_picker: ^1.1.2
  file_picker: ^8.1.2
  cached_network_image: ^3.4.1

  # Audio Recording (معطل مؤقتاً)
  # flutter_sound: ^9.11.2
  # permission_handler: ^11.3.1

  # UI & Utils
  intl: ^0.19.0
  shared_preferences: ^2.3.2
  path_provider: ^2.1.5

  # Additional UI
  flutter_spinkit: ^5.2.1

  # Sound and vibration
  vibration: ^3.1.3
  audioplayers: ^6.4.0

  # URL launcher for contact links
  url_launcher: ^6.3.1

  # HTTP requests for link previews
  http: ^1.1.0

  # Photo viewer for image zoom and gallery
  photo_view: ^0.14.0

  # Share functionality
  share_plus: ^7.2.2

  # Save images to gallery (disabled for now)
  # gallery_saver: ^2.3.2

  # HTTP client for downloading
  dio: ^5.4.0

  # Local authentication for chat lock
  local_auth: ^2.3.0

  # Location services
  geolocator: ^13.0.1

  # Permissions
  permission_handler: ^11.3.1
  video_player: ^2.9.5
  camera: ^0.11.1

  # Contacts (معطل مؤقتاً بسبب مشاكل التوافق)
  # contacts_service: ^0.6.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/sounds/
    - assets/images/
