import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/message_model.dart';
import '../services/chat_service.dart';
import '../services/group_service.dart';
import '../services/auth_service.dart';
import '../services/reactions_service.dart';
import '../services/pinned_messages_service.dart';
import 'image_message.dart';
import 'voice_message.dart';

class MessageBubble extends StatefulWidget {
  final MessageModel message;
  final bool isMe;
  final String? groupId; // For group messages
  final VoidCallback? onMessageUpdated; // Callback when message is updated
  final Function(MessageModel)? onReply; // Callback for replying to message
  final Function(MessageModel)? onForward; // Callback for forwarding message

  const MessageBubble({
    super.key,
    required this.message,
    required this.isMe,
    this.groupId,
    this.onMessageUpdated,
    this.onReply,
    this.onForward,
  });

  @override
  State<MessageBubble> createState() => _MessageBubbleState();
}

class _MessageBubbleState extends State<MessageBubble> {
  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(widget.message.id),
      direction: DismissDirection.startToEnd,
      confirmDismiss: (direction) async {
        if (widget.onReply != null) {
          widget.onReply!(widget.message);
        }
        return false; // Don't actually dismiss
      },
      background: Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        color: Colors.green.withValues(alpha: 0.3),
        child: const Icon(
          Icons.reply,
          color: Colors.green,
          size: 30,
        ),
      ),
      child: GestureDetector(
        onLongPress: () => _showMessageOptions(context),
        onDoubleTap: () => _handleDoubleTap(context),
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: Row(
            mainAxisAlignment: widget.isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
            children: [
              if (!widget.isMe) const SizedBox(width: 40),
              Flexible(
                child: Container(
                  padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.isMe ? const Color(0xFFD32F2F) : Colors.grey[200],
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(16),
                    topRight: const Radius.circular(16),
                    bottomLeft: widget.isMe ? const Radius.circular(16) : const Radius.circular(4),
                    bottomRight: widget.isMe ? const Radius.circular(4) : const Radius.circular(16),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildMessageContent(),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (widget.message.isEdited) ...[
                          Text(
                            'edited',
                            style: TextStyle(
                              fontSize: 10,
                              color: widget.isMe ? Colors.white60 : Colors.grey[500],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                          const SizedBox(width: 4),
                        ],
                        Text(
                          DateFormat('HH:mm').format(widget.message.timestamp),
                          style: TextStyle(
                            fontSize: 11,
                            color: widget.isMe ? Colors.white70 : Colors.grey[600],
                          ),
                        ),
                        if (widget.isMe) ...[
                          const SizedBox(width: 4),
                          Icon(
                            widget.message.isRead ? Icons.done_all : Icons.done,
                            size: 16,
                            color: widget.message.isRead ? Colors.blue : Colors.white70,
                          ),
                        ],
                        // إضافة أيقونة التثبيت
                        if (widget.message.isPinned) ...[
                          const SizedBox(width: 4),
                          Icon(
                            Icons.push_pin,
                            size: 12,
                            color: widget.isMe ? Colors.white70 : Colors.grey[600],
                          ),
                        ],
                      ],
                    ),
                    // إضافة ردود الفعل
                    _buildMessageReactions(),
                  ],
                ),
                ),
              ),
              if (widget.isMe) const SizedBox(width: 40),
            ],
          ),
        ),
      ),
    );
  }

  // إضافة دالة النقر المزدوج
  void _handleDoubleTap(BuildContext context) {
    final reactionsService = Provider.of<ReactionsService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      reactionsService.addReaction(
        messageId: widget.message.id,
        emoji: '👍',
        userId: currentUser.phoneNumber,
        userName: currentUser.name,
      );

      // إظهار تأثير بصري
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('👍 Reaction added!'),
          duration: Duration(milliseconds: 800),
        ),
      );
    }
  }

  // بناء ردود الفعل للرسالة
  Widget _buildMessageReactions() {
    return Consumer<ReactionsService>(
      builder: (context, reactionsService, child) {
        final authService = Provider.of<AuthService>(context, listen: false);
        final currentUser = authService.currentUser;
        final currentUserId = currentUser?.phoneNumber ?? '';

        final reactionStats = reactionsService.getReactionStatsForMessage(widget.message.id, currentUserId);

        if (reactionStats.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.only(top: 4),
          child: Wrap(
            spacing: 4,
            runSpacing: 4,
            children: reactionStats.map((stat) => GestureDetector(
              onTap: () => _addReaction(context, stat.emoji),
              onLongPress: () => _showReactionDetails(context, stat),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: stat.currentUserReacted
                      ? const Color(0xFFD32F2F).withValues(alpha: 0.1)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: stat.currentUserReacted
                        ? const Color(0xFFD32F2F)
                        : Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      stat.emoji,
                      style: const TextStyle(fontSize: 16),
                    ),
                    if (stat.count > 1) ...[
                      const SizedBox(width: 4),
                      Text(
                        '${stat.count}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: stat.currentUserReacted
                              ? const Color(0xFFD32F2F)
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            )).toList(),
          ),
        );
      },
    );
  }

  // عرض تفاصيل ردود الفعل
  void _showReactionDetails(BuildContext context, dynamic stat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Text(
              stat.emoji,
              style: const TextStyle(fontSize: 24),
            ),
            const SizedBox(width: 8),
            Text(
              '${stat.count} ${stat.count == 1 ? 'person' : 'people'}',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...stat.userNames.map((name) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Text(
                name,
                style: const TextStyle(fontSize: 14),
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  // بناء رد فعل سريع
  Widget _buildQuickReaction(BuildContext context, String emoji) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        _addReaction(context, emoji);
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Center(
          child: Text(
            emoji,
            style: const TextStyle(fontSize: 20),
          ),
        ),
      ),
    );
  }

  // إضافة رد فعل
  void _addReaction(BuildContext context, String emoji) {
    final reactionsService = Provider.of<ReactionsService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      reactionsService.addReaction(
        messageId: widget.message.id,
        emoji: emoji,
        userId: currentUser.phoneNumber,
        userName: currentUser.name,
      );
    }
  }

  Widget _buildMessageContent() {
    if (widget.message.isDeleted) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.block,
            size: 16,
            color: widget.isMe ? Colors.white60 : Colors.grey[500],
          ),
          const SizedBox(width: 8),
          Text(
            'This message was deleted',
            style: TextStyle(
              color: widget.isMe ? Colors.white60 : Colors.grey[500],
              fontSize: 14,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      );
    }

    switch (widget.message.type) {
      case MessageType.text:
        return Text(
          widget.message.message,
          style: TextStyle(
            color: widget.isMe ? Colors.white : Colors.black87,
            fontSize: 16,
          ),
          textAlign: TextAlign.left,
        );

      case MessageType.image:
        return ImageMessage(
          imageUrl: widget.message.mediaUrl!,
          message: widget.message.message,
          isMe: widget.isMe,
        );

      case MessageType.voice:
        return VoiceMessage(
          audioUrl: widget.message.mediaUrl!,
          duration: widget.message.duration ?? 0,
          isMe: widget.isMe,
        );

      case MessageType.video:
        return Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.videocam,
                color: widget.isMe ? Colors.white : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                'Video',
                style: TextStyle(
                  color: widget.isMe ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        );

      case MessageType.file:
        return Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.attach_file,
                color: widget.isMe ? Colors.white : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                'File',
                style: TextStyle(
                  color: widget.isMe ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ),
        );

      case MessageType.sticker:
        return Container(
          padding: const EdgeInsets.all(8),
          child: Text(
            widget.message.message, // الملصق كإيموجي
            style: const TextStyle(fontSize: 48),
          ),
        );
    }
  }

  void _showMessageOptions(BuildContext context) {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser == null) return;

    final canEdit = widget.isMe &&
                   widget.message.type == MessageType.text &&
                   !widget.message.isDeleted &&
                   DateTime.now().difference(widget.message.timestamp).inMinutes < 15; // Can edit within 15 minutes

    final canDelete = widget.isMe ||
                     (widget.groupId != null); // In groups, anyone can delete their own messages

    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // إضافة ردود الفعل السريعة
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildQuickReaction(context, '👍'),
                  _buildQuickReaction(context, '❤️'),
                  _buildQuickReaction(context, '😂'),
                  _buildQuickReaction(context, '😮'),
                  _buildQuickReaction(context, '😢'),
                  _buildQuickReaction(context, '😡'),
                ],
              ),
            ),
            const Divider(),
            if (canEdit)
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Edit Message'),
                onTap: () {
                  Navigator.pop(context);
                  _showEditDialog(context);
                },
              ),
            ListTile(
              leading: const Icon(Icons.reply),
              title: const Text('Reply'),
              onTap: () {
                Navigator.pop(context);
                if (widget.onReply != null) {
                  widget.onReply!(widget.message);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.forward),
              title: const Text('Forward'),
              onTap: () {
                Navigator.pop(context);
                if (widget.onForward != null) {
                  widget.onForward!(widget.message);
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copy'),
              onTap: () {
                Navigator.pop(context);
                _copyMessage();
              },
            ),
            ListTile(
              leading: Icon(
                widget.message.isPinned ? Icons.push_pin_outlined : Icons.push_pin,
                color: widget.message.isPinned ? Colors.red : null,
              ),
              title: Text(
                widget.message.isPinned ? 'Unpin Message' : 'Pin Message',
                style: TextStyle(
                  color: widget.message.isPinned ? Colors.red : null,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _togglePinMessage(context);
              },
            ),
            if (canDelete)
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Message', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteDialog(context);
                },
              ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('Message Info'),
              onTap: () {
                Navigator.pop(context);
                _showMessageInfo(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showEditDialog(BuildContext context) {
    final controller = TextEditingController(text: widget.message.message);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Message'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'Enter new message...',
            border: OutlineInputBorder(),
          ),
          maxLines: null,
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _editMessage(controller.text.trim());
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Message'),
        content: const Text('Are you sure you want to delete this message?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteMessage();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _editMessage(String newMessage) {
    if (newMessage.isEmpty || newMessage == widget.message.message) return;

    try {
      if (widget.groupId != null) {
        // Group message
        final groupService = Provider.of<GroupService>(context, listen: false);
        groupService.editGroupMessage(widget.message.id, newMessage);
      } else {
        // Individual message
        final chatService = Provider.of<ChatService>(context, listen: false);
        chatService.editMessage(widget.message.id, newMessage);
      }

      widget.onMessageUpdated?.call();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Message edited')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error editing message: $e')),
      );
    }
  }

  void _deleteMessage() {
    try {
      if (widget.groupId != null) {
        // Group message
        final groupService = Provider.of<GroupService>(context, listen: false);
        groupService.deleteGroupMessage(widget.message.id);
      } else {
        // Individual message
        final chatService = Provider.of<ChatService>(context, listen: false);
        chatService.deleteIndividualMessage(widget.message.id);
      }

      widget.onMessageUpdated?.call();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Message deleted')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting message: $e')),
      );
    }
  }

  void _copyMessage() {
    if (widget.message.type == MessageType.text && !widget.message.isDeleted) {
      Clipboard.setData(ClipboardData(text: widget.message.message));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Message copied to clipboard')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Cannot copy this message type')),
      );
    }
  }

  // تبديل تثبيت الرسالة
  void _togglePinMessage(BuildContext context) {
    final pinnedService = Provider.of<PinnedMessagesService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser == null) return;

    if (widget.message.isPinned) {
      // إلغاء التثبيت
      pinnedService.unpinMessage(widget.message.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Message unpinned')),
      );
    } else {
      // تثبيت الرسالة
      final chatId = widget.groupId ?? _getChatId(currentUser.phoneNumber, widget.message.senderId);
      pinnedService.pinMessage(
        messageId: widget.message.id,
        chatId: chatId,
        content: widget.message.message,
        pinnedBy: currentUser.phoneNumber,
        pinnedByName: currentUser.name,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Message pinned')),
      );
    }
  }

  // الحصول على معرف الدردشة
  String _getChatId(String userId1, String userId2) {
    final List<String> ids = [userId1, userId2];
    ids.sort();
    return ids.join('_');
  }

  void _showMessageInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Message Info'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Sent: ${DateFormat('dd/MM/yyyy HH:mm').format(widget.message.timestamp)}'),
            if (widget.message.isEdited)
              const Text('Edited: Yes'),
            if (widget.message.isRead)
              const Text('Read: Yes')
            else
              const Text('Read: No'),
            if (widget.message.isPinned)
              const Text('Pinned: Yes'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
