import 'package:flutter/material.dart';

/// نموذج الملاحظة (مثل Facebook Notes)
class NoteModel {
  final String id;
  final String userId;
  final String userName;
  final String? userProfileImage;
  final String content;
  final Color backgroundColor;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive; // هل الملاحظة نشطة أم لا
  final List<String> viewedBy; // من شاهد الملاحظة

  const NoteModel({
    required this.id,
    required this.userId,
    required this.userName,
    this.userProfileImage,
    required this.content,
    required this.backgroundColor,
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.viewedBy = const [],
  });

  /// إنشاء ملاحظة من Map
  factory NoteModel.fromMap(Map<String, dynamic> map) {
    return NoteModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      userProfileImage: map['userProfileImage'],
      content: map['content'] ?? '',
      backgroundColor: Color(map['backgroundColor'] ?? 0xFF1877F2),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: map['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
          : null,
      isActive: map['isActive'] ?? true,
      viewedBy: List<String>.from(map['viewedBy'] ?? []),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userProfileImage': userProfileImage,
      'content': content,
      'backgroundColor': backgroundColor.value,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'isActive': isActive,
      'viewedBy': viewedBy,
    };
  }

  /// نسخ مع تعديل
  NoteModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userProfileImage,
    String? content,
    Color? backgroundColor,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    List<String>? viewedBy,
  }) {
    return NoteModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImage: userProfileImage ?? this.userProfileImage,
      content: content ?? this.content,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      viewedBy: viewedBy ?? this.viewedBy,
    );
  }

  /// الحصول على نص الوقت المنسق
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  /// التحقق من انتهاء صلاحية الملاحظة (24 ساعة)
  bool get isExpired {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours >= 24;
  }

  /// إضافة مشاهد للملاحظة
  NoteModel addViewer(String viewerId) {
    if (viewedBy.contains(viewerId)) return this;
    
    final newViewedBy = List<String>.from(viewedBy);
    newViewedBy.add(viewerId);
    
    return copyWith(viewedBy: newViewedBy);
  }

  /// التحقق من مشاهدة المستخدم للملاحظة
  bool hasViewedBy(String userId) {
    return viewedBy.contains(userId);
  }

  /// عدد المشاهدين
  int get viewersCount => viewedBy.length;
}
