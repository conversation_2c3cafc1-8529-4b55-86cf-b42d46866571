#!/usr/bin/env python3
"""
إنشاء أيقونة احترافية حمراء للدردشة
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_chat_icon():
    # إنشاء صورة 512x512 بخلفية شفافة
    size = 512
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # الألوان
    red_gradient_start = (255, 68, 68)  # أحمر فاتح
    red_gradient_end = (197, 48, 48)    # أحمر غامق
    white = (255, 255, 255)
    light_red = (255, 229, 229)
    
    # رسم الخلفية الدائرية المتدرجة
    center = size // 2
    radius = 240
    
    # محاكاة التدرج بدوائر متعددة
    for i in range(radius, 0, -2):
        # حساب اللون المتدرج
        ratio = (radius - i) / radius
        r = int(red_gradient_start[0] + (red_gradient_end[0] - red_gradient_start[0]) * ratio)
        g = int(red_gradient_start[1] + (red_gradient_end[1] - red_gradient_start[1]) * ratio)
        b = int(red_gradient_start[2] + (red_gradient_end[2] - red_gradient_start[2]) * ratio)
        
        draw.ellipse([center-i, center-i, center+i, center+i], fill=(r, g, b))
    
    # رسم حلقة داخلية للعمق
    draw.ellipse([center-220, center-220, center+220, center+220], 
                 outline=(*white, 80), width=4)
    
    # رسم فقاعة الدردشة الكبيرة
    bubble_left = center - 80
    bubble_top = center - 100
    bubble_right = center + 80
    bubble_bottom = center + 60
    
    # الفقاعة الرئيسية
    draw.rounded_rectangle([bubble_left, bubble_top, bubble_right, bubble_bottom], 
                          radius=25, fill=white)
    
    # ذيل الفقاعة
    tail_points = [
        (bubble_left - 20, bubble_bottom),
        (bubble_left, bubble_bottom - 20),
        (bubble_left, bubble_bottom)
    ]
    draw.polygon(tail_points, fill=white)
    
    # رسم فقاعة صغيرة
    small_bubble_left = center + 20
    small_bubble_top = center - 40
    small_bubble_right = center + 80
    small_bubble_bottom = center - 5
    
    draw.rounded_rectangle([small_bubble_left, small_bubble_top, small_bubble_right, small_bubble_bottom], 
                          radius=15, fill=light_red)
    
    # ذيل الفقاعة الصغيرة
    small_tail_points = [
        (small_bubble_left + 10, small_bubble_bottom),
        (small_bubble_left + 25, small_bubble_bottom + 15),
        (small_bubble_left + 30, small_bubble_bottom)
    ]
    draw.polygon(small_tail_points, fill=light_red)
    
    # رسم نقاط الدردشة
    dot_y = center - 40
    dot_radius = 8
    dot_color = red_gradient_end
    
    draw.ellipse([center-40-dot_radius, dot_y-dot_radius, center-40+dot_radius, dot_y+dot_radius], 
                 fill=dot_color)
    draw.ellipse([center-10-dot_radius, dot_y-dot_radius, center-10+dot_radius, dot_y+dot_radius], 
                 fill=dot_color)
    draw.ellipse([center+20-dot_radius, dot_y-dot_radius, center+20+dot_radius, dot_y+dot_radius], 
                 fill=dot_color)
    
    # رسم خطوط النص
    text_color = (*red_gradient_end, 150)
    draw.rectangle([bubble_left+20, center-20, bubble_left+100, center-14], fill=text_color)
    draw.rectangle([bubble_left+20, center-5, bubble_left+80, center+1], fill=text_color)
    draw.rectangle([bubble_left+20, center+10, bubble_left+90, center+16], fill=text_color)
    
    # رسم قلب صغير
    heart_x = center + 45
    heart_y = center - 15
    heart_size = 8
    draw.ellipse([heart_x-heart_size//2, heart_y-heart_size//2, 
                  heart_x+heart_size//2, heart_y+heart_size//2], fill=(255, 107, 107))
    
    # إضافة تأثير اللمعان
    shine_center_x = center - 56
    shine_center_y = center - 76
    shine_width = 60
    shine_height = 30
    
    # رسم اللمعان كشكل بيضاوي شفاف
    shine_img = Image.new('RGBA', (shine_width, shine_height), (0, 0, 0, 0))
    shine_draw = ImageDraw.Draw(shine_img)
    shine_draw.ellipse([0, 0, shine_width, shine_height], fill=(*white, 80))
    
    # تدوير اللمعان
    shine_img = shine_img.rotate(-30, expand=True)
    img.paste(shine_img, (shine_center_x - shine_img.width//2, shine_center_y - shine_img.height//2), shine_img)
    
    # حفظ الصورة
    img.save('app_icon.png', 'PNG')
    print("✅ تم إنشاء أيقونة التطبيق بنجاح: app_icon.png")

if __name__ == "__main__":
    create_chat_icon()
