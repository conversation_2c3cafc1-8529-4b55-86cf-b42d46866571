import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/saved_items_service.dart';
import '../../models/post_model.dart';

/// شاشة العناصر المحفوظة في الإعدادات
class SavedItemsSettingsScreen extends StatefulWidget {
  const SavedItemsSettingsScreen({super.key});

  @override
  State<SavedItemsSettingsScreen> createState() => _SavedItemsSettingsScreenState();
}

class _SavedItemsSettingsScreenState extends State<SavedItemsSettingsScreen> {
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    // تحميل المنشورات المحفوظة عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final savedItemsService = Provider.of<SavedItemsService>(context, listen: false);
      savedItemsService.loadSavedPosts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Saved Items',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1C1E21),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        iconTheme: const IconThemeData(color: Color(0xFF1C1E21)),
        actions: [
          // قائمة الخيارات
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'clear_all':
                  _showClearAllDialog();
                  break;
                case 'manage':
                  _showManageDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'manage',
                child: Row(
                  children: [
                    Icon(Icons.settings, color: Color(0xFF65676B)),
                    SizedBox(width: 8),
                    Text('Manage'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Clear All'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<SavedItemsService>(
        builder: (context, savedItemsService, child) {
          final allPosts = savedItemsService.savedPosts;
          final filteredPosts = _selectedFilter == 'all' 
              ? allPosts 
              : savedItemsService.getSavedPostsByType(_selectedFilter);

          if (filteredPosts.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              // إحصائيات العناصر المحفوظة
              _buildStatsCard(savedItemsService),
              
              // شريط الفلاتر
              _buildFilterBar(savedItemsService),
              
              // قائمة المنشورات المحفوظة
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: filteredPosts.length,
                  itemBuilder: (context, index) {
                    final post = filteredPosts[index];
                    return _buildSavedPostCard(post, savedItemsService);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// بناء بطاقة الإحصائيات
  Widget _buildStatsCard(SavedItemsService service) {
    final stats = service.savedItemsStats;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Saved Items Statistics',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1C1E21),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('Total', stats['total'] ?? 0, Icons.bookmark, const Color(0xFF1877F2)),
              _buildStatItem('Photos', stats['image'] ?? 0, Icons.image, const Color(0xFF42B883)),
              _buildStatItem('Videos', stats['video'] ?? 0, Icons.play_circle, const Color(0xFFE91E63)),
              _buildStatItem('Links', stats['link'] ?? 0, Icons.link, const Color(0xFFFF9800)),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(String label, int count, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1C1E21),
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF65676B),
          ),
        ),
      ],
    );
  }

  /// بناء شريط الفلاتر
  Widget _buildFilterBar(SavedItemsService service) {
    final stats = service.savedItemsStats;
    
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('all', 'All (${stats['total'] ?? 0})'),
          _buildFilterChip('text', 'Text (${stats['text'] ?? 0})'),
          _buildFilterChip('image', 'Photos (${stats['image'] ?? 0})'),
          _buildFilterChip('video', 'Videos (${stats['video'] ?? 0})'),
          _buildFilterChip('link', 'Links (${stats['link'] ?? 0})'),
        ],
      ),
    );
  }

  /// بناء رقاقة الفلتر
  Widget _buildFilterChip(String filter, String label) {
    final isSelected = _selectedFilter == filter;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        selected: isSelected,
        label: Text(label),
        onSelected: (selected) {
          setState(() {
            _selectedFilter = filter;
          });
        },
        backgroundColor: Colors.white,
        selectedColor: const Color(0xFF1877F2).withValues(alpha: 0.1),
        checkmarkColor: const Color(0xFF1877F2),
        labelStyle: TextStyle(
          color: isSelected ? const Color(0xFF1877F2) : const Color(0xFF65676B),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          fontSize: 12,
        ),
      ),
    );
  }

  /// بناء بطاقة المنشور المحفوظ
  Widget _buildSavedPostCard(PostModel post, SavedItemsService service) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        leading: CircleAvatar(
          backgroundColor: const Color(0xFF1877F2),
          backgroundImage: post.authorAvatar.isNotEmpty 
              ? NetworkImage(post.authorAvatar) 
              : null,
          child: post.authorAvatar.isEmpty 
              ? Text(
                  post.authorName.isNotEmpty ? post.authorName[0].toUpperCase() : 'U',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Text(
          post.authorName,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Color(0xFF1C1E21),
            fontSize: 14,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              post.content,
              style: const TextStyle(
                color: Color(0xFF1C1E21),
                fontSize: 13,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 6),
            Row(
              children: [
                if (post.images.isNotEmpty) ...[
                  const Icon(Icons.image, size: 14, color: Color(0xFF65676B)),
                  const SizedBox(width: 4),
                  Text('${post.images.length}', style: const TextStyle(fontSize: 11)),
                  const SizedBox(width: 8),
                ],
                if (post.videos.isNotEmpty) ...[
                  const Icon(Icons.play_circle, size: 14, color: Color(0xFF65676B)),
                  const SizedBox(width: 4),
                  Text('${post.videos.length}', style: const TextStyle(fontSize: 11)),
                  const SizedBox(width: 8),
                ],
                Text(
                  post.timeAgo,
                  style: const TextStyle(
                    fontSize: 11,
                    color: Color(0xFF65676B),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: IconButton(
          onPressed: () {
            service.unsavePost(post.id);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Post removed from saved items'),
                backgroundColor: Colors.red,
              ),
            );
          },
          icon: const Icon(
            Icons.bookmark_remove,
            color: Colors.red,
            size: 20,
          ),
        ),
        onTap: () {
          // عرض المنشور الكامل
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Opening full post...'),
              backgroundColor: Color(0xFF1877F2),
            ),
          );
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80,
            color: Color(0xFF65676B),
          ),
          SizedBox(height: 16),
          Text(
            'No saved items yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1C1E21),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Save posts from social media to view them here',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF65676B),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// عرض حوار مسح الكل
  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Saved Items'),
        content: const Text('Are you sure you want to remove all saved items? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final service = Provider.of<SavedItemsService>(context, listen: false);
              service.clearAllSavedPosts();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All saved items cleared'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الإدارة
  void _showManageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Manage Saved Items'),
        content: const Text('Saved items management features will be available in a future update.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
