// import 'package:cloud_firestore/cloud_firestore.dart';

enum MessageType {
  text,
  image,
  voice,
  video,
  file,
  sticker,
}

class MessageModel {
  final String id;
  final String senderId;
  final String receiverId;
  final String message;
  final MessageType type;
  final DateTime timestamp;
  final bool isRead;
  final String? mediaUrl;
  final int? duration; // للرسائل الصوتية (بالثواني)
  final bool isEdited;
  final bool isDeleted;
  final bool isPinned;
  final bool? isStarred;
  final List<String> reactions;

  MessageModel({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.mediaUrl,
    this.duration,
    this.isEdited = false,
    this.isDeleted = false,
    this.isPinned = false,
    this.isStarred,
    this.reactions = const [],
  });

  // تحويل من Map إلى MessageModel
  factory MessageModel.fromMap(Map<String, dynamic> map, String documentId) {
    return MessageModel(
      id: documentId,
      senderId: map['senderId'] ?? '',
      receiverId: map['receiverId'] ?? '',
      message: map['message'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.toString().split('.').last == map['type'],
        orElse: () => MessageType.text,
      ),
      timestamp: DateTime.parse(map['timestamp'] ?? DateTime.now().toIso8601String()),
      isRead: map['isRead'] ?? false,
      mediaUrl: map['mediaUrl'],
      duration: map['duration'],
      isEdited: map['isEdited'] ?? false,
      isDeleted: map['isDeleted'] ?? false,
      isPinned: map['isPinned'] ?? false,
      isStarred: map['isStarred'],
      reactions: List<String>.from(map['reactions'] ?? []),
    );
  }

  // تحويل من MessageModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'senderId': senderId,
      'receiverId': receiverId,
      'message': message,
      'type': type.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'mediaUrl': mediaUrl,
      'duration': duration,
      'isEdited': isEdited,
      'isDeleted': isDeleted,
      'isPinned': isPinned,
      'isStarred': isStarred,
      'reactions': reactions,
    };
  }

  // نسخ MessageModel مع تعديل بعض الخصائص
  MessageModel copyWith({
    String? id,
    String? senderId,
    String? receiverId,
    String? message,
    MessageType? type,
    DateTime? timestamp,
    bool? isRead,
    String? mediaUrl,
    int? duration,
    bool? isEdited,
    bool? isDeleted,
    bool? isPinned,
    bool? isStarred,
    List<String>? reactions,
  }) {
    return MessageModel(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      duration: duration ?? this.duration,
      isEdited: isEdited ?? this.isEdited,
      isDeleted: isDeleted ?? this.isDeleted,
      isPinned: isPinned ?? this.isPinned,
      isStarred: isStarred ?? this.isStarred,
      reactions: reactions ?? this.reactions,
    );
  }

  // إنشاء معرف المحادثة من معرفي المرسل والمستقبل
  String get chatId {
    final List<String> ids = [senderId, receiverId];
    ids.sort();
    return ids.join('_');
  }

  @override
  String toString() {
    return 'MessageModel(id: $id, senderId: $senderId, receiverId: $receiverId, message: $message, type: $type, timestamp: $timestamp, isRead: $isRead, mediaUrl: $mediaUrl, duration: $duration)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageModel && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
