import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../services/notes_service.dart';
import '../../widgets/user_note_widget.dart';
import '../chat/chat_screen.dart';
import 'report_contact_screen.dart';

class UserProfileScreen extends StatelessWidget {
  final UserModel user;

  const UserProfileScreen({
    super.key,
    required this.user,
  });

  void _startChat(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChatScreen(otherUser: user),
      ),
    );
  }

  void _showBlockDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block Contact'),
        content: Text('Are you sure you want to block ${user.name}? You will no longer receive messages from this contact.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${user.name} has been blocked')),
              );
            },
            child: const Text('Block', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(user.name),
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'report':
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => ReportContactScreen(reportedUser: user),
                    ),
                  );
                  break;
                case 'block':
                  _showBlockDialog(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.report, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Report Contact'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'block',
                child: Row(
                  children: [
                    Icon(Icons.block, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Block Contact'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const SizedBox(height: 20),

            // ملاحظات المستخدم (فوق الملف الشخصي)
            Consumer<NotesService>(
              builder: (context, notesService, child) {
                final userNotes = notesService.getUserNotes(user.phoneNumber);

                if (userNotes.isNotEmpty) {
                  return Column(
                    children: [
                      // عرض الملاحظة الأحدث
                      UserNoteWidget(
                        note: userNotes.first,
                        showViewers: true,
                      ),
                      const SizedBox(height: 20),
                    ],
                  );
                }

                return const SizedBox.shrink();
              },
            ),

            // Profile Image
            CircleAvatar(
              radius: 80,
              backgroundColor: const Color(0xFFD32F2F),
              backgroundImage: user.profileImageUrl != null
                  ? NetworkImage(user.profileImageUrl!)
                  : null,
              child: user.profileImageUrl == null
                  ? Text(
                      user.name.isNotEmpty
                          ? user.name[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        fontSize: 50,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),

            const SizedBox(height: 20),

            // Name
            Text(
              user.name,
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Color(0xFFD32F2F),
              ),
            ),

            const SizedBox(height: 8),

            // Status
            if (user.status != null && user.status!.isNotEmpty)
              Text(
                user.status!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),

            const SizedBox(height: 8),

            // Online Status (with privacy check)
            if (user.privacySettings.showOnlineStatus || user.privacySettings.showLastSeen)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (user.privacySettings.showOnlineStatus)
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: user.isOnline ? Colors.green : Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                  if (user.privacySettings.showOnlineStatus)
                    const SizedBox(width: 8),
                  Text(
                    user.privacySettings.showOnlineStatus && user.isOnline
                        ? 'Online now'
                        : user.privacySettings.showLastSeen && !user.isOnline
                            ? 'Last seen: ${DateFormat('MMM dd, yyyy HH:mm').format(user.lastSeen)}'
                            : 'Status hidden',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              )
            else
              Text(
                'Status hidden',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),

            const SizedBox(height: 30),

            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Message Button
                Column(
                  children: [
                    FloatingActionButton(
                      onPressed: () => _startChat(context),
                      backgroundColor: const Color(0xFFD32F2F),
                      heroTag: "message",
                      child: const Icon(Icons.message, color: Colors.white),
                    ),
                    const SizedBox(height: 8),
                    const Text('Message', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 40),

            // User Details Card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'User Information',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFFD32F2F),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Phone Number (with privacy check)
                    if (user.privacySettings.showPhoneNumber)
                      _buildInfoRow(Icons.phone, 'Phone', user.phoneNumber)
                    else
                      _buildInfoRow(Icons.phone, 'Phone', 'Hidden'),
                    const SizedBox(height: 12),

                    // Gender (with privacy check)
                    if (user.privacySettings.showGender)
                      _buildInfoRow(
                        user.gender == Gender.male ? Icons.male : Icons.female,
                        'Gender',
                        user.gender == Gender.male ? 'Male' : 'Female',
                      )
                    else
                      _buildInfoRow(Icons.person_outline, 'Gender', 'Hidden'),
                    const SizedBox(height: 12),

                    // Age (with privacy check)
                    if (user.privacySettings.showAge)
                      _buildInfoRow(Icons.cake, 'Age', '${user.age} years old')
                    else
                      _buildInfoRow(Icons.cake, 'Age', 'Hidden'),
                    const SizedBox(height: 12),

                    // Location (with privacy check)
                    if (user.privacySettings.showLocation)
                      _buildInfoRow(Icons.location_on, 'Location', '${user.city}, ${user.country}')
                    else
                      _buildInfoRow(Icons.location_on, 'Location', 'Hidden'),
                    const SizedBox(height: 12),

                    // Member Since (with privacy check)
                    if (user.privacySettings.showRegistrationDate)
                      _buildInfoRow(
                        Icons.calendar_today,
                        'Member Since',
                        DateFormat('MMM dd, yyyy').format(user.registrationDate),
                      )
                    else
                      _buildInfoRow(Icons.calendar_today, 'Member Since', 'Hidden'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: const Color(0xFFD32F2F), size: 20),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }
}
