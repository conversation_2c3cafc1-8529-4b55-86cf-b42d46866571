/// نموذج معاينة الروابط
class LinkPreviewModel {
  final String url;
  final String? title;
  final String? description;
  final String? imageUrl;
  final String? siteName;
  final String? domain;
  final DateTime createdAt;

  const LinkPreviewModel({
    required this.url,
    this.title,
    this.description,
    this.imageUrl,
    this.siteName,
    this.domain,
    required this.createdAt,
  });

  /// إنشاء من Map
  factory LinkPreviewModel.fromMap(Map<String, dynamic> map) {
    return LinkPreviewModel(
      url: map['url'] ?? '',
      title: map['title'],
      description: map['description'],
      imageUrl: map['imageUrl'],
      siteName: map['siteName'],
      domain: map['domain'],
      createdAt: map['createdAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
          : DateTime.now(),
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'url': url,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'siteName': siteName,
      'domain': domain,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  /// نسخ مع تعديل
  LinkPreviewModel copyWith({
    String? url,
    String? title,
    String? description,
    String? imageUrl,
    String? siteName,
    String? domain,
    DateTime? createdAt,
  }) {
    return LinkPreviewModel(
      url: url ?? this.url,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      siteName: siteName ?? this.siteName,
      domain: domain ?? this.domain,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// التحقق من صحة الرابط
  bool get isValid => url.isNotEmpty && Uri.tryParse(url) != null;

  /// الحصول على النطاق من الرابط
  String get extractedDomain {
    if (domain != null) return domain!;
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return '';
    }
  }

  /// الحصول على العنوان المختصر
  String get shortTitle {
    if (title == null || title!.isEmpty) return extractedDomain;
    return title!.length > 60 ? '${title!.substring(0, 60)}...' : title!;
  }

  /// الحصول على الوصف المختصر
  String get shortDescription {
    if (description == null || description!.isEmpty) return '';
    return description!.length > 120 ? '${description!.substring(0, 120)}...' : description!;
  }

  @override
  String toString() {
    return 'LinkPreviewModel(url: $url, title: $title, domain: $extractedDomain)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LinkPreviewModel && other.url == url;
  }

  @override
  int get hashCode => url.hashCode;
}
