import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/ai_service.dart';

/// شاشة إعدادات الذكاء الاصطناعي
class AISettingsScreen extends StatefulWidget {
  const AISettingsScreen({super.key});

  @override
  State<AISettingsScreen> createState() => _AISettingsScreenState();
}

class _AISettingsScreenState extends State<AISettingsScreen> {
  final TextEditingController _testMessageController = TextEditingController();
  String _testResult = '';
  bool _isTranslatingToArabic = true;

  @override
  void dispose() {
    _testMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'AI Features',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFFD32F2F),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Consumer<AIService>(
        builder: (context, aiService, child) {
          final stats = aiService.getAIStats();
          
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // إحصائيات الذكاء الاصطناعي
              _buildStatsCard(stats),
              
              const SizedBox(height: 24),
              
              // اختبار الردود الذكية
              _buildSmartRepliesTest(aiService),
              
              const SizedBox(height: 24),
              
              // اختبار الترجمة
              _buildTranslationTest(aiService),
              
              const SizedBox(height: 24),
              
              // اختبار تحليل المشاعر
              _buildEmotionAnalysisTest(aiService),
              
              const SizedBox(height: 24),
              
              // اختبار توقع النص
              _buildTextPredictionTest(aiService),
              
              const SizedBox(height: 24),
              
              // معلومات الميزات
              _buildFeaturesInfo(),
            ],
          );
        },
      ),
    );
  }

  /// بناء بطاقة الإحصائيات
  Widget _buildStatsCard(Map<String, dynamic> stats) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: const Color(0xFFD32F2F),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'AI Statistics',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Smart Replies',
                    '${stats['smartRepliesCount']}',
                    Icons.reply,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Emotions',
                    '${stats['emotionsCount']}',
                    Icons.sentiment_satisfied,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Translations',
                    '${stats['translationsCount']}',
                    Icons.translate,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Predictions',
                    '${stats['predictionsCount']}',
                    Icons.psychology,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFFD32F2F),
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFFD32F2F),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء اختبار الردود الذكية
  Widget _buildSmartRepliesTest(AIService aiService) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.reply,
                  color: const Color(0xFFD32F2F),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Smart Replies Test',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _testMessageController,
              decoration: const InputDecoration(
                hintText: 'Enter a message to get smart replies...',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () => _testSmartReplies(aiService),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFD32F2F),
                foregroundColor: Colors.white,
              ),
              child: const Text('Get Smart Replies'),
            ),
            if (_testResult.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _testResult,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء اختبار الترجمة
  Widget _buildTranslationTest(AIService aiService) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.translate,
                  color: const Color(0xFFD32F2F),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Translation Test',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('Translate to: '),
                Switch(
                  value: _isTranslatingToArabic,
                  onChanged: (value) {
                    setState(() {
                      _isTranslatingToArabic = value;
                    });
                  },
                  activeColor: const Color(0xFFD32F2F),
                ),
                Text(_isTranslatingToArabic ? 'Arabic' : 'English'),
              ],
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () => _testTranslation(aiService),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFD32F2F),
                foregroundColor: Colors.white,
              ),
              child: const Text('Translate Message'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء اختبار تحليل المشاعر
  Widget _buildEmotionAnalysisTest(AIService aiService) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.sentiment_satisfied,
                  color: const Color(0xFFD32F2F),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Emotion Analysis Test',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _testEmotionAnalysis(aiService),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFD32F2F),
                foregroundColor: Colors.white,
              ),
              child: const Text('Analyze Emotions'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء اختبار توقع النص
  Widget _buildTextPredictionTest(AIService aiService) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.psychology,
                  color: const Color(0xFFD32F2F),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Text Prediction Test',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _testTextPrediction(aiService),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFD32F2F),
                foregroundColor: Colors.white,
              ),
              child: const Text('Get Text Predictions'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الميزات
  Widget _buildFeaturesInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: const Color(0xFFD32F2F).withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(0xFFD32F2F),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'AI Features',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFD32F2F),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• Smart Replies: Get intelligent response suggestions\n'
              '• Translation: Translate messages between English and Arabic\n'
              '• Emotion Analysis: Understand the sentiment of messages\n'
              '• Text Prediction: Get word and phrase suggestions while typing',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// اختبار الردود الذكية
  void _testSmartReplies(AIService aiService) {
    final message = _testMessageController.text.trim();
    if (message.isEmpty) {
      setState(() {
        _testResult = 'Please enter a message first.';
      });
      return;
    }

    final replies = aiService.getSuggestedReplies(message);
    setState(() {
      _testResult = 'Smart Replies:\n${replies.map((r) => '• $r').join('\n')}';
    });
  }

  /// اختبار الترجمة
  void _testTranslation(AIService aiService) {
    final message = _testMessageController.text.trim();
    if (message.isEmpty) {
      setState(() {
        _testResult = 'Please enter a message first.';
      });
      return;
    }

    final translation = aiService.translateMessage(message, toArabic: _isTranslatingToArabic);
    setState(() {
      _testResult = 'Translation:\n$translation';
    });
  }

  /// اختبار تحليل المشاعر
  void _testEmotionAnalysis(AIService aiService) {
    final message = _testMessageController.text.trim();
    if (message.isEmpty) {
      setState(() {
        _testResult = 'Please enter a message first.';
      });
      return;
    }

    final analysis = aiService.analyzeEmotion(message);
    setState(() {
      _testResult = 'Emotion Analysis:\n'
          'Emotion: ${analysis['emotion']} ${analysis['emoji']}\n'
          'Confidence: ${(analysis['confidence'] * 100).toStringAsFixed(1)}%\n'
          'Detected words: ${analysis['detectedWords'].join(', ')}';
    });
  }

  /// اختبار توقع النص
  void _testTextPrediction(AIService aiService) {
    final message = _testMessageController.text.trim();
    if (message.isEmpty) {
      setState(() {
        _testResult = 'Please enter a message first.';
      });
      return;
    }

    final predictions = aiService.predictText(message);
    setState(() {
      _testResult = 'Text Predictions:\n${predictions.map((p) => '• $p').join('\n')}';
    });
  }
}
