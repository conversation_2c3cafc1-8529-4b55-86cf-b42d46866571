#!/usr/bin/env python3
"""
إنشاء أيقونة حمراء احترافية للدردشة
"""

try:
    from PIL import Image, ImageDraw
    import os
    
    def create_red_chat_icon():
        # إنشاء صورة 512x512 بخلفية شفافة
        size = 512
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # الألوان الحمراء
        red_main = (211, 47, 47)      # أحمر رئيسي
        red_dark = (183, 28, 28)      # أحمر غامق
        red_light = (239, 83, 80)     # أحمر فاتح
        white = (255, 255, 255)
        
        # رسم الخلفية الدائرية
        center = size // 2
        radius = 240
        
        # رسم دائرة حمراء متدرجة
        for i in range(radius, 0, -1):
            alpha = int(255 * (i / radius))
            color = (*red_main, alpha)
            draw.ellipse([center-i, center-i, center+i, center+i], fill=color)
        
        # رسم فقاعة الدردشة الكبيرة
        bubble_coords = [center-80, center-60, center+80, center+40]
        draw.rounded_rectangle(bubble_coords, radius=20, fill=white)
        
        # رسم ذيل الفقاعة
        tail_points = [
            (center-80, center+40),
            (center-100, center+60),
            (center-80, center+60)
        ]
        draw.polygon(tail_points, fill=white)
        
        # رسم فقاعة صغيرة
        small_bubble = [center+20, center-20, center+70, center+10]
        draw.rounded_rectangle(small_bubble, radius=10, fill=(255, 235, 235))
        
        # رسم نقاط الكتابة
        dot_y = center - 20
        for i, x_offset in enumerate([-40, -20, 0]):
            dot_x = center + x_offset
            dot_color = [red_dark, red_main, red_light][i]
            draw.ellipse([dot_x-5, dot_y-5, dot_x+5, dot_y+5], fill=dot_color)
        
        # رسم خطوط النص
        line_color = (*red_main, 150)
        draw.rectangle([center-60, center, center+40, center+8], fill=line_color)
        draw.rectangle([center-60, center+15, center+20, center+23], fill=line_color)
        
        # حفظ الأيقونة
        img.save('red_chat_icon.png', 'PNG')
        print("✅ تم إنشاء الأيقونة الحمراء بنجاح!")
        return True
        
    except ImportError:
        print("❌ مكتبة PIL غير متوفرة")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    create_red_chat_icon()
