import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// شاشة إعدادات الوسائط والتخزين
class MediaSettingsScreen extends StatefulWidget {
  const MediaSettingsScreen({super.key});

  @override
  State<MediaSettingsScreen> createState() => _MediaSettingsScreenState();
}

class _MediaSettingsScreenState extends State<MediaSettingsScreen> {
  // إعدادات التحميل التلقائي
  bool _autoDownloadImages = true;
  bool _autoDownloadVideos = false;
  bool _autoDownloadAudio = true;
  bool _autoDownloadDocuments = false;
  
  // إعدادات جودة الوسائط
  String _imageQuality = 'High';
  String _videoQuality = 'Medium';
  String _compressionLevel = 'Medium';
  
  // إعدادات التخزين
  bool _saveToGallery = true;
  bool _deleteAfterSending = false;
  String _storageLocation = 'Internal Storage';
  
  // إحصائيات التخزين
  double _totalStorage = 0.0;
  double _imagesStorage = 0.0;
  double _videosStorage = 0.0;
  double _audioStorage = 0.0;
  double _documentsStorage = 0.0;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _calculateStorageUsage();
  }

  /// تحميل الإعدادات المحفوظة
  void _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _autoDownloadImages = prefs.getBool('auto_download_images') ?? true;
      _autoDownloadVideos = prefs.getBool('auto_download_videos') ?? false;
      _autoDownloadAudio = prefs.getBool('auto_download_audio') ?? true;
      _autoDownloadDocuments = prefs.getBool('auto_download_documents') ?? false;
      _imageQuality = prefs.getString('image_quality') ?? 'High';
      _videoQuality = prefs.getString('video_quality') ?? 'Medium';
      _compressionLevel = prefs.getString('compression_level') ?? 'Medium';
      _saveToGallery = prefs.getBool('save_to_gallery') ?? true;
      _deleteAfterSending = prefs.getBool('delete_after_sending') ?? false;
      _storageLocation = prefs.getString('storage_location') ?? 'Internal Storage';
    });
  }

  /// حفظ الإعدادات
  void _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('auto_download_images', _autoDownloadImages);
    await prefs.setBool('auto_download_videos', _autoDownloadVideos);
    await prefs.setBool('auto_download_audio', _autoDownloadAudio);
    await prefs.setBool('auto_download_documents', _autoDownloadDocuments);
    await prefs.setString('image_quality', _imageQuality);
    await prefs.setString('video_quality', _videoQuality);
    await prefs.setString('compression_level', _compressionLevel);
    await prefs.setBool('save_to_gallery', _saveToGallery);
    await prefs.setBool('delete_after_sending', _deleteAfterSending);
    await prefs.setString('storage_location', _storageLocation);
  }

  /// حساب استخدام التخزين
  void _calculateStorageUsage() {
    // محاكاة حساب استخدام التخزين
    setState(() {
      _imagesStorage = 245.6; // MB
      _videosStorage = 1024.3; // MB
      _audioStorage = 89.2; // MB
      _documentsStorage = 156.8; // MB
      _totalStorage = _imagesStorage + _videosStorage + _audioStorage + _documentsStorage;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Media & Storage',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF42B883),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // إحصائيات التخزين
            _buildStorageCard(),

            const SizedBox(height: 16),

            // إعدادات التحميل التلقائي
            _buildSectionCard(
              title: 'Auto Download',
              icon: Icons.download,
              children: [
                _buildSwitchTile(
                  title: 'Images',
                  subtitle: 'Automatically download images',
                  value: _autoDownloadImages,
                  onChanged: (value) {
                    setState(() => _autoDownloadImages = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Videos',
                  subtitle: 'Automatically download videos',
                  value: _autoDownloadVideos,
                  onChanged: (value) {
                    setState(() => _autoDownloadVideos = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Audio',
                  subtitle: 'Automatically download audio files',
                  value: _autoDownloadAudio,
                  onChanged: (value) {
                    setState(() => _autoDownloadAudio = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Documents',
                  subtitle: 'Automatically download documents',
                  value: _autoDownloadDocuments,
                  onChanged: (value) {
                    setState(() => _autoDownloadDocuments = value);
                    _saveSettings();
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات جودة الوسائط
            _buildSectionCard(
              title: 'Media Quality',
              icon: Icons.high_quality,
              children: [
                _buildListTile(
                  title: 'Image Quality',
                  subtitle: _imageQuality,
                  onTap: () => _selectImageQuality(),
                ),
                _buildListTile(
                  title: 'Video Quality',
                  subtitle: _videoQuality,
                  onTap: () => _selectVideoQuality(),
                ),
                _buildListTile(
                  title: 'Compression Level',
                  subtitle: _compressionLevel,
                  onTap: () => _selectCompressionLevel(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات التخزين
            _buildSectionCard(
              title: 'Storage Settings',
              icon: Icons.storage,
              children: [
                _buildSwitchTile(
                  title: 'Save to Gallery',
                  subtitle: 'Save received media to device gallery',
                  value: _saveToGallery,
                  onChanged: (value) {
                    setState(() => _saveToGallery = value);
                    _saveSettings();
                  },
                ),
                _buildSwitchTile(
                  title: 'Delete After Sending',
                  subtitle: 'Delete media after successful upload',
                  value: _deleteAfterSending,
                  onChanged: (value) {
                    setState(() => _deleteAfterSending = value);
                    _saveSettings();
                  },
                ),
                _buildListTile(
                  title: 'Storage Location',
                  subtitle: _storageLocation,
                  onTap: () => _selectStorageLocation(),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // أزرار إدارة التخزين
            _buildManagementButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائيات التخزين
  Widget _buildStorageCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.pie_chart, color: const Color(0xFF42B883)),
              const SizedBox(width: 12),
              const Text(
                'Storage Usage',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF42B883),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // إجمالي الاستخدام
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total Used:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              Text(
                '${_totalStorage.toStringAsFixed(1)} MB',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF42B883),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // تفاصيل الاستخدام
          _buildStorageItem('Images', _imagesStorage, Colors.blue),
          _buildStorageItem('Videos', _videosStorage, Colors.red),
          _buildStorageItem('Audio', _audioStorage, Colors.orange),
          _buildStorageItem('Documents', _documentsStorage, Colors.purple),
        ],
      ),
    );
  }

  /// بناء عنصر استخدام التخزين
  Widget _buildStorageItem(String type, double size, Color color) {
    final percentage = (size / _totalStorage * 100);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              type,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Text(
            '${size.toStringAsFixed(1)} MB (${percentage.toStringAsFixed(1)}%)',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF42B883).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF42B883)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF42B883),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// بناء مفتاح تبديل
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: const Color(0xFF42B883),
    );
  }

  /// بناء عنصر قائمة
  Widget _buildListTile({
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[600],
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey[400],
        size: 16,
      ),
      onTap: onTap,
    );
  }

  /// بناء أزرار إدارة التخزين
  Widget _buildManagementButtons() {
    return Column(
      children: [
        // زر مسح الذاكرة التخزين المؤقت
        Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 12),
          child: ElevatedButton.icon(
            onPressed: _clearCache,
            icon: const Icon(Icons.cleaning_services),
            label: const Text('Clear Cache'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        
        // زر مسح جميع الوسائط
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _clearAllMedia,
            icon: const Icon(Icons.delete_forever),
            label: const Text('Clear All Media'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// اختيار جودة الصور
  void _selectImageQuality() {
    final qualities = ['Low', 'Medium', 'High', 'Original'];
    _showSelectionDialog('Select Image Quality', qualities, _imageQuality, (value) {
      setState(() => _imageQuality = value);
      _saveSettings();
    });
  }

  /// اختيار جودة الفيديو
  void _selectVideoQuality() {
    final qualities = ['Low', 'Medium', 'High', 'Original'];
    _showSelectionDialog('Select Video Quality', qualities, _videoQuality, (value) {
      setState(() => _videoQuality = value);
      _saveSettings();
    });
  }

  /// اختيار مستوى الضغط
  void _selectCompressionLevel() {
    final levels = ['Low', 'Medium', 'High'];
    _showSelectionDialog('Select Compression Level', levels, _compressionLevel, (value) {
      setState(() => _compressionLevel = value);
      _saveSettings();
    });
  }

  /// اختيار موقع التخزين
  void _selectStorageLocation() {
    final locations = ['Internal Storage', 'SD Card', 'Cloud Storage'];
    _showSelectionDialog('Select Storage Location', locations, _storageLocation, (value) {
      setState(() => _storageLocation = value);
      _saveSettings();
    });
  }

  /// عرض حوار الاختيار
  void _showSelectionDialog(String title, List<String> options, String currentValue, Function(String) onSelected) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: options.map((option) => RadioListTile<String>(
            title: Text(option),
            value: option,
            groupValue: currentValue,
            onChanged: (value) {
              onSelected(value!);
              Navigator.of(context).pop();
            },
            activeColor: const Color(0xFF42B883),
          )).toList(),
        ),
      ),
    );
  }

  /// مسح الذاكرة التخزين المؤقت
  void _clearCache() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('This will clear temporary files and free up space. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Cache cleared successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  /// مسح جميع الوسائط
  void _clearAllMedia() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Media'),
        content: const Text('This will permanently delete all downloaded media files. This action cannot be undone. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All media cleared successfully'),
                  backgroundColor: Colors.red,
                ),
              );
              // إعادة حساب استخدام التخزين
              setState(() {
                _imagesStorage = 0;
                _videosStorage = 0;
                _audioStorage = 0;
                _documentsStorage = 0;
                _totalStorage = 0;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );
  }
}
