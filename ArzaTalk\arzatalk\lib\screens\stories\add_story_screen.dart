import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../services/story_service.dart';
import '../../services/auth_service.dart';
import 'add_text_story_screen.dart';

/// شاشة إضافة قصة جديدة
class AddStoryScreen extends StatefulWidget {
  const AddStoryScreen({super.key});

  @override
  State<AddStoryScreen> createState() => _AddStoryScreenState();
}

class _AddStoryScreenState extends State<AddStoryScreen> {
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.white),
        ),
        title: const Text(
          'Add Story',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFD32F2F),
              ),
            )
          : Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // أيقونة القصص
                  const Icon(
                    Icons.auto_stories,
                    size: 100,
                    color: Colors.white,
                  ),

                  const SizedBox(height: 40),

                  const Text(
                    'Share your story',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  const Text(
                    'Choose how you want to share your moment',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 60),

                  // خيارات إضافة القصة
                  _buildStoryOption(
                    icon: Icons.text_fields,
                    title: 'Text Story',
                    subtitle: 'Share your thoughts with text',
                    color: const Color(0xFFD32F2F),
                    onTap: _addTextStory,
                  ),

                  const SizedBox(height: 20),

                  _buildStoryOption(
                    icon: Icons.photo_camera,
                    title: 'Take Photo',
                    subtitle: 'Capture a moment with camera',
                    color: const Color(0xFF4CAF50),
                    onTap: _takePhoto,
                  ),

                  const SizedBox(height: 20),

                  _buildStoryOption(
                    icon: Icons.photo_library,
                    title: 'Choose Photo',
                    subtitle: 'Select from your gallery',
                    color: const Color(0xFF2196F3),
                    onTap: _choosePhoto,
                  ),

                  const SizedBox(height: 20),

                  _buildStoryOption(
                    icon: Icons.videocam,
                    title: 'Record Video',
                    subtitle: 'Capture a video moment',
                    color: const Color(0xFF9C27B0),
                    onTap: _recordVideo,
                  ),

                  const SizedBox(height: 20),

                  _buildStoryOption(
                    icon: Icons.video_library,
                    title: 'Choose Video',
                    subtitle: 'Select video from gallery',
                    color: const Color(0xFFFF9800),
                    onTap: _chooseVideo,
                  ),
                ],
              ),
            ),
    );
  }

  /// بناء خيار القصة
  Widget _buildStoryOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return SizedBox(
      width: double.infinity,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withOpacity(0.5),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// إضافة قصة نصية
  void _addTextStory() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddTextStoryScreen(),
      ),
    );
  }

  /// التقاط صورة
  Future<void> _takePhoto() async {
    try {
      setState(() => _isLoading = true);

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        // الحصول على بيانات المستخدم الحالي
        final authService = Provider.of<AuthService>(context, listen: false);
        final currentUser = authService.currentUser;

        if (currentUser != null) {
          final success = await Provider.of<StoryService>(context, listen: false)
              .addImageStory(currentUser.phoneNumber, currentUser.name);

          if (success && mounted) {
            Navigator.of(context).pop(); // العودة لشاشة القصص
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('📸 Photo story added successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Failed to add photo story'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ User not authenticated'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error taking photo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// اختيار صورة
  Future<void> _choosePhoto() async {
    try {
      setState(() => _isLoading = true);

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null && mounted) {
        // الحصول على بيانات المستخدم الحالي
        final authService = Provider.of<AuthService>(context, listen: false);
        final storyService = Provider.of<StoryService>(context, listen: false);
        final currentUser = authService.currentUser;

        if (currentUser != null) {
          final success = await storyService.addImageStory(
            currentUser.phoneNumber,
            currentUser.name
          );

          if (success && mounted) {
            Navigator.of(context).pop(); // العودة لشاشة القصص
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('🖼️ Photo story added successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Failed to add photo story'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ User not authenticated'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error choosing photo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// تسجيل فيديو
  Future<void> _recordVideo() async {
    try {
      setState(() => _isLoading = true);

      final XFile? video = await _picker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(seconds: 30),
      );

      if (video != null && mounted) {
        // الحصول على بيانات المستخدم الحالي
        final authService = Provider.of<AuthService>(context, listen: false);
        final storyService = Provider.of<StoryService>(context, listen: false);
        final currentUser = authService.currentUser;

        if (currentUser != null) {
          final success = await storyService.addVideoStory(
            currentUser.phoneNumber,
            currentUser.name
          );

          if (success && mounted) {
            Navigator.of(context).pop(); // العودة لشاشة القصص
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('🎥 Video story added successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Failed to add video story'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ User not authenticated'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recording video: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// اختيار فيديو
  Future<void> _chooseVideo() async {
    try {
      setState(() => _isLoading = true);

      final XFile? video = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(seconds: 30),
      );

      if (video != null && mounted) {
        // الحصول على بيانات المستخدم الحالي
        final authService = Provider.of<AuthService>(context, listen: false);
        final storyService = Provider.of<StoryService>(context, listen: false);
        final currentUser = authService.currentUser;

        if (currentUser != null) {
          final success = await storyService.addVideoStory(
            currentUser.phoneNumber,
            currentUser.name
          );

          if (success && mounted) {
            Navigator.of(context).pop(); // العودة لشاشة القصص
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('🎥 Video story added successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Failed to add video story'),
                backgroundColor: Colors.red,
              ),
            );
          }
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ User not authenticated'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error choosing video: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
