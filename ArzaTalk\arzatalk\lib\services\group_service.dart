import 'package:flutter/material.dart';
import '../models/group_model.dart';
import '../models/message_model.dart';
import 'sound_service.dart';

class GroupService extends ChangeNotifier {
  static final List<GroupModel> _groups = [];
  static final Map<String, List<MessageModel>> _groupMessages = {};
  final SoundService _soundService = SoundService();

  List<GroupModel> get groups => _groups;

  // Create a new group
  Future<String> createGroup({
    required String name,
    String? description,
    String? groupImageUrl,
    required List<String> members,
    required String createdBy,
  }) async {
    try {
      final groupId = 'group_${DateTime.now().millisecondsSinceEpoch}';

      final group = GroupModel(
        id: groupId,
        name: name,
        description: description,
        groupImageUrl: groupImageUrl,
        members: [...members, createdBy], // Add creator to members
        admins: [createdBy], // Creator is admin
        createdBy: createdBy,
        createdAt: DateTime.now(),
        lastMessageTime: DateTime.now(),
        lastMessage: 'Group created',
      );

      _groups.add(group);
      _groupMessages[groupId] = [];

      notifyListeners();
      return groupId;
    } catch (e) {
      throw Exception('Failed to create group: $e');
    }
  }

  // Get groups for a user
  List<GroupModel> getUserGroups(String userPhoneNumber) {
    return _groups.where((group) =>
      group.members.contains(userPhoneNumber)
    ).toList()..sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));
  }

  // Get all groups (for suggestions)
  List<GroupModel> getAllGroups() {
    return List.from(_groups);
  }

  // Get group by ID
  GroupModel? getGroupById(String groupId) {
    try {
      return _groups.firstWhere((group) => group.id == groupId);
    } catch (e) {
      return null;
    }
  }

  // Send message to group
  Future<void> sendGroupMessage({
    required String groupId,
    required String senderId,
    required String message,
    required MessageType type,
    String? mediaUrl,
  }) async {
    try {
      final messageId = 'msg_${DateTime.now().millisecondsSinceEpoch}';
      final newMessage = MessageModel(
        id: messageId,
        senderId: senderId,
        receiverId: groupId, // Group ID as receiver
        message: message,
        type: type,
        timestamp: DateTime.now(),
        isRead: false,
        mediaUrl: mediaUrl,
      );

      if (_groupMessages[groupId] == null) {
        _groupMessages[groupId] = [];
      }

      _groupMessages[groupId]!.insert(0, newMessage);

      // Update group's last message
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        _groups[groupIndex] = _groups[groupIndex].copyWith(
          lastMessage: message,
          lastMessageTime: DateTime.now(),
        );
      }

      notifyListeners();

      // تشغيل صوت إرسال رسالة المجموعة
      _soundService.playGroupNotificationSound();
    } catch (e) {
      throw Exception('Failed to send group message: $e');
    }
  }

  // Get group messages
  Stream<List<MessageModel>> getGroupMessages(String groupId) {
    return Stream.periodic(const Duration(seconds: 1), (_) {
      return _groupMessages[groupId] ?? [];
    });
  }

  // Add member to group
  Future<void> addMemberToGroup(String groupId, String memberPhoneNumber) async {
    try {
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        final group = _groups[groupIndex];
        if (!group.members.contains(memberPhoneNumber)) {
          final updatedMembers = [...group.members, memberPhoneNumber];
          _groups[groupIndex] = group.copyWith(members: updatedMembers);
          notifyListeners();
        }
      }
    } catch (e) {
      throw Exception('Failed to add member: $e');
    }
  }

  // Remove member from group
  Future<void> removeMemberFromGroup(String groupId, String memberPhoneNumber) async {
    try {
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        final group = _groups[groupIndex];
        final updatedMembers = group.members.where((m) => m != memberPhoneNumber).toList();
        _groups[groupIndex] = group.copyWith(members: updatedMembers);
        notifyListeners();
      }
    } catch (e) {
      throw Exception('Failed to remove member: $e');
    }
  }

  // Make user admin
  Future<void> makeAdmin(String groupId, String memberPhoneNumber) async {
    try {
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        final group = _groups[groupIndex];
        if (group.members.contains(memberPhoneNumber) && !group.admins.contains(memberPhoneNumber)) {
          final updatedAdmins = [...group.admins, memberPhoneNumber];
          _groups[groupIndex] = group.copyWith(admins: updatedAdmins);
          notifyListeners();
        }
      }
    } catch (e) {
      throw Exception('Failed to make admin: $e');
    }
  }

  // Remove admin
  Future<void> removeAdmin(String groupId, String memberPhoneNumber) async {
    try {
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        final group = _groups[groupIndex];
        if (group.admins.length > 1) { // Keep at least one admin
          final updatedAdmins = group.admins.where((a) => a != memberPhoneNumber).toList();
          _groups[groupIndex] = group.copyWith(admins: updatedAdmins);
          notifyListeners();
        }
      }
    } catch (e) {
      throw Exception('Failed to remove admin: $e');
    }
  }

  // Update group info
  Future<void> updateGroupInfo({
    required String groupId,
    String? name,
    String? description,
    String? groupImageUrl,
  }) async {
    try {
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        _groups[groupIndex] = _groups[groupIndex].copyWith(
          name: name,
          description: description,
          groupImageUrl: groupImageUrl,
        );
        notifyListeners();
      }
    } catch (e) {
      throw Exception('Failed to update group info: $e');
    }
  }

  // Delete group
  Future<void> deleteGroup(String groupId) async {
    try {
      _groups.removeWhere((g) => g.id == groupId);
      _groupMessages.remove(groupId);
      notifyListeners();
    } catch (e) {
      throw Exception('Failed to delete group: $e');
    }
  }

  // Leave group
  Future<void> leaveGroup(String groupId, String userPhoneNumber) async {
    try {
      await removeMemberFromGroup(groupId, userPhoneNumber);

      // If user was admin and only admin, make someone else admin
      final group = getGroupById(groupId);
      if (group != null && group.admins.contains(userPhoneNumber) && group.admins.length == 1) {
        if (group.members.isNotEmpty) {
          await makeAdmin(groupId, group.members.first);
        }
      }
    } catch (e) {
      throw Exception('Failed to leave group: $e');
    }
  }

  // Mute/unmute group
  Future<void> toggleGroupMute(String groupId) async {
    try {
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        _groups[groupIndex] = _groups[groupIndex].copyWith(
          isMuted: !_groups[groupIndex].isMuted,
        );
        notifyListeners();
      }
    } catch (e) {
      throw Exception('Failed to toggle group mute: $e');
    }
  }

  // Archive/unarchive group
  Future<void> toggleGroupArchive(String groupId) async {
    try {
      final groupIndex = _groups.indexWhere((g) => g.id == groupId);
      if (groupIndex != -1) {
        _groups[groupIndex] = _groups[groupIndex].copyWith(
          isArchived: !_groups[groupIndex].isArchived,
        );
        notifyListeners();
      }
    } catch (e) {
      throw Exception('Failed to toggle group archive: $e');
    }
  }

  // Clear group messages
  Future<void> clearGroupMessages(String groupId) async {
    try {
      _groupMessages[groupId] = [];
      notifyListeners();
    } catch (e) {
      throw Exception('Failed to clear group messages: $e');
    }
  }

  // Edit group message
  Future<void> editGroupMessage(String messageId, String newMessage) async {
    try {
      for (final groupId in _groupMessages.keys) {
        final messages = _groupMessages[groupId]!;
        final messageIndex = messages.indexWhere((msg) => msg.id == messageId);
        if (messageIndex != -1) {
          _groupMessages[groupId]![messageIndex] = messages[messageIndex].copyWith(
            message: newMessage,
            isEdited: true,
          );
          notifyListeners();

          // تشغيل صوت تحرير الرسالة
          _soundService.playMessageEditedSound();
          return;
        }
      }
    } catch (e) {
      throw Exception('Failed to edit group message: $e');
    }
  }

  // Delete group message
  Future<void> deleteGroupMessage(String messageId) async {
    try {
      for (final groupId in _groupMessages.keys) {
        final messages = _groupMessages[groupId]!;
        final messageIndex = messages.indexWhere((msg) => msg.id == messageId);
        if (messageIndex != -1) {
          _groupMessages[groupId]![messageIndex] = messages[messageIndex].copyWith(
            message: '',
            isDeleted: true,
          );
          notifyListeners();

          // تشغيل صوت حذف الرسالة
          _soundService.playMessageDeletedSound();
          return;
        }
      }
    } catch (e) {
      throw Exception('Failed to delete group message: $e');
    }
  }

  // Add demo groups
  static void addDemoGroups() {
    final demoGroups = [
      GroupModel(
        id: 'group_1',
        name: 'Family Group',
        description: 'Family chat group',
        members: ['+1234567890', '+1987654321', '+1555123456'],
        admins: ['+1234567890'],
        createdBy: '+1234567890',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        lastMessageTime: DateTime.now().subtract(const Duration(minutes: 30)),
        lastMessage: 'Hello everyone!',
      ),
      GroupModel(
        id: 'group_2',
        name: 'Work Team',
        description: 'Project discussion',
        members: ['+1234567890', '+1777888999', '+1555123456'],
        admins: ['+1234567890', '+1777888999'],
        createdBy: '+1234567890',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        lastMessageTime: DateTime.now().subtract(const Duration(hours: 2)),
        lastMessage: 'Meeting at 3 PM',
      ),
    ];

    for (final group in demoGroups) {
      if (!_groups.any((g) => g.id == group.id)) {
        _groups.add(group);
        _groupMessages[group.id] = [];
      }
    }
  }
}
