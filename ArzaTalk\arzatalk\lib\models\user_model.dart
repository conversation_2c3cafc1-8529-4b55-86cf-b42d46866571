// import 'package:cloud_firestore/cloud_firestore.dart';
import 'privacy_settings_model.dart';

enum Gender { male, female }

class UserModel {
  final String phoneNumber;
  final String name;
  final String? profileImageUrl;
  final String? status;
  final DateTime lastSeen;
  final bool isOnline;
  final Gender gender;
  final int age;
  final String country;
  final String city;
  final DateTime registrationDate;
  final PrivacySettings privacySettings;

  UserModel({
    required this.phoneNumber,
    required this.name,
    this.profileImageUrl,
    this.status,
    required this.lastSeen,
    this.isOnline = false,
    required this.gender,
    required this.age,
    required this.country,
    required this.city,
    required this.registrationDate,
    this.privacySettings = const PrivacySettings(),
  });

  // تحويل من Map إلى UserModel
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      phoneNumber: map['phoneNumber'] ?? '',
      name: map['name'] ?? '',
      profileImageUrl: map['profileImageUrl'],
      status: map['status'],
      lastSeen: DateTime.parse(map['lastSeen'] ?? DateTime.now().toIso8601String()),
      isOnline: map['isOnline'] ?? false,
      gender: Gender.values.firstWhere(
        (e) => e.toString() == 'Gender.${map['gender']}',
        orElse: () => Gender.male,
      ),
      age: map['age'] ?? 18,
      country: map['country'] ?? '',
      city: map['city'] ?? '',
      registrationDate: DateTime.parse(map['registrationDate'] ?? DateTime.now().toIso8601String()),
    );
  }

  // تحويل من UserModel إلى Map
  Map<String, dynamic> toMap() {
    return {
      'phoneNumber': phoneNumber,
      'name': name,
      'profileImageUrl': profileImageUrl,
      'status': status,
      'lastSeen': lastSeen.toIso8601String(),
      'isOnline': isOnline,
      'gender': gender.toString().split('.').last,
      'age': age,
      'country': country,
      'city': city,
      'registrationDate': registrationDate.toIso8601String(),
    };
  }

  // نسخ UserModel مع تعديل بعض الخصائص
  UserModel copyWith({
    String? phoneNumber,
    String? name,
    String? profileImageUrl,
    String? status,
    DateTime? lastSeen,
    bool? isOnline,
    Gender? gender,
    int? age,
    String? country,
    String? city,
    DateTime? registrationDate,
    PrivacySettings? privacySettings,
  }) {
    return UserModel(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      name: name ?? this.name,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      status: status ?? this.status,
      lastSeen: lastSeen ?? this.lastSeen,
      isOnline: isOnline ?? this.isOnline,
      gender: gender ?? this.gender,
      age: age ?? this.age,
      country: country ?? this.country,
      city: city ?? this.city,
      registrationDate: registrationDate ?? this.registrationDate,
      privacySettings: privacySettings ?? this.privacySettings,
    );
  }

  @override
  String toString() {
    return 'UserModel(phoneNumber: $phoneNumber, name: $name, profileImageUrl: $profileImageUrl, lastSeen: $lastSeen, isOnline: $isOnline)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.phoneNumber == phoneNumber;
  }

  @override
  int get hashCode {
    return phoneNumber.hashCode;
  }
}
