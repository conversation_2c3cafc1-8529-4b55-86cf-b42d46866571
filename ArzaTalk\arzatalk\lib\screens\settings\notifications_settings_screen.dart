import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// شاشة إعدادات الإشعارات
class NotificationsSettingsScreen extends StatefulWidget {
  const NotificationsSettingsScreen({super.key});

  @override
  State<NotificationsSettingsScreen> createState() => _NotificationsSettingsScreenState();
}

class _NotificationsSettingsScreenState extends State<NotificationsSettingsScreen> {
  // إعدادات الإشعارات
  bool _allNotifications = true;
  bool _messageNotifications = true;
  bool _socialNotifications = true;
  bool _storyNotifications = true;
  bool _groupNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _ledEnabled = true;
  bool _showPreview = true;
  String _notificationTone = 'Default';
  String _quietHoursStart = '22:00';
  String _quietHoursEnd = '08:00';
  bool _quietHoursEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات المحفوظة
  void _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _allNotifications = prefs.getBool('all_notifications') ?? true;
      _messageNotifications = prefs.getBool('message_notifications') ?? true;
      _socialNotifications = prefs.getBool('social_notifications') ?? true;
      _storyNotifications = prefs.getBool('story_notifications') ?? true;
      _groupNotifications = prefs.getBool('group_notifications') ?? true;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
      _ledEnabled = prefs.getBool('led_enabled') ?? true;
      _showPreview = prefs.getBool('show_preview') ?? true;
      _notificationTone = prefs.getString('notification_tone') ?? 'Default';
      _quietHoursStart = prefs.getString('quiet_hours_start') ?? '22:00';
      _quietHoursEnd = prefs.getString('quiet_hours_end') ?? '08:00';
      _quietHoursEnabled = prefs.getBool('quiet_hours_enabled') ?? false;
    });
  }

  /// حفظ الإعدادات
  void _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('all_notifications', _allNotifications);
    await prefs.setBool('message_notifications', _messageNotifications);
    await prefs.setBool('social_notifications', _socialNotifications);
    await prefs.setBool('story_notifications', _storyNotifications);
    await prefs.setBool('group_notifications', _groupNotifications);
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setBool('vibration_enabled', _vibrationEnabled);
    await prefs.setBool('led_enabled', _ledEnabled);
    await prefs.setBool('show_preview', _showPreview);
    await prefs.setString('notification_tone', _notificationTone);
    await prefs.setString('quiet_hours_start', _quietHoursStart);
    await prefs.setString('quiet_hours_end', _quietHoursEnd);
    await prefs.setBool('quiet_hours_enabled', _quietHoursEnabled);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Notifications',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFFFF6B35),
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // إعدادات عامة
            _buildSectionCard(
              title: 'General Settings',
              icon: Icons.settings,
              children: [
                _buildSwitchTile(
                  title: 'All Notifications',
                  subtitle: 'Enable or disable all notifications',
                  value: _allNotifications,
                  onChanged: (value) {
                    setState(() {
                      _allNotifications = value;
                      if (!value) {
                        _messageNotifications = false;
                        _socialNotifications = false;
                        _storyNotifications = false;
                        _groupNotifications = false;
                      }
                    });
                    _saveSettings();
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات الإشعارات حسب النوع
            _buildSectionCard(
              title: 'Notification Types',
              icon: Icons.category,
              children: [
                _buildSwitchTile(
                  title: 'Messages',
                  subtitle: 'New chat messages',
                  value: _messageNotifications && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _messageNotifications = value);
                    _saveSettings();
                  } : null,
                ),
                _buildSwitchTile(
                  title: 'Social Media',
                  subtitle: 'Likes, comments, shares',
                  value: _socialNotifications && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _socialNotifications = value);
                    _saveSettings();
                  } : null,
                ),
                _buildSwitchTile(
                  title: 'Stories',
                  subtitle: 'New stories from friends',
                  value: _storyNotifications && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _storyNotifications = value);
                    _saveSettings();
                  } : null,
                ),
                _buildSwitchTile(
                  title: 'Groups',
                  subtitle: 'Group messages and activities',
                  value: _groupNotifications && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _groupNotifications = value);
                    _saveSettings();
                  } : null,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات الصوت والاهتزاز
            _buildSectionCard(
              title: 'Sound & Vibration',
              icon: Icons.volume_up,
              children: [
                _buildSwitchTile(
                  title: 'Sound',
                  subtitle: 'Play notification sound',
                  value: _soundEnabled && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _soundEnabled = value);
                    _saveSettings();
                  } : null,
                ),
                _buildSwitchTile(
                  title: 'Vibration',
                  subtitle: 'Vibrate on notifications',
                  value: _vibrationEnabled && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _vibrationEnabled = value);
                    _saveSettings();
                  } : null,
                ),
                _buildSwitchTile(
                  title: 'LED Light',
                  subtitle: 'Flash LED for notifications',
                  value: _ledEnabled && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _ledEnabled = value);
                    _saveSettings();
                  } : null,
                ),
                _buildListTile(
                  title: 'Notification Tone',
                  subtitle: _notificationTone,
                  onTap: _allNotifications ? _selectNotificationTone : null,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // إعدادات الخصوصية
            _buildSectionCard(
              title: 'Privacy',
              icon: Icons.privacy_tip,
              children: [
                _buildSwitchTile(
                  title: 'Show Preview',
                  subtitle: 'Show message content in notifications',
                  value: _showPreview && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _showPreview = value);
                    _saveSettings();
                  } : null,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // الساعات الهادئة
            _buildSectionCard(
              title: 'Quiet Hours',
              icon: Icons.bedtime,
              children: [
                _buildSwitchTile(
                  title: 'Enable Quiet Hours',
                  subtitle: 'Mute notifications during specific hours',
                  value: _quietHoursEnabled && _allNotifications,
                  onChanged: _allNotifications ? (value) {
                    setState(() => _quietHoursEnabled = value);
                    _saveSettings();
                  } : null,
                ),
                if (_quietHoursEnabled && _allNotifications) ...[
                  _buildListTile(
                    title: 'Start Time',
                    subtitle: _quietHoursStart,
                    onTap: () => _selectTime(true),
                  ),
                  _buildListTile(
                    title: 'End Time',
                    subtitle: _quietHoursEnd,
                    onTap: () => _selectTime(false),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFF6B35).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFFFF6B35)),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFFF6B35),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// بناء مفتاح تبديل
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: onChanged != null ? Colors.black87 : Colors.grey,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: onChanged != null ? Colors.grey[600] : Colors.grey[400],
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: const Color(0xFFFF6B35),
    );
  }

  /// بناء عنصر قائمة
  Widget _buildListTile({
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
  }) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: onTap != null ? Colors.black87 : Colors.grey,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: onTap != null ? Colors.grey[600] : Colors.grey[400],
        ),
      ),
      trailing: onTap != null ? Icon(
        Icons.arrow_forward_ios,
        color: Colors.grey[400],
        size: 16,
      ) : null,
      onTap: onTap,
    );
  }

  /// اختيار نغمة الإشعار
  void _selectNotificationTone() {
    final tones = ['Default', 'Bell', 'Chime', 'Ding', 'Notification'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Notification Tone'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: tones.map((tone) => RadioListTile<String>(
            title: Text(tone),
            value: tone,
            groupValue: _notificationTone,
            onChanged: (value) {
              setState(() => _notificationTone = value!);
              _saveSettings();
              Navigator.of(context).pop();
            },
            activeColor: const Color(0xFFFF6B35),
          )).toList(),
        ),
      ),
    );
  }

  /// اختيار الوقت
  void _selectTime(bool isStartTime) async {
    final time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(
        DateTime.parse('2023-01-01 ${isStartTime ? _quietHoursStart : _quietHoursEnd}:00'),
      ),
    );

    if (time != null) {
      final timeString = '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
      setState(() {
        if (isStartTime) {
          _quietHoursStart = timeString;
        } else {
          _quietHoursEnd = timeString;
        }
      });
      _saveSettings();
    }
  }
}
