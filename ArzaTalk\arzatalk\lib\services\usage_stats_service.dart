import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'dart:convert';

/// إحصائية يومية
class DailyStats {
  final DateTime date;
  final int messagesSent;
  final int messagesReceived;
  final int callsMade;
  final int callsReceived;
  final int storiesPosted;
  final int storiesViewed;
  final Duration appUsageTime;
  final int chatsOpened;
  final int groupsCreated;

  const DailyStats({
    required this.date,
    this.messagesSent = 0,
    this.messagesReceived = 0,
    this.callsMade = 0,
    this.callsReceived = 0,
    this.storiesPosted = 0,
    this.storiesViewed = 0,
    this.appUsageTime = Duration.zero,
    this.chatsOpened = 0,
    this.groupsCreated = 0,
  });

  DailyStats copyWith({
    DateTime? date,
    int? messagesSent,
    int? messagesReceived,
    int? callsMade,
    int? callsReceived,
    int? storiesPosted,
    int? storiesViewed,
    Duration? appUsageTime,
    int? chatsOpened,
    int? groupsCreated,
  }) {
    return DailyStats(
      date: date ?? this.date,
      messagesSent: messagesSent ?? this.messagesSent,
      messagesReceived: messagesReceived ?? this.messagesReceived,
      callsMade: callsMade ?? this.callsMade,
      callsReceived: callsReceived ?? this.callsReceived,
      storiesPosted: storiesPosted ?? this.storiesPosted,
      storiesViewed: storiesViewed ?? this.storiesViewed,
      appUsageTime: appUsageTime ?? this.appUsageTime,
      chatsOpened: chatsOpened ?? this.chatsOpened,
      groupsCreated: groupsCreated ?? this.groupsCreated,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date.millisecondsSinceEpoch,
      'messagesSent': messagesSent,
      'messagesReceived': messagesReceived,
      'callsMade': callsMade,
      'callsReceived': callsReceived,
      'storiesPosted': storiesPosted,
      'storiesViewed': storiesViewed,
      'appUsageTime': appUsageTime.inMilliseconds,
      'chatsOpened': chatsOpened,
      'groupsCreated': groupsCreated,
    };
  }

  factory DailyStats.fromMap(Map<String, dynamic> map) {
    return DailyStats(
      date: DateTime.fromMillisecondsSinceEpoch(map['date'] ?? 0),
      messagesSent: map['messagesSent'] ?? 0,
      messagesReceived: map['messagesReceived'] ?? 0,
      callsMade: map['callsMade'] ?? 0,
      callsReceived: map['callsReceived'] ?? 0,
      storiesPosted: map['storiesPosted'] ?? 0,
      storiesViewed: map['storiesViewed'] ?? 0,
      appUsageTime: Duration(milliseconds: map['appUsageTime'] ?? 0),
      chatsOpened: map['chatsOpened'] ?? 0,
      groupsCreated: map['groupsCreated'] ?? 0,
    );
  }
}

/// خدمة إحصائيات الاستخدام
class UsageStatsService extends ChangeNotifier {
  static final UsageStatsService _instance = UsageStatsService._internal();
  factory UsageStatsService() => _instance;
  UsageStatsService._internal();

  // إحصائيات يومية
  final Map<String, DailyStats> _dailyStats = {};
  
  // تتبع الوقت
  DateTime? _sessionStartTime;
  Timer? _usageTimer;
  Duration _currentSessionDuration = Duration.zero;
  
  // إحصائيات الجلسة الحالية
  int _currentSessionMessages = 0;
  int _currentSessionCalls = 0;
  int _currentSessionStories = 0;
  int _currentSessionChats = 0;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadStats();
    _startSession();
  }

  /// بدء جلسة جديدة
  void _startSession() {
    _sessionStartTime = DateTime.now();
    _currentSessionDuration = Duration.zero;
    _currentSessionMessages = 0;
    _currentSessionCalls = 0;
    _currentSessionStories = 0;
    _currentSessionChats = 0;
    
    // بدء مؤقت لتتبع الوقت
    _usageTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _updateSessionTime();
    });
    
    debugPrint('📊 Usage tracking session started');
  }

  /// إنهاء الجلسة
  void endSession() {
    if (_sessionStartTime != null) {
      _updateSessionTime();
      _saveCurrentSession();
      _usageTimer?.cancel();
      _sessionStartTime = null;
      debugPrint('📊 Usage tracking session ended');
    }
  }

  /// تحديث وقت الجلسة
  void _updateSessionTime() {
    if (_sessionStartTime != null) {
      _currentSessionDuration = DateTime.now().difference(_sessionStartTime!);
    }
  }

  /// حفظ الجلسة الحالية
  Future<void> _saveCurrentSession() async {
    final today = _getDateKey(DateTime.now());
    final currentStats = _dailyStats[today] ?? DailyStats(date: DateTime.now());
    
    final updatedStats = currentStats.copyWith(
      messagesSent: currentStats.messagesSent + _currentSessionMessages,
      callsMade: currentStats.callsMade + _currentSessionCalls,
      storiesPosted: currentStats.storiesPosted + _currentSessionStories,
      chatsOpened: currentStats.chatsOpened + _currentSessionChats,
      appUsageTime: currentStats.appUsageTime + _currentSessionDuration,
    );
    
    _dailyStats[today] = updatedStats;
    await _saveStats();
    notifyListeners();
  }

  /// تسجيل إرسال رسالة
  void recordMessageSent() {
    _currentSessionMessages++;
    _incrementTodaysStat('messagesSent');
    debugPrint('📊 Message sent recorded');
  }

  /// تسجيل استقبال رسالة
  void recordMessageReceived() {
    _incrementTodaysStat('messagesReceived');
    debugPrint('📊 Message received recorded');
  }

  /// تسجيل مكالمة صادرة
  void recordCallMade() {
    _currentSessionCalls++;
    _incrementTodaysStat('callsMade');
    debugPrint('📊 Call made recorded');
  }

  /// تسجيل مكالمة واردة
  void recordCallReceived() {
    _incrementTodaysStat('callsReceived');
    debugPrint('📊 Call received recorded');
  }

  /// تسجيل نشر قصة
  void recordStoryPosted() {
    _currentSessionStories++;
    _incrementTodaysStat('storiesPosted');
    debugPrint('📊 Story posted recorded');
  }

  /// تسجيل مشاهدة قصة
  void recordStoryViewed() {
    _incrementTodaysStat('storiesViewed');
    debugPrint('📊 Story viewed recorded');
  }

  /// تسجيل فتح دردشة
  void recordChatOpened() {
    _currentSessionChats++;
    _incrementTodaysStat('chatsOpened');
    debugPrint('📊 Chat opened recorded');
  }

  /// تسجيل إنشاء مجموعة
  void recordGroupCreated() {
    _incrementTodaysStat('groupsCreated');
    debugPrint('📊 Group created recorded');
  }

  /// زيادة إحصائية اليوم
  void _incrementTodaysStat(String statName) {
    final today = _getDateKey(DateTime.now());
    final currentStats = _dailyStats[today] ?? DailyStats(date: DateTime.now());
    
    DailyStats updatedStats;
    switch (statName) {
      case 'messagesSent':
        updatedStats = currentStats.copyWith(messagesSent: currentStats.messagesSent + 1);
        break;
      case 'messagesReceived':
        updatedStats = currentStats.copyWith(messagesReceived: currentStats.messagesReceived + 1);
        break;
      case 'callsMade':
        updatedStats = currentStats.copyWith(callsMade: currentStats.callsMade + 1);
        break;
      case 'callsReceived':
        updatedStats = currentStats.copyWith(callsReceived: currentStats.callsReceived + 1);
        break;
      case 'storiesPosted':
        updatedStats = currentStats.copyWith(storiesPosted: currentStats.storiesPosted + 1);
        break;
      case 'storiesViewed':
        updatedStats = currentStats.copyWith(storiesViewed: currentStats.storiesViewed + 1);
        break;
      case 'chatsOpened':
        updatedStats = currentStats.copyWith(chatsOpened: currentStats.chatsOpened + 1);
        break;
      case 'groupsCreated':
        updatedStats = currentStats.copyWith(groupsCreated: currentStats.groupsCreated + 1);
        break;
      default:
        return;
    }
    
    _dailyStats[today] = updatedStats;
    _saveStats();
    notifyListeners();
  }

  /// الحصول على إحصائيات اليوم
  DailyStats getTodaysStats() {
    final today = _getDateKey(DateTime.now());
    return _dailyStats[today] ?? DailyStats(date: DateTime.now());
  }

  /// الحصول على إحصائيات الأسبوع
  Map<String, dynamic> getWeeklyStats() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    
    int totalMessages = 0;
    int totalCalls = 0;
    int totalStories = 0;
    Duration totalTime = Duration.zero;
    final dailyData = <String, DailyStats>{};
    
    for (int i = 0; i < 7; i++) {
      final date = weekStart.add(Duration(days: i));
      final dateKey = _getDateKey(date);
      final stats = _dailyStats[dateKey] ?? DailyStats(date: date);
      
      dailyData[dateKey] = stats;
      totalMessages += stats.messagesSent + stats.messagesReceived;
      totalCalls += stats.callsMade + stats.callsReceived;
      totalStories += stats.storiesPosted + stats.storiesViewed;
      totalTime += stats.appUsageTime;
    }
    
    return {
      'totalMessages': totalMessages,
      'totalCalls': totalCalls,
      'totalStories': totalStories,
      'totalTime': totalTime,
      'dailyData': dailyData,
      'averageDaily': {
        'messages': totalMessages / 7,
        'calls': totalCalls / 7,
        'stories': totalStories / 7,
        'time': Duration(milliseconds: totalTime.inMilliseconds ~/ 7),
      },
    };
  }

  /// الحصول على إحصائيات الشهر
  Map<String, dynamic> getMonthlyStats() {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    final monthEnd = DateTime(now.year, now.month + 1, 0);
    
    int totalMessages = 0;
    int totalCalls = 0;
    int totalStories = 0;
    Duration totalTime = Duration.zero;
    final dailyData = <String, DailyStats>{};
    
    for (int day = 1; day <= monthEnd.day; day++) {
      final date = DateTime(now.year, now.month, day);
      final dateKey = _getDateKey(date);
      final stats = _dailyStats[dateKey] ?? DailyStats(date: date);
      
      dailyData[dateKey] = stats;
      totalMessages += stats.messagesSent + stats.messagesReceived;
      totalCalls += stats.callsMade + stats.callsReceived;
      totalStories += stats.storiesPosted + stats.storiesViewed;
      totalTime += stats.appUsageTime;
    }
    
    return {
      'totalMessages': totalMessages,
      'totalCalls': totalCalls,
      'totalStories': totalStories,
      'totalTime': totalTime,
      'dailyData': dailyData,
      'averageDaily': {
        'messages': totalMessages / monthEnd.day,
        'calls': totalCalls / monthEnd.day,
        'stories': totalStories / monthEnd.day,
        'time': Duration(milliseconds: totalTime.inMilliseconds ~/ monthEnd.day),
      },
    };
  }

  /// الحصول على أكثر الأيام نشاطاً
  List<DailyStats> getMostActivedays(int count) {
    final allStats = _dailyStats.values.toList();
    allStats.sort((a, b) {
      final aActivity = a.messagesSent + a.messagesReceived + a.callsMade + a.callsReceived;
      final bActivity = b.messagesSent + b.messagesReceived + b.callsMade + b.callsReceived;
      return bActivity.compareTo(aActivity);
    });
    
    return allStats.take(count).toList();
  }

  /// مسح الإحصائيات القديمة
  Future<void> clearOldStats(int daysToKeep) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
    final keysToRemove = <String>[];
    
    for (final entry in _dailyStats.entries) {
      if (entry.value.date.isBefore(cutoffDate)) {
        keysToRemove.add(entry.key);
      }
    }
    
    for (final key in keysToRemove) {
      _dailyStats.remove(key);
    }
    
    await _saveStats();
    notifyListeners();
    debugPrint('📊 Cleared ${keysToRemove.length} old stats entries');
  }

  /// تحميل الإحصائيات
  Future<void> _loadStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString('usage_stats');
      
      if (statsJson != null) {
        final statsMap = json.decode(statsJson) as Map<String, dynamic>;
        _dailyStats.clear();
        
        for (final entry in statsMap.entries) {
          _dailyStats[entry.key] = DailyStats.fromMap(entry.value);
        }
        
        debugPrint('📊 Loaded ${_dailyStats.length} stats entries');
      }
    } catch (e) {
      debugPrint('❌ Error loading usage stats: $e');
    }
  }

  /// حفظ الإحصائيات
  Future<void> _saveStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsMap = _dailyStats.map((key, stats) => MapEntry(key, stats.toMap()));
      final statsJson = json.encode(statsMap);
      
      await prefs.setString('usage_stats', statsJson);
    } catch (e) {
      debugPrint('❌ Error saving usage stats: $e');
    }
  }

  /// الحصول على مفتاح التاريخ
  String _getDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Getters
  Duration get currentSessionDuration => _currentSessionDuration;
  int get currentSessionMessages => _currentSessionMessages;
  int get currentSessionCalls => _currentSessionCalls;
  int get currentSessionStories => _currentSessionStories;
  int get currentSessionChats => _currentSessionChats;
  Map<String, DailyStats> get allStats => Map.from(_dailyStats);

  @override
  void dispose() {
    endSession();
    super.dispose();
  }
}
