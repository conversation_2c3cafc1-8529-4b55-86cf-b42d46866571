import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../../services/auth_service.dart';
import '../../services/chat_service.dart';
import '../../services/storage_service.dart';
import '../../services/pinned_messages_service.dart';
import '../../services/call_service.dart';
import '../../services/sound_service.dart';
import '../../services/chat_lock_service.dart';
import 'forward_message_screen.dart';
import 'package:file_picker/file_picker.dart';
import 'package:geolocator/geolocator.dart';
import '../../models/user_model.dart';
import '../../models/message_model.dart';
import '../../widgets/message_bubble.dart';
import '../../widgets/voice_recorder.dart';
import '../calls/call_screen.dart';
import 'package:intl/intl.dart';

class ChatScreen extends StatefulWidget {
  final UserModel otherUser;

  const ChatScreen({
    super.key,
    required this.otherUser,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final StorageService _storageService = StorageService();
  final SoundService _soundService = SoundService();
  bool _isRecording = false;
  bool _isSending = false;
  MessageModel? _replyingTo; // Message being replied to

  Future<void> _startVoiceCall() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final callService = Provider.of<CallService>(context, listen: false);

      await callService.startVoiceCall(
        authService.currentUser!.phoneNumber,
        widget.otherUser.phoneNumber,
      );

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CallScreen(
              otherUser: widget.otherUser,
              callType: CallType.voice,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting call: $e')),
        );
      }
    }
  }

  Future<void> _startVideoCall() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final callService = Provider.of<CallService>(context, listen: false);

      await callService.startVideoCall(
        authService.currentUser!.phoneNumber,
        widget.otherUser.phoneNumber,
      );

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CallScreen(
              otherUser: widget.otherUser,
              callType: CallType.video,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting call: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _sendTextMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
    });

    final authService = Provider.of<AuthService>(context, listen: false);
    final chatService = Provider.of<ChatService>(context, listen: false);

    final success = await chatService.sendMessage(
      senderId: authService.currentUser!.phoneNumber,
      receiverId: widget.otherUser.phoneNumber,
      message: message,
      type: MessageType.text,
    );

    if (success) {
      _messageController.clear();
      _scrollToBottom();
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('فشل في إرسال الرسالة'),
          backgroundColor: Colors.red,
        ),
      );
    }

    setState(() {
      _isSending = false;
    });
  }

  Future<void> _sendImageMessage() async {
    final imageFile = await _storageService.showImagePickerOptions(context);
    if (imageFile == null) return;

    setState(() {
      _isSending = true;
    });

    try {
      // رفع الصورة إلى Firebase Storage
      final imageUrl = await _storageService.uploadImage(imageFile, 'chat_images');

      if (imageUrl != null) {
        final authService = Provider.of<AuthService>(context, listen: false);
        final chatService = Provider.of<ChatService>(context, listen: false);

        final success = await chatService.sendMessage(
          senderId: authService.currentUser!.phoneNumber,
          receiverId: widget.otherUser.phoneNumber,
          message: 'صورة',
          type: MessageType.image,
          mediaUrl: imageUrl,
        );

        if (success) {
          _scrollToBottom();
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إرسال الصورة'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في رفع الصورة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    setState(() {
      _isSending = false;
    });
  }

  Future<void> _sendVoiceMessage(String audioPath, int duration) async {
    setState(() {
      _isSending = true;
    });

    try {
      // رفع الملف الصوتي إلى Firebase Storage
      final audioUrl = await _storageService.uploadAudio(
        File(audioPath),
        'voice_messages',
      );

      if (audioUrl != null) {
        final authService = Provider.of<AuthService>(context, listen: false);
        final chatService = Provider.of<ChatService>(context, listen: false);

        final success = await chatService.sendMessage(
          senderId: authService.currentUser!.phoneNumber,
          receiverId: widget.otherUser.phoneNumber,
          message: 'رسالة صوتية',
          type: MessageType.voice,
          mediaUrl: audioUrl,
          duration: duration,
        );

        if (success) {
          _scrollToBottom();
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في إرسال الرسالة الصوتية'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في رفع الملف الصوتي'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    setState(() {
      _isSending = false;
    });
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _replyToMessage(MessageModel message) {
    setState(() {
      _replyingTo = message;
    });
  }

  void _cancelReply() {
    setState(() {
      _replyingTo = null;
    });
  }

  void _forwardMessage(MessageModel message) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ForwardMessageScreen(message: message),
      ),
    );
  }

  String _getMessageTypeText(MessageType type) {
    switch (type) {
      case MessageType.text:
        return 'Text message';
      case MessageType.image:
        return '📷 Image';
      case MessageType.voice:
        return '🎵 Voice message';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.file:
        return '📄 File';
      case MessageType.sticker:
        return '🎭 Sticker';
    }
  }

  Future<void> _sendDocument() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _isSending = true;
        });

        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;

        // Upload file to storage
        final fileUrl = await _storageService.uploadFile(file, 'documents', fileName);

        if (fileUrl != null && mounted) {
          final authService = Provider.of<AuthService>(context, listen: false);
          final chatService = Provider.of<ChatService>(context, listen: false);

          await chatService.sendMessage(
            senderId: authService.currentUser!.phoneNumber,
            receiverId: widget.otherUser.phoneNumber,
            message: '📄 $fileName',
            type: MessageType.text, // For now, treat as text with file info
            mediaUrl: fileUrl,
          );
          _scrollToBottom();
        }

        if (mounted) {
          setState(() {
            _isSending = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sending document: $e')),
        );
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  Future<void> _shareLocation() async {
    try {
      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Location permission denied')),
            );
          }
          return;
        }
      }

      setState(() {
        _isSending = true;
      });

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      if (mounted) {
        final authService = Provider.of<AuthService>(context, listen: false);
        final chatService = Provider.of<ChatService>(context, listen: false);

        await chatService.sendMessage(
          senderId: authService.currentUser!.phoneNumber,
          receiverId: widget.otherUser.phoneNumber,
          message: '📍 Location: ${position.latitude}, ${position.longitude}',
          type: MessageType.text,
        );
        _scrollToBottom();
      }

      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sharing location: $e')),
        );
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  Future<void> _shareContact() async {
    // Show contact picker dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Contact'),
        content: const Text('Contact sharing feature will be implemented with contact picker'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _sendAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _isSending = true;
        });

        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;

        // Upload audio file to storage
        final audioUrl = await _storageService.uploadAudio(file, 'audio_files');

        if (audioUrl != null && mounted) {
          final authService = Provider.of<AuthService>(context, listen: false);
          final chatService = Provider.of<ChatService>(context, listen: false);

          await chatService.sendMessage(
            senderId: authService.currentUser!.phoneNumber,
            receiverId: widget.otherUser.phoneNumber,
            message: '🎵 $fileName',
            type: MessageType.voice, // Treat as voice message
            mediaUrl: audioUrl,
          );
          _scrollToBottom();
        }

        if (mounted) {
          setState(() {
            _isSending = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sending audio file: $e')),
        );
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  Future<void> _toggleChatLock() async {
    final chatLockService = Provider.of<ChatLockService>(context, listen: false);
    final chatId = widget.otherUser.phoneNumber;

    if (!chatLockService.isLockEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enable Chat Lock in Settings first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final isLocked = chatLockService.isChatLocked(chatId);

    if (isLocked) {
      await chatLockService.unlockChat(chatId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chat unlocked'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      await chatLockService.lockChat(chatId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chat locked'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _clearChat() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Chat'),
        content: const Text('Are you sure you want to clear all messages in this chat? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Clear', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final chatService = Provider.of<ChatService>(context, listen: false);
      final authService = Provider.of<AuthService>(context, listen: false);

      // Clear messages (this would need to be implemented in ChatService)
      // await chatService.clearChat(authService.currentUser!.phoneNumber, widget.otherUser.phoneNumber);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chat cleared'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library, color: Color(0xFFD32F2F)),
                title: const Text('Photo from Gallery'),
                onTap: () {
                  Navigator.of(context).pop();
                  _sendImageMessage();
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Color(0xFFD32F2F)),
                title: const Text('Take Photo'),
                onTap: () async {
                  Navigator.of(context).pop();

                  // تشغيل صوت الكاميرا
                  _soundService.playCameraSound();

                  final imageFile = await _storageService.takePhoto();
                  if (imageFile != null) {
                    // معالجة الصورة المُلتقطة
                    setState(() {
                      _isSending = true;
                    });

                    final imageUrl = await _storageService.uploadImage(
                      imageFile,
                      'chat_images',
                    );

                    if (imageUrl != null && mounted) {
                      final authService = Provider.of<AuthService>(context, listen: false);
                      final chatService = Provider.of<ChatService>(context, listen: false);

                      await chatService.sendMessage(
                        senderId: authService.currentUser!.phoneNumber,
                        receiverId: widget.otherUser.phoneNumber,
                        message: 'Image',
                        type: MessageType.image,
                        mediaUrl: imageUrl,
                      );
                      _scrollToBottom();
                    }

                    if (mounted) {
                      setState(() {
                        _isSending = false;
                      });
                    }
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.attach_file, color: Color(0xFFD32F2F)),
                title: const Text('Document'),
                onTap: () {
                  Navigator.of(context).pop();
                  _sendDocument();
                },
              ),
              ListTile(
                leading: const Icon(Icons.location_on, color: Color(0xFFD32F2F)),
                title: const Text('Share Location'),
                onTap: () {
                  Navigator.of(context).pop();
                  _shareLocation();
                },
              ),
              ListTile(
                leading: const Icon(Icons.contacts, color: Color(0xFFD32F2F)),
                title: const Text('Share Contact'),
                onTap: () {
                  Navigator.of(context).pop();
                  _shareContact();
                },
              ),
              ListTile(
                leading: const Icon(Icons.music_note, color: Color(0xFFD32F2F)),
                title: const Text('Audio File'),
                onTap: () {
                  Navigator.of(context).pop();
                  _sendAudioFile();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.white,
              backgroundImage: widget.otherUser.profileImageUrl != null
                  ? NetworkImage(widget.otherUser.profileImageUrl!)
                  : null,
              child: widget.otherUser.profileImageUrl == null
                  ? Text(
                      widget.otherUser.name.isNotEmpty
                          ? widget.otherUser.name[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: Color(0xFFD32F2F),
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.otherUser.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    widget.otherUser.isOnline
                        ? 'Online now'
                        : 'Last seen: ${DateFormat('HH:mm').format(widget.otherUser.lastSeen)}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.call),
            onPressed: () => _startVoiceCall(),
          ),
          IconButton(
            icon: const Icon(Icons.videocam),
            onPressed: () => _startVideoCall(),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'lock':
                  _toggleChatLock();
                  break;
                case 'clear':
                  _clearChat();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'lock',
                child: Consumer<ChatLockService>(
                  builder: (context, chatLockService, child) {
                    final chatId = widget.otherUser.phoneNumber;
                    final isLocked = chatLockService.isChatLocked(chatId);
                    return Row(
                      children: [
                        Icon(isLocked ? Icons.lock_open : Icons.lock),
                        const SizedBox(width: 8),
                        Text(isLocked ? 'Unlock Chat' : 'Lock Chat'),
                      ],
                    );
                  },
                ),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('Clear Chat'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // إضافة الرسائل المثبتة
          Consumer<PinnedMessagesService>(
            builder: (context, pinnedService, child) {
              final authService = Provider.of<AuthService>(context, listen: false);
              final currentUser = authService.currentUser;
              if (currentUser == null) return const SizedBox.shrink();

              final chatId = _getChatId(currentUser.phoneNumber, widget.otherUser.phoneNumber);
              final pinnedMessages = pinnedService.getPinnedMessagesForChat(chatId);

              if (pinnedMessages.isEmpty) {
                return const SizedBox.shrink();
              }

              return Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFFD32F2F).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFD32F2F).withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.push_pin,
                          color: Color(0xFFD32F2F),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Pinned Messages (${pinnedMessages.length})',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFFD32F2F),
                            fontSize: 14,
                          ),
                        ),
                        const Spacer(),
                        if (pinnedMessages.length > 1)
                          GestureDetector(
                            onTap: () => _showAllPinnedMessages(context, pinnedMessages),
                            child: const Text(
                              'View All',
                              style: TextStyle(
                                color: Color(0xFFD32F2F),
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      pinnedMessages.first.content,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Pinned by ${pinnedMessages.first.pinnedByName}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
          // منطقة الرسائل
          Expanded(
            child: Consumer2<AuthService, ChatService>(
              builder: (context, authService, chatService, child) {
                final currentUser = authService.currentUser;
                if (currentUser == null) return const SizedBox();

                return StreamBuilder<List<MessageModel>>(
                  stream: chatService.getMessages(
                    currentUser.phoneNumber,
                    widget.otherUser.phoneNumber,
                  ),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text('Error: ${snapshot.error}'),
                      );
                    }

                    final messages = snapshot.data ?? [];

                    if (messages.isEmpty) {
                      return const Center(
                        child: Text(
                          'Start the conversation by sending a message',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                      );
                    }

                    return ListView.builder(
                      controller: _scrollController,
                      reverse: true,
                      itemCount: messages.length,
                      itemBuilder: (context, index) {
                        final message = messages[index];
                        final isMe = message.senderId == currentUser.phoneNumber;

                        return MessageBubble(
                          message: message,
                          isMe: isMe,
                          onReply: _replyToMessage,
                          onForward: _forwardMessage,
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),

          // Reply preview
          if (_replyingTo != null)
            Container(
              padding: const EdgeInsets.all(12),
              color: Colors.grey[100],
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 40,
                    color: const Color(0xFFD32F2F),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Replying to ${_replyingTo!.senderId}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFFD32F2F),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _replyingTo!.type == MessageType.text
                              ? _replyingTo!.message
                              : _getMessageTypeText(_replyingTo!.type),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, size: 20),
                    onPressed: _cancelReply,
                  ),
                ],
              ),
            ),

          // شريط إدخال الرسائل
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Text field
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    textAlign: TextAlign.left,
                    decoration: InputDecoration(
                      hintText: 'Type a message...',
                      prefixIcon: IconButton(
                        icon: const Icon(
                          Icons.attach_file,
                          color: Color(0xFFD32F2F),
                        ),
                        onPressed: _showAttachmentOptions,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 10,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendTextMessage(),
                  ),
                ),

                const SizedBox(width: 8),

                // زر الإرسال أو التسجيل الصوتي
                ValueListenableBuilder<TextEditingValue>(
                  valueListenable: _messageController,
                  builder: (context, value, child) {
                    return value.text.trim().isNotEmpty || _isSending
                        ? IconButton(
                            icon: _isSending
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(
                                    Icons.send,
                                    color: Color(0xFFD32F2F),
                                  ),
                            onPressed: _isSending ? null : _sendTextMessage,
                          )
                        : VoiceRecorder(
                            onVoiceRecorded: _sendVoiceMessage,
                            isRecording: _isRecording,
                            onRecordingStateChanged: (isRecording) {
                              setState(() {
                                _isRecording = isRecording;
                              });
                            },
                          );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على معرف الدردشة
  String _getChatId(String userId1, String userId2) {
    final List<String> ids = [userId1, userId2];
    ids.sort();
    return ids.join('_');
  }

  // عرض جميع الرسائل المثبتة
  void _showAllPinnedMessages(BuildContext context, List<dynamic> pinnedMessages) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // عنوان
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(
                      Icons.push_pin,
                      color: Color(0xFFD32F2F),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Pinned Messages',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${pinnedMessages.length} messages',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),

              // قائمة الرسائل المثبتة
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: pinnedMessages.length,
                  itemBuilder: (context, index) {
                    final pinnedMessage = pinnedMessages[index];
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            pinnedMessage.content,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Text(
                                'Pinned by ${pinnedMessage.pinnedByName}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const Spacer(),
                              Text(
                                _formatDate(pinnedMessage.pinnedAt),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
