import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:local_auth/local_auth.dart';
import '../../services/chat_lock_service.dart';

class ChatLockScreen extends StatefulWidget {
  const ChatLockScreen({super.key});

  @override
  State<ChatLockScreen> createState() => _ChatLockScreenState();
}

class _ChatLockScreenState extends State<ChatLockScreen> {
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _biometricAvailable = false;
  bool _enableBiometric = false;
  List<BiometricType> _availableBiometrics = [];

  @override
  void initState() {
    super.initState();
    _checkBiometricAvailability();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _checkBiometricAvailability() async {
    final chatLockService = Provider.of<ChatLockService>(context, listen: false);
    final isAvailable = await chatLockService.isBiometricAvailable();
    final availableBiometrics = await chatLockService.getAvailableBiometrics();
    
    setState(() {
      _biometricAvailable = isAvailable;
      _availableBiometrics = availableBiometrics;
    });
  }

  String _getBiometricTypeText() {
    if (_availableBiometrics.contains(BiometricType.face)) {
      return 'Face ID';
    } else if (_availableBiometrics.contains(BiometricType.fingerprint)) {
      return 'Fingerprint';
    } else if (_availableBiometrics.contains(BiometricType.iris)) {
      return 'Iris';
    } else {
      return 'Biometric';
    }
  }

  Future<void> _setupChatLock() async {
    if (_passwordController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a password'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_passwordController.text.length < 4) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Password must be at least 4 characters'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_passwordController.text != _confirmPasswordController.text) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Passwords do not match'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final chatLockService = Provider.of<ChatLockService>(context, listen: false);
    final success = await chatLockService.setupChatLock(
      _passwordController.text,
      _enableBiometric && _biometricAvailable,
    );

    if (success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chat lock enabled successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to enable chat lock'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _disableChatLock() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Disable Chat Lock'),
        content: const Text(
          'Are you sure you want to disable chat lock? All locked chats will be unlocked.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Disable', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final chatLockService = Provider.of<ChatLockService>(context, listen: false);
      final success = await chatLockService.disableChatLock();

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chat lock disabled'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatLockService>(
      builder: (context, chatLockService, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Chat Lock'),
            backgroundColor: const Color(0xFFD32F2F),
            foregroundColor: Colors.white,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (chatLockService.isLockEnabled) ...[
                  // Chat lock is enabled
                  Card(
                    color: Colors.green[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(Icons.lock, color: Colors.green),
                          const SizedBox(width: 12),
                          const Expanded(
                            child: Text(
                              'Chat lock is enabled. Your locked chats are protected.',
                              style: TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Biometric toggle
                  if (_biometricAvailable)
                    SwitchListTile(
                      title: Text('Enable ${_getBiometricTypeText()}'),
                      subtitle: Text('Use ${_getBiometricTypeText()} to unlock chats'),
                      value: chatLockService.isBiometricEnabled,
                      activeColor: const Color(0xFFD32F2F),
                      onChanged: (value) async {
                        await chatLockService.toggleBiometric(value);
                      },
                    ),

                  const SizedBox(height: 16),

                  // Locked chats count
                  ListTile(
                    leading: const Icon(Icons.lock_outline, color: Color(0xFFD32F2F)),
                    title: const Text('Locked Chats'),
                    subtitle: Text('${chatLockService.lockedChats.length} chats are locked'),
                  ),

                  const SizedBox(height: 24),

                  // Disable button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _disableChatLock,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Disable Chat Lock'),
                    ),
                  ),
                ] else ...[
                  // Chat lock is disabled - setup screen
                  const Text(
                    'Secure Your Chats',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFD32F2F),
                    ),
                  ),

                  const SizedBox(height: 8),

                  Text(
                    'Lock your important chats with a password and biometric authentication.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Password field
                  TextField(
                    controller: _passwordController,
                    obscureText: _obscurePassword,
                    decoration: InputDecoration(
                      labelText: 'Password',
                      hintText: 'Enter a secure password',
                      prefixIcon: const Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Confirm password field
                  TextField(
                    controller: _confirmPasswordController,
                    obscureText: _obscureConfirmPassword,
                    decoration: InputDecoration(
                      labelText: 'Confirm Password',
                      hintText: 'Re-enter your password',
                      prefixIcon: const Icon(Icons.lock_outline),
                      suffixIcon: IconButton(
                        icon: Icon(_obscureConfirmPassword ? Icons.visibility : Icons.visibility_off),
                        onPressed: () {
                          setState(() {
                            _obscureConfirmPassword = !_obscureConfirmPassword;
                          });
                        },
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Biometric option
                  if (_biometricAvailable)
                    CheckboxListTile(
                      title: Text('Enable ${_getBiometricTypeText()}'),
                      subtitle: Text('Use ${_getBiometricTypeText()} for quick access'),
                      value: _enableBiometric,
                      activeColor: const Color(0xFFD32F2F),
                      onChanged: (value) {
                        setState(() {
                          _enableBiometric = value ?? false;
                        });
                      },
                    ),

                  const SizedBox(height: 32),

                  // Enable button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _setupChatLock,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFD32F2F),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text(
                        'Enable Chat Lock',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Info text
                  Text(
                    'Once enabled, you can lock individual chats by long-pressing on them in the chat list.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
