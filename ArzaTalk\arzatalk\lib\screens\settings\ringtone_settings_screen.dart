import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/ringtone_model.dart';
import '../../services/ringtone_service.dart';

class RingtoneSettingsScreen extends StatefulWidget {
  const RingtoneSettingsScreen({super.key});

  @override
  State<RingtoneSettingsScreen> createState() => _RingtoneSettingsScreenState();
}

class _RingtoneSettingsScreenState extends State<RingtoneSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ringtone Settings'),
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.message),
              text: 'Messages',
            ),
            Tab(
              icon: Icon(Icons.call),
              text: 'Calls',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRingtoneList(RingtoneType.message),
          _buildRingtoneList(RingtoneType.call),
        ],
      ),
    );
  }

  Widget _buildRingtoneList(RingtoneType type) {
    return Consumer<RingtoneService>(
      builder: (context, ringtoneService, child) {
        final ringtones = type == RingtoneType.message
            ? ringtoneService.getMessageRingtones()
            : ringtoneService.getCallRingtones();

        return ListView.builder(
          padding: const EdgeInsets.all(8),
          itemCount: ringtones.length,
          itemBuilder: (context, index) {
            final ringtone = ringtones[index];
            final isSelected = ringtoneService.isRingtoneSelected(ringtone.id, type);
            final isPlaying = ringtoneService.isPlaying;

            return Card(
              margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: isSelected 
                      ? const Color(0xFFD32F2F) 
                      : Colors.grey[300],
                  child: Icon(
                    type == RingtoneType.message ? Icons.message : Icons.call,
                    color: isSelected ? Colors.white : Colors.grey[600],
                  ),
                ),
                title: Text(
                  ringtone.name,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? const Color(0xFFD32F2F) : null,
                  ),
                ),
                subtitle: Text(
                  '${ringtone.duration} seconds',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Play/Stop button
                    IconButton(
                      icon: Icon(
                        isPlaying ? Icons.stop : Icons.play_arrow,
                        color: const Color(0xFFD32F2F),
                      ),
                      onPressed: () async {
                        if (isPlaying) {
                          await ringtoneService.stopRingtone();
                        } else {
                          await ringtoneService.playRingtone(ringtone.id);
                        }
                      },
                    ),
                    // Selection radio
                    Radio<String>(
                      value: ringtone.id,
                      groupValue: type == RingtoneType.message
                          ? ringtoneService.selectedMessageRingtone
                          : ringtoneService.selectedCallRingtone,
                      onChanged: (value) async {
                        if (value != null) {
                          if (type == RingtoneType.message) {
                            await ringtoneService.setMessageRingtone(value);
                          } else {
                            await ringtoneService.setCallRingtone(value);
                          }
                          
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  '${type == RingtoneType.message ? 'Message' : 'Call'} ringtone set to ${ringtone.name}',
                                ),
                                backgroundColor: Colors.green,
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          }
                        }
                      },
                      activeColor: const Color(0xFFD32F2F),
                    ),
                  ],
                ),
                onTap: () async {
                  if (type == RingtoneType.message) {
                    await ringtoneService.setMessageRingtone(ringtone.id);
                  } else {
                    await ringtoneService.setCallRingtone(ringtone.id);
                  }
                  
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          '${type == RingtoneType.message ? 'Message' : 'Call'} ringtone set to ${ringtone.name}',
                        ),
                        backgroundColor: Colors.green,
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  }
                },
              ),
            );
          },
        );
      },
    );
  }
}
