import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:async';

/// مشغل فيديو بأسلوب Facebook مع أدوات تحكم كاملة
class FacebookStyleVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final double? width;
  final double? height;
  final bool autoPlay;
  final bool showControls;
  final bool allowFullscreen;
  final bool showVideoTypeIndicator;
  final String? postId;
  final String? authorId;
  final String? currentUserId;
  final bool isOwner;
  final Function(String)? onLike;
  final Function(String)? onComment;
  final Function(String)? onShare;
  final int likesCount;
  final int commentsCount;
  final bool isLiked;

  const FacebookStyleVideoPlayer({
    super.key,
    required this.videoUrl,
    this.width,
    this.height,
    this.autoPlay = false,
    this.showControls = true,
    this.allowFullscreen = true,
    this.showVideoTypeIndicator = false,
    this.postId,
    this.authorId,
    this.currentUserId,
    this.isOwner = false,
    this.onLike,
    this.onComment,
    this.onShare,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.isLiked = false,
  });

  @override
  State<FacebookStyleVideoPlayer> createState() => _FacebookStyleVideoPlayerState();
}

class _FacebookStyleVideoPlayerState extends State<FacebookStyleVideoPlayer> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isBuffering = false;
  Timer? _hideControlsTimer;
  double _currentPosition = 0.0;
  double _totalDuration = 1.0;
  double _volume = 1.0;
  bool _isMuted = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    _controller?.dispose();
    super.dispose();
  }

  /// تهيئة الفيديو
  Future<void> _initializeVideo() async {
    try {
      debugPrint('🎬 Initializing video: ${widget.videoUrl}');

      if (widget.videoUrl.startsWith('http')) {
        _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      } else {
        _controller = VideoPlayerController.asset(widget.videoUrl);
      }

      await _controller!.initialize();

      setState(() {
        _isInitialized = true;
        _totalDuration = _controller!.value.duration.inMilliseconds.toDouble();
      });

      // إعداد المستمعين
      _controller!.addListener(_videoListener);

      // تشغيل تلقائي إذا مطلوب
      if (widget.autoPlay) {
        _playPause();
      }

      debugPrint('✅ Video initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing video: $e');
    }
  }

  /// مستمع تغييرات الفيديو
  void _videoListener() {
    if (_controller != null && mounted) {
      final value = _controller!.value;

      setState(() {
        _isPlaying = value.isPlaying;
        _isBuffering = value.isBuffering;
        _currentPosition = value.position.inMilliseconds.toDouble();
      });

      // إخفاء الأدوات تلقائياً أثناء التشغيل
      if (_isPlaying && _showControls) {
        _startHideControlsTimer();
      }
    }
  }

  /// بدء مؤقت إخفاء الأدوات
  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  /// تشغيل/إيقاف
  void _playPause() {
    if (_controller != null) {
      setState(() {
        if (_isPlaying) {
          _controller!.pause();
        } else {
          _controller!.play();
        }
      });
    }
  }

  /// تغيير موضع الفيديو
  void _seekTo(double position) {
    if (_controller != null) {
      final duration = Duration(milliseconds: position.toInt());
      _controller!.seekTo(duration);
    }
  }

  /// تغيير مستوى الصوت
  void _changeVolume(double volume) {
    if (_controller != null) {
      setState(() {
        _volume = volume;
        _isMuted = volume == 0;
      });
      _controller!.setVolume(volume);
    }
  }

  /// كتم/إلغاء كتم الصوت
  void _toggleMute() {
    if (_isMuted) {
      _changeVolume(1.0);
    } else {
      _changeVolume(0.0);
    }
  }

  /// عرض بملء الشاشة مع جميع الأدوات
  void _enterFullscreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _FullscreenVideoPlayer(
          controller: _controller!,
          videoUrl: widget.videoUrl,
          postId: widget.postId,
          authorId: widget.authorId,
          currentUserId: widget.currentUserId,
          isOwner: widget.isOwner,
          onLike: widget.onLike,
          onComment: widget.onComment,
          onShare: widget.onShare,
          likesCount: widget.likesCount,
          commentsCount: widget.commentsCount,
          isLiked: widget.isLiked,
        ),
        fullscreenDialog: true,
      ),
    );
  }

  /// إظهار/إخفاء الأدوات
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls && _isPlaying) {
      _startHideControlsTimer();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return _buildLoadingWidget();
    }

    // حساب المقاسات الديناميكية مثل Facebook
    final videoSize = _calculateOptimalVideoSize();

    return Container(
      width: videoSize.width,
      height: videoSize.height,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // الفيديو مع المقاسات الصحيحة
            Center(
              child: AspectRatio(
                aspectRatio: _controller!.value.aspectRatio,
                child: GestureDetector(
                  onTap: _toggleControls,
                  child: VideoPlayer(_controller!),
                ),
              ),
            ),

            // مؤشر التحميل
            if (_isBuffering)
              const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),

            // أدوات التحكم الأساسية فقط (مثل Facebook)
            if (_showControls && widget.showControls)
              _buildBasicControls(),

            // مؤشر نوع الفيديو (اختياري)
            if (widget.showVideoTypeIndicator)
              _buildVideoTypeIndicator(),
          ],
        ),
      ),
    );
  }

  /// حساب المقاسات المثلى للفيديو مثل Facebook
  Size _calculateOptimalVideoSize() {
    if (_controller == null || !_controller!.value.isInitialized) {
      return Size(widget.width ?? double.infinity, widget.height ?? 200);
    }

    final aspectRatio = _controller!.value.aspectRatio;
    final screenWidth = MediaQuery.of(context).size.width;
    final maxWidth = widget.width ?? screenWidth;

    // تحديد نوع الفيديو حسب نسبة العرض إلى الارتفاع
    if (aspectRatio < 0.8) {
      // فيديو عمودي (مثل Stories) - نسبة أقل من 0.8
      return _calculateVerticalVideoSize(maxWidth, aspectRatio);
    } else if (aspectRatio > 1.5) {
      // فيديو طولي (مثل YouTube) - نسبة أكبر من 1.5
      return _calculateHorizontalVideoSize(maxWidth, aspectRatio);
    } else {
      // فيديو مربع أو قريب من المربع
      return _calculateSquareVideoSize(maxWidth, aspectRatio);
    }
  }

  /// حساب مقاسات الفيديو العمودي (Stories style)
  Size _calculateVerticalVideoSize(double maxWidth, double aspectRatio) {
    // للفيديوهات العمودية، نعطي عرض أصغر وارتفاع أكبر
    final width = maxWidth * 0.7; // 70% من العرض المتاح
    final height = width / aspectRatio;

    // تحديد الحد الأقصى للارتفاع
    final maxHeight = MediaQuery.of(context).size.height * 0.7;

    if (height > maxHeight) {
      return Size(maxHeight * aspectRatio, maxHeight);
    }

    return Size(width, height);
  }

  /// حساب مقاسات الفيديو الطولي (YouTube style)
  Size _calculateHorizontalVideoSize(double maxWidth, double aspectRatio) {
    // للفيديوهات الطولية، نستخدم العرض الكامل
    final width = maxWidth;
    final height = width / aspectRatio;

    // تحديد الحد الأقصى للارتفاع
    final maxHeight = 250.0;

    if (height > maxHeight) {
      return Size(maxHeight * aspectRatio, maxHeight);
    }

    return Size(width, height);
  }

  /// حساب مقاسات الفيديو المربع
  Size _calculateSquareVideoSize(double maxWidth, double aspectRatio) {
    // للفيديوهات المربعة، نستخدم نسبة متوازنة
    final width = maxWidth;
    final height = width / aspectRatio;

    // تحديد الحد الأقصى للارتفاع
    final maxHeight = 300.0;

    if (height > maxHeight) {
      return Size(maxHeight * aspectRatio, maxHeight);
    }

    return Size(width, height);
  }

  /// بناء ويدجت التحميل مع مقاسات ديناميكية
  Widget _buildLoadingWidget() {
    // استخدام مقاسات افتراضية للتحميل
    final defaultSize = Size(
      widget.width ?? MediaQuery.of(context).size.width,
      widget.height ?? 200,
    );

    return Container(
      width: defaultSize.width,
      height: defaultSize.height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1877F2)),
            ),
            SizedBox(height: 8),
            Text(
              'Loading video...',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أدوات التحكم الأساسية (مثل Facebook)
  Widget _buildBasicControls() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _showControls = !_showControls;
        });
        if (_showControls && _isPlaying) {
          _startHideControlsTimer();
        }
      },
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // زر التشغيل/الإيقاف في المنتصف (يظهر عند الإيقاف أو عند إظهار الأدوات)
            if (!_isPlaying || _showControls)
              Center(
                child: GestureDetector(
                  onTap: _playPause,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
              ),

            // زر ملء الشاشة في الزاوية (يظهر عند إظهار الأدوات فقط)
            if (_showControls && widget.allowFullscreen)
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: _enterFullscreen,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.fullscreen,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),

            // شريط التقدم في الأسفل (يظهر عند إظهار الأدوات فقط)
            if (_showControls)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.6),
                      ],
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        _formatDuration(Duration(milliseconds: _currentPosition.toInt())),
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: SliderTheme(
                          data: SliderTheme.of(context).copyWith(
                            activeTrackColor: Colors.white,
                            inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                            thumbColor: Colors.white,
                            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
                            overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
                          ),
                          child: Slider(
                            value: _currentPosition.clamp(0.0, _totalDuration),
                            max: _totalDuration,
                            onChanged: _seekTo,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _formatDuration(Duration(milliseconds: _totalDuration.toInt())),
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر نوع الفيديو
  Widget _buildVideoTypeIndicator() {
    if (_controller == null || !_controller!.value.isInitialized) {
      return const SizedBox.shrink();
    }

    final aspectRatio = _controller!.value.aspectRatio;
    String videoType;
    IconData icon;
    Color color;

    if (aspectRatio < 0.8) {
      // فيديو عمودي
      videoType = 'Vertical';
      icon = Icons.stay_current_portrait;
      color = const Color(0xFF42B883); // أخضر
    } else if (aspectRatio > 1.5) {
      // فيديو طولي
      videoType = 'Landscape';
      icon = Icons.stay_current_landscape;
      color = const Color(0xFF1877F2); // أزرق Facebook
    } else {
      // فيديو مربع
      videoType = 'Square';
      icon = Icons.crop_square;
      color = const Color(0xFFFFC107); // أصفر
    }

    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: color,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              videoType,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أدوات التفاعل الجانبية (مثل Facebook)
  Widget _buildSideActions() {
    return Positioned(
      right: 8,
      bottom: 80,
      child: Column(
        children: [
          // زر الإعجاب
          _buildActionButton(
            icon: widget.isLiked ? Icons.favorite : Icons.favorite_border,
            color: widget.isLiked ? Colors.red : Colors.white,
            count: widget.likesCount,
            onTap: () {
              if (widget.onLike != null && widget.postId != null) {
                widget.onLike!(widget.postId!);
              }
            },
          ),

          const SizedBox(height: 16),

          // زر التعليقات
          _buildActionButton(
            icon: Icons.chat_bubble_outline,
            color: Colors.white,
            count: widget.commentsCount,
            onTap: () {
              if (widget.onComment != null && widget.postId != null) {
                widget.onComment!(widget.postId!);
              }
            },
          ),

          const SizedBox(height: 16),

          // زر المشاركة
          _buildActionButton(
            icon: Icons.share,
            color: Colors.white,
            count: 0,
            onTap: () {
              if (widget.onShare != null && widget.postId != null) {
                widget.onShare!(widget.postId!);
              } else {
                _shareVideo();
              }
            },
          ),

          const SizedBox(height: 16),

          // ثلاث نقاط (خيارات إضافية)
          _buildActionButton(
            icon: Icons.more_vert,
            color: Colors.white,
            count: 0,
            onTap: () => _showVideoOptions(),
          ),
        ],
      ),
    );
  }

  /// بناء ثلاث نقاط في الأعلى
  Widget _buildTopActions() {
    return Positioned(
      top: 8,
      right: 8,
      child: IconButton(
        onPressed: () => _showVideoOptions(),
        icon: const Icon(
          Icons.more_vert,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  /// بناء زر تفاعل
  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required int count,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          shape: BoxShape.circle,
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            if (count > 0) ...[
              const SizedBox(height: 4),
              Text(
                _formatCount(count),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// عرض خيارات الفيديو
  void _showVideoOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: 16),

            // خيارات المالك
            if (widget.isOwner) ...[
              ListTile(
                leading: const Icon(Icons.edit, color: Color(0xFFD32F2F)),
                title: const Text('Edit Description'),
                onTap: () {
                  Navigator.pop(context);
                  _editVideoDescription();
                },
              ),

              ListTile(
                leading: const Icon(Icons.privacy_tip, color: Color(0xFFD32F2F)),
                title: const Text('Privacy Settings'),
                onTap: () {
                  Navigator.pop(context);
                  _editPrivacySettings();
                },
              ),

              ListTile(
                leading: const Icon(Icons.push_pin, color: Color(0xFFD32F2F)),
                title: const Text('Pin Post'),
                onTap: () {
                  Navigator.pop(context);
                  _pinPost();
                },
              ),

              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Video'),
                onTap: () {
                  Navigator.pop(context);
                  _deleteVideo();
                },
              ),
            ],

            // خيارات عامة
            ListTile(
              leading: const Icon(Icons.download, color: Color(0xFFD32F2F)),
              title: const Text('Save Video'),
              onTap: () {
                Navigator.pop(context);
                _saveVideo();
              },
            ),

            ListTile(
              leading: const Icon(Icons.link, color: Color(0xFFD32F2F)),
              title: const Text('Copy Link'),
              onTap: () {
                Navigator.pop(context);
                _copyVideoLink();
              },
            ),

            if (!widget.isOwner) ...[
              ListTile(
                leading: const Icon(Icons.visibility_off, color: Colors.orange),
                title: const Text('Hide Video'),
                onTap: () {
                  Navigator.pop(context);
                  _hideVideo();
                },
              ),

              ListTile(
                leading: const Icon(Icons.person_off, color: Colors.orange),
                title: const Text('Hide all from this user'),
                onTap: () {
                  Navigator.pop(context);
                  _hideAllFromUser();
                },
              ),

              ListTile(
                leading: const Icon(Icons.report, color: Colors.red),
                title: const Text('Report Video'),
                onTap: () {
                  Navigator.pop(context);
                  _reportVideo();
                },
              ),

              ListTile(
                leading: const Icon(Icons.block, color: Colors.red),
                title: const Text('Block User'),
                onTap: () {
                  Navigator.pop(context);
                  _blockUser();
                },
              ),
            ],

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// مشاركة الفيديو
  void _shareVideo() async {
    try {
      await Share.share(
        widget.videoUrl,
        subject: 'Shared from ArzaTalk',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error sharing: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تعديل وصف الفيديو
  void _editVideoDescription() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('✏️ Edit description feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// تعديل إعدادات الخصوصية
  void _editPrivacySettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔒 Privacy settings feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// تثبيت المنشور
  void _pinPost() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('📌 Post pinned successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// حذف الفيديو
  void _deleteVideo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Video'),
        content: const Text('Are you sure you want to delete this video?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🗑️ Video deleted'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// حفظ الفيديو
  void _saveVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('💾 Video saved to gallery!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// نسخ رابط الفيديو
  void _copyVideoLink() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔗 Video link copied to clipboard!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// إخفاء الفيديو
  void _hideVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('👁️ Video hidden from your feed'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// إخفاء جميع منشورات المستخدم
  void _hideAllFromUser() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🚫 All posts from this user hidden'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// الإبلاغ عن الفيديو
  void _reportVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🚨 Video reported. Thank you for your feedback.'),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// حظر المستخدم
  void _blockUser() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: const Text('Are you sure you want to block this user?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🚫 User blocked successfully'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Block', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// تنسيق العدد
  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }

  /// تنسيق المدة الزمنية
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

/// مشغل فيديو بملء الشاشة مع جميع أدوات Facebook
class _FullscreenVideoPlayer extends StatefulWidget {
  final VideoPlayerController controller;
  final String videoUrl;
  final String? postId;
  final String? authorId;
  final String? currentUserId;
  final bool isOwner;
  final Function(String)? onLike;
  final Function(String)? onComment;
  final Function(String)? onShare;
  final int likesCount;
  final int commentsCount;
  final bool isLiked;

  const _FullscreenVideoPlayer({
    required this.controller,
    required this.videoUrl,
    this.postId,
    this.authorId,
    this.currentUserId,
    this.isOwner = false,
    this.onLike,
    this.onComment,
    this.onShare,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.isLiked = false,
  });

  @override
  State<_FullscreenVideoPlayer> createState() => _FullscreenVideoPlayerState();
}

class _FullscreenVideoPlayerState extends State<_FullscreenVideoPlayer> {
  bool _showControls = true;
  Timer? _hideControlsTimer;

  @override
  void initState() {
    super.initState();
    _startHideControlsTimer();
  }

  @override
  void dispose() {
    _hideControlsTimer?.cancel();
    super.dispose();
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startHideControlsTimer();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // الفيديو
            Center(
              child: AspectRatio(
                aspectRatio: widget.controller.value.aspectRatio,
                child: VideoPlayer(widget.controller),
              ),
            ),

            // أدوات التحكم
            if (_showControls) _buildFullscreenControls(),

            // أدوات التفاعل الجانبية (مثل Facebook) - تظهر في ملء الشاشة فقط
            if (_showControls) _buildFullscreenSideActions(),

            // ثلاث نقاط في الأعلى - تظهر في ملء الشاشة فقط
            if (_showControls) _buildFullscreenTopActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildFullscreenControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.7),
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          // أدوات علوية
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const Spacer(),
                ],
              ),
            ),
          ),

          const Spacer(),

          // زر التشغيل المركزي
          Center(
            child: GestureDetector(
              onTap: () {
                if (widget.controller.value.isPlaying) {
                  widget.controller.pause();
                } else {
                  widget.controller.play();
                }
                setState(() {});
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  widget.controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: 40,
                ),
              ),
            ),
          ),

          const Spacer(),

          // أدوات سفلية
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // شريط التقدم
                  VideoProgressIndicator(
                    widget.controller,
                    allowScrubbing: true,
                    colors: const VideoProgressColors(
                      playedColor: Colors.white,
                      bufferedColor: Colors.grey,
                      backgroundColor: Colors.black26,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // أدوات التحكم
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      IconButton(
                        onPressed: () {
                          if (widget.controller.value.isPlaying) {
                            widget.controller.pause();
                          } else {
                            widget.controller.play();
                          }
                          setState(() {});
                        },
                        icon: Icon(
                          widget.controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          // TODO: Volume control
                        },
                        icon: const Icon(
                          Icons.volume_up,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(
                          Icons.fullscreen_exit,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أدوات التفاعل الجانبية في ملء الشاشة
  Widget _buildFullscreenSideActions() {
    return Positioned(
      right: 16,
      bottom: 120,
      child: Column(
        children: [
          // زر الإعجاب
          _buildFullscreenActionButton(
            icon: widget.isLiked ? Icons.favorite : Icons.favorite_border,
            color: widget.isLiked ? Colors.red : Colors.white,
            count: widget.likesCount,
            onTap: () {
              if (widget.onLike != null && widget.postId != null) {
                widget.onLike!(widget.postId!);
              }
            },
          ),

          const SizedBox(height: 20),

          // زر التعليقات
          _buildFullscreenActionButton(
            icon: Icons.chat_bubble_outline,
            color: Colors.white,
            count: widget.commentsCount,
            onTap: () {
              if (widget.onComment != null && widget.postId != null) {
                widget.onComment!(widget.postId!);
              }
            },
          ),

          const SizedBox(height: 20),

          // زر المشاركة
          _buildFullscreenActionButton(
            icon: Icons.share,
            color: Colors.white,
            count: 0,
            onTap: () {
              if (widget.onShare != null && widget.postId != null) {
                widget.onShare!(widget.postId!);
              } else {
                _shareVideo();
              }
            },
          ),
        ],
      ),
    );
  }

  /// بناء ثلاث نقاط في ملء الشاشة
  Widget _buildFullscreenTopActions() {
    return Positioned(
      top: 60,
      right: 16,
      child: IconButton(
        onPressed: () => _showVideoOptions(),
        icon: const Icon(
          Icons.more_vert,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  /// بناء زر تفاعل في ملء الشاشة
  Widget _buildFullscreenActionButton({
    required IconData icon,
    required Color color,
    required int count,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          shape: BoxShape.circle,
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            if (count > 0) ...[
              const SizedBox(height: 4),
              Text(
                _formatCount(count),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// مشاركة الفيديو
  void _shareVideo() async {
    try {
      await Share.share(
        widget.videoUrl,
        subject: 'Shared from ArzaTalk',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error sharing: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض خيارات الفيديو
  void _showVideoOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: 16),

            // خيارات المالك
            if (widget.isOwner) ...[
              ListTile(
                leading: const Icon(Icons.edit, color: Color(0xFF1877F2)),
                title: const Text('Edit Description'),
                onTap: () {
                  Navigator.pop(context);
                  _editVideoDescription();
                },
              ),

              ListTile(
                leading: const Icon(Icons.privacy_tip, color: Color(0xFF1877F2)),
                title: const Text('Privacy Settings'),
                onTap: () {
                  Navigator.pop(context);
                  _editPrivacySettings();
                },
              ),

              ListTile(
                leading: const Icon(Icons.push_pin, color: Color(0xFF1877F2)),
                title: const Text('Pin Post'),
                onTap: () {
                  Navigator.pop(context);
                  _pinPost();
                },
              ),

              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Video'),
                onTap: () {
                  Navigator.pop(context);
                  _deleteVideo();
                },
              ),
            ],

            // خيارات عامة
            ListTile(
              leading: const Icon(Icons.download, color: Color(0xFF1877F2)),
              title: const Text('Save Video'),
              onTap: () {
                Navigator.pop(context);
                _saveVideo();
              },
            ),

            ListTile(
              leading: const Icon(Icons.link, color: Color(0xFF1877F2)),
              title: const Text('Copy Link'),
              onTap: () {
                Navigator.pop(context);
                _copyVideoLink();
              },
            ),

            if (!widget.isOwner) ...[
              ListTile(
                leading: const Icon(Icons.visibility_off, color: Colors.orange),
                title: const Text('Hide Video'),
                onTap: () {
                  Navigator.pop(context);
                  _hideVideo();
                },
              ),

              ListTile(
                leading: const Icon(Icons.person_off, color: Colors.orange),
                title: const Text('Hide all from this user'),
                onTap: () {
                  Navigator.pop(context);
                  _hideAllFromUser();
                },
              ),

              ListTile(
                leading: const Icon(Icons.report, color: Colors.red),
                title: const Text('Report Video'),
                onTap: () {
                  Navigator.pop(context);
                  _reportVideo();
                },
              ),

              ListTile(
                leading: const Icon(Icons.block, color: Colors.red),
                title: const Text('Block User'),
                onTap: () {
                  Navigator.pop(context);
                  _blockUser();
                },
              ),
            ],

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  // جميع دوال الخيارات
  void _editVideoDescription() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('✏️ Edit description feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _editPrivacySettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔒 Privacy settings feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _pinPost() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('📌 Post pinned successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _deleteVideo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Video'),
        content: const Text('Are you sure you want to delete this video?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // إغلاق ملء الشاشة أيض<|im_start|>
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🗑️ Video deleted'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _saveVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('💾 Video saved to gallery!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _copyVideoLink() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔗 Video link copied to clipboard!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _hideVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('👁️ Video hidden from your feed'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _hideAllFromUser() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🚫 All posts from this user hidden'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _reportVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🚨 Video reported. Thank you for your feedback.'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _blockUser() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: const Text('Are you sure you want to block this user?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // إغلاق ملء الشاشة أيض<|im_start|>
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🚫 User blocked successfully'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Block', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// تنسيق العدد
  String _formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 1000000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    }
  }
}
