class ContactModel {
  final String phoneNumber;
  final String name;
  final String? profileImageUrl;
  final bool isBlocked;
  final bool isMuted;
  final bool isOnArzaTalk;
  final DateTime? lastSeen;
  final String? status;

  ContactModel({
    required this.phoneNumber,
    required this.name,
    this.profileImageUrl,
    this.isBlocked = false,
    this.isMuted = false,
    this.isOnArzaTalk = false,
    this.lastSeen,
    this.status,
  });

  factory ContactModel.fromMap(Map<String, dynamic> map) {
    return ContactModel(
      phoneNumber: map['phoneNumber'] ?? '',
      name: map['name'] ?? '',
      profileImageUrl: map['profileImageUrl'],
      isBlocked: map['isBlocked'] ?? false,
      isMuted: map['isMuted'] ?? false,
      isOnArzaTalk: map['isOnArzaTalk'] ?? false,
      lastSeen: map['lastSeen'] != null 
          ? DateTime.parse(map['lastSeen']) 
          : null,
      status: map['status'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'phoneNumber': phoneNumber,
      'name': name,
      'profileImageUrl': profileImageUrl,
      'isBlocked': isBlocked,
      'isMuted': isMuted,
      'isOnArzaTalk': isOnArzaTalk,
      'lastSeen': lastSeen?.toIso8601String(),
      'status': status,
    };
  }

  ContactModel copyWith({
    String? phoneNumber,
    String? name,
    String? profileImageUrl,
    bool? isBlocked,
    bool? isMuted,
    bool? isOnArzaTalk,
    DateTime? lastSeen,
    String? status,
  }) {
    return ContactModel(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      name: name ?? this.name,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isBlocked: isBlocked ?? this.isBlocked,
      isMuted: isMuted ?? this.isMuted,
      isOnArzaTalk: isOnArzaTalk ?? this.isOnArzaTalk,
      lastSeen: lastSeen ?? this.lastSeen,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'ContactModel(phoneNumber: $phoneNumber, name: $name, isOnArzaTalk: $isOnArzaTalk)';
  }
}
