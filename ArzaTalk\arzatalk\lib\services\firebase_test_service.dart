import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:typed_data';

/// خدمة اختبار Firebase للتأكد من عمل الاتصال والحفظ
class FirebaseTestService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// اختبار شامل لـ Firebase
  static Future<Map<String, dynamic>> runCompleteTest() async {
    final results = <String, dynamic>{};
    
    debugPrint('🧪 بدء اختبار Firebase الشامل...');
    
    try {
      // 1. اختبار الاتصال بـ Firestore
      results['firestore_connection'] = await _testFirestoreConnection();
      
      // 2. اختبار الكتابة في Firestore
      results['firestore_write'] = await _testFirestoreWrite();
      
      // 3. اختبار القراءة من Firestore
      results['firestore_read'] = await _testFirestoreRead();
      
      // 4. اختبار Firebase Storage
      results['storage_upload'] = await _testStorageUpload();
      
      // 5. اختبار حذف البيانات
      results['cleanup'] = await _testCleanup();
      
      // تلخيص النتائج
      final allPassed = results.values.every((result) => result['success'] == true);
      results['overall_success'] = allPassed;
      
      debugPrint('🎯 نتائج اختبار Firebase:');
      results.forEach((test, result) {
        final status = result['success'] ? '✅' : '❌';
        debugPrint('   $status $test: ${result['message']}');
      });
      
      return results;
      
    } catch (e) {
      debugPrint('❌ خطأ في اختبار Firebase: $e');
      results['error'] = {
        'success': false,
        'message': 'خطأ عام: $e',
        'details': e.toString(),
      };
      return results;
    }
  }

  /// اختبار الاتصال بـ Firestore
  static Future<Map<String, dynamic>> _testFirestoreConnection() async {
    try {
      debugPrint('🔗 اختبار الاتصال بـ Firestore...');
      
      // محاولة الوصول إلى Firestore
      await _firestore.enableNetwork();
      
      return {
        'success': true,
        'message': 'الاتصال بـ Firestore نجح',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ فشل الاتصال بـ Firestore: $e');
      return {
        'success': false,
        'message': 'فشل الاتصال بـ Firestore',
        'error': e.toString(),
      };
    }
  }

  /// اختبار الكتابة في Firestore
  static Future<Map<String, dynamic>> _testFirestoreWrite() async {
    try {
      debugPrint('✍️ اختبار الكتابة في Firestore...');
      
      final testDoc = _firestore.collection('test').doc('firebase_test');
      final testData = {
        'message': 'اختبار Firebase من ArzaTalk',
        'timestamp': Timestamp.now(),
        'app_version': '1.0.0',
        'test_type': 'write_test',
        'success': true,
      };
      
      await testDoc.set(testData);
      
      debugPrint('✅ نجح حفظ البيانات في Firestore');
      return {
        'success': true,
        'message': 'نجح حفظ البيانات في Firestore',
        'document_id': 'firebase_test',
        'data': testData,
      };
    } catch (e) {
      debugPrint('❌ فشل حفظ البيانات في Firestore: $e');
      return {
        'success': false,
        'message': 'فشل حفظ البيانات في Firestore',
        'error': e.toString(),
      };
    }
  }

  /// اختبار القراءة من Firestore
  static Future<Map<String, dynamic>> _testFirestoreRead() async {
    try {
      debugPrint('📖 اختبار القراءة من Firestore...');
      
      final testDoc = await _firestore.collection('test').doc('firebase_test').get();
      
      if (testDoc.exists) {
        final data = testDoc.data();
        debugPrint('✅ نجح قراءة البيانات من Firestore: $data');
        return {
          'success': true,
          'message': 'نجح قراءة البيانات من Firestore',
          'data': data,
          'document_exists': true,
        };
      } else {
        debugPrint('⚠️ المستند غير موجود في Firestore');
        return {
          'success': false,
          'message': 'المستند غير موجود في Firestore',
          'document_exists': false,
        };
      }
    } catch (e) {
      debugPrint('❌ فشل قراءة البيانات من Firestore: $e');
      return {
        'success': false,
        'message': 'فشل قراءة البيانات من Firestore',
        'error': e.toString(),
      };
    }
  }

  /// اختبار رفع ملف إلى Firebase Storage
  static Future<Map<String, dynamic>> _testStorageUpload() async {
    try {
      debugPrint('📤 اختبار رفع ملف إلى Firebase Storage...');
      
      // إنشاء ملف اختبار صغير
      final testData = Uint8List.fromList('اختبار Firebase Storage من ArzaTalk'.codeUnits);
      final fileName = 'test/firebase_test_${DateTime.now().millisecondsSinceEpoch}.txt';
      
      final ref = _storage.ref().child(fileName);
      final uploadTask = ref.putData(testData);
      
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      debugPrint('✅ نجح رفع الملف إلى Firebase Storage: $downloadUrl');
      return {
        'success': true,
        'message': 'نجح رفع الملف إلى Firebase Storage',
        'download_url': downloadUrl,
        'file_name': fileName,
        'file_size': testData.length,
      };
    } catch (e) {
      debugPrint('❌ فشل رفع الملف إلى Firebase Storage: $e');
      return {
        'success': false,
        'message': 'فشل رفع الملف إلى Firebase Storage',
        'error': e.toString(),
      };
    }
  }

  /// تنظيف بيانات الاختبار
  static Future<Map<String, dynamic>> _testCleanup() async {
    try {
      debugPrint('🧹 تنظيف بيانات الاختبار...');
      
      // حذف مستند الاختبار من Firestore
      await _firestore.collection('test').doc('firebase_test').delete();
      
      debugPrint('✅ تم تنظيف بيانات الاختبار');
      return {
        'success': true,
        'message': 'تم تنظيف بيانات الاختبار',
      };
    } catch (e) {
      debugPrint('⚠️ تحذير: فشل تنظيف بيانات الاختبار: $e');
      return {
        'success': true, // لا نعتبر هذا فشل حرج
        'message': 'تحذير: فشل تنظيف بيانات الاختبار',
        'warning': e.toString(),
      };
    }
  }

  /// اختبار سريع للاتصال
  static Future<bool> quickConnectionTest() async {
    try {
      debugPrint('⚡ اختبار سريع للاتصال بـ Firebase...');
      
      // اختبار بسيط للاتصال
      await _firestore.collection('test').limit(1).get();
      
      debugPrint('✅ الاتصال بـ Firebase يعمل');
      return true;
    } catch (e) {
      debugPrint('❌ فشل الاتصال بـ Firebase: $e');
      return false;
    }
  }

  /// اختبار حفظ منشور حقيقي
  static Future<Map<String, dynamic>> testRealPostSave() async {
    try {
      debugPrint('📝 اختبار حفظ منشور حقيقي...');
      
      final postData = {
        'id': 'test_post_${DateTime.now().millisecondsSinceEpoch}',
        'authorId': '+212638813823',
        'authorName': 'مستخدم اختبار',
        'content': 'هذا منشور اختبار للتأكد من عمل Firebase',
        'images': <String>[],
        'videos': <String>[],
        'reactions': <String, int>{},
        'commentsCount': 0,
        'sharesCount': 0,
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        'type': 'text',
        'privacy': 'public',
        'isDeleted': false,
        'isEdited': false,
        'tags': <String>[],
        'location': null,
        'feeling': null,
        'musicUrl': null,
        'musicTitle': null,
        'backgroundColor': null,
        'taggedUsers': <String>[],
        'isLiveStream': false,
        'originalPostId': null,
        'isRepost': false,
        'linkPreviews': <Map<String, dynamic>>[],
      };
      
      await _firestore.collection('posts').doc(postData['id'] as String).set(postData);
      
      // التحقق من الحفظ
      final savedDoc = await _firestore.collection('posts').doc(postData['id'] as String).get();
      
      if (savedDoc.exists) {
        debugPrint('✅ نجح حفظ المنشور في Firebase');
        
        // حذف المنشور التجريبي
        await _firestore.collection('posts').doc(postData['id'] as String).delete();
        
        return {
          'success': true,
          'message': 'نجح حفظ المنشور في Firebase',
          'post_id': postData['id'],
        };
      } else {
        return {
          'success': false,
          'message': 'فشل التحقق من حفظ المنشور',
        };
      }
    } catch (e) {
      debugPrint('❌ فشل حفظ المنشور: $e');
      return {
        'success': false,
        'message': 'فشل حفظ المنشور في Firebase',
        'error': e.toString(),
      };
    }
  }

  /// اختبار حفظ تفاعل حقيقي
  static Future<Map<String, dynamic>> testRealReactionSave() async {
    try {
      debugPrint('👍 اختبار حفظ تفاعل حقيقي...');
      
      // إنشاء منشور اختبار أولاً
      final postId = 'test_reaction_post_${DateTime.now().millisecondsSinceEpoch}';
      final postData = {
        'id': postId,
        'authorId': '+212638813823',
        'authorName': 'مستخدم اختبار',
        'content': 'منشور اختبار للتفاعلات',
        'reactions': <String, int>{},
        'createdAt': Timestamp.now(),
      };
      
      await _firestore.collection('posts').doc(postId).set(postData);
      
      // إضافة تفاعل
      final reactions = {'👍': 1, '❤️': 1};
      await _firestore.collection('posts').doc(postId).update({
        'reactions': reactions,
        'updatedAt': Timestamp.now(),
      });
      
      // التحقق من الحفظ
      final updatedDoc = await _firestore.collection('posts').doc(postId).get();
      final savedReactions = updatedDoc.data()?['reactions'];
      
      // حذف المنشور التجريبي
      await _firestore.collection('posts').doc(postId).delete();
      
      if (savedReactions != null && savedReactions['👍'] == 1) {
        debugPrint('✅ نجح حفظ التفاعل في Firebase');
        return {
          'success': true,
          'message': 'نجح حفظ التفاعل في Firebase',
          'reactions': savedReactions,
        };
      } else {
        return {
          'success': false,
          'message': 'فشل التحقق من حفظ التفاعل',
          'saved_reactions': savedReactions,
        };
      }
    } catch (e) {
      debugPrint('❌ فشل حفظ التفاعل: $e');
      return {
        'success': false,
        'message': 'فشل حفظ التفاعل في Firebase',
        'error': e.toString(),
      };
    }
  }
}
