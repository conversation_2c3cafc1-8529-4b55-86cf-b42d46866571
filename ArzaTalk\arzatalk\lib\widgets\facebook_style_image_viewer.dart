import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:share_plus/share_plus.dart';

/// عارض صور بأسلوب Facebook مع أدوات تحكم كاملة
class FacebookStyleImageViewer extends StatefulWidget {
  final List<String> imageUrls;
  final int initialIndex;
  final bool showControls;
  final bool allowZoom;
  final bool allowSwipe;
  final String? postId;
  final String? authorId;
  final bool canEdit;

  const FacebookStyleImageViewer({
    super.key,
    required this.imageUrls,
    this.initialIndex = 0,
    this.showControls = true,
    this.allowZoom = true,
    this.allowSwipe = true,
    this.postId,
    this.authorId,
    this.canEdit = false,
  });

  @override
  State<FacebookStyleImageViewer> createState() => _FacebookStyleImageViewerState();
}

class _FacebookStyleImageViewerState extends State<FacebookStyleImageViewer> {
  late PageController _pageController;
  int _currentIndex = 0;
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// التنقل للصورة التالية
  void _nextImage() {
    if (_currentIndex < widget.imageUrls.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// التنقل للصورة السابقة
  void _previousImage() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// إظهار/إخفاء الأدوات
  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  /// فتح الصورة بملء الشاشة
  void _openFullscreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _FullscreenImageGallery(
          imageUrls: widget.imageUrls,
          initialIndex: _currentIndex,
        ),
        fullscreenDialog: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.imageUrls.isEmpty) {
      return _buildEmptyWidget();
    }

    if (widget.imageUrls.length == 1) {
      return _buildSingleImage();
    }

    return _buildImageGallery();
  }

  /// بناء ويدجت فارغ
  Widget _buildEmptyWidget() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Icon(
          Icons.image_not_supported,
          color: Colors.grey,
          size: 50,
        ),
      ),
    );
  }

  /// بناء صورة واحدة
  Widget _buildSingleImage() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // الصورة
            Positioned.fill(
              child: GestureDetector(
                onTap: widget.allowZoom ? _openFullscreen : _toggleControls,
                child: CachedNetworkImage(
                  imageUrl: widget.imageUrls[0],
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[300],
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD32F2F)),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(
                        Icons.error,
                        color: Colors.grey,
                        size: 50,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // أدوات التحكم
            if (_showControls && widget.showControls)
              _buildSingleImageControls(),
          ],
        ),
      ),
    );
  }

  /// بناء معرض الصور
  Widget _buildImageGallery() {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // معرض الصور
            Positioned.fill(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemCount: widget.imageUrls.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: widget.allowZoom ? _openFullscreen : _toggleControls,
                    child: CachedNetworkImage(
                      imageUrl: widget.imageUrls[index],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[300],
                        child: const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD32F2F)),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[300],
                        child: const Center(
                          child: Icon(
                            Icons.error,
                            color: Colors.grey,
                            size: 50,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            // أدوات التحكم
            if (_showControls && widget.showControls)
              _buildGalleryControls(),
          ],
        ),
      ),
    );
  }

  /// بناء أدوات تحكم الصورة الواحدة
  Widget _buildSingleImageControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.3),
            Colors.transparent,
            Colors.black.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: Column(
        children: [
          // أدوات علوية
          Padding(
            padding: const EdgeInsets.all(8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // ثلاث نقاط (قائمة الخيارات)
                IconButton(
                  onPressed: () => _showImageOptions(context, 0),
                  icon: const Icon(
                    Icons.more_vert,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                // زر ملء الشاشة
                if (widget.allowZoom)
                  IconButton(
                    onPressed: _openFullscreen,
                    icon: const Icon(
                      Icons.fullscreen,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
              ],
            ),
          ),

          const Spacer(),

          // أيقونة التكبير في المنتصف
          if (widget.allowZoom)
            Center(
              child: GestureDetector(
                onTap: _openFullscreen,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.zoom_in,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),

          const Spacer(),
        ],
      ),
    );
  }

  /// بناء أدوات تحكم المعرض
  Widget _buildGalleryControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.3),
            Colors.transparent,
            Colors.black.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: Column(
        children: [
          // أدوات علوية
          Padding(
            padding: const EdgeInsets.all(8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // عداد الصور
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${_currentIndex + 1} / ${widget.imageUrls.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                // زر ملء الشاشة
                if (widget.allowZoom)
                  IconButton(
                    onPressed: _openFullscreen,
                    icon: const Icon(
                      Icons.fullscreen,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
              ],
            ),
          ),

          const Spacer(),

          // أزرار التنقل
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // زر السابق
              if (_currentIndex > 0)
                Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: IconButton(
                    onPressed: _previousImage,
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),

              const Spacer(),

              // زر التالي
              if (_currentIndex < widget.imageUrls.length - 1)
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    onPressed: _nextImage,
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
            ],
          ),

          const Spacer(),

          // مؤشرات الصور
          Padding(
            padding: const EdgeInsets.all(8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                widget.imageUrls.length,
                (index) => Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: index == _currentIndex
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.4),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض خيارات الصورة (ثلاث نقاط)
  void _showImageOptions(BuildContext context, int imageIndex) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: 16),

            // خيارات الصورة
            ListTile(
              leading: const Icon(Icons.download, color: Colors.grey),
              title: const Text('Save to Phone (Coming Soon)'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('💡 Save feature coming soon!'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.share, color: Color(0xFFD32F2F)),
              title: const Text('Share'),
              onTap: () {
                Navigator.pop(context);
                _shareImage(imageIndex);
              },
            ),

            if (widget.canEdit) ...[
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Image'),
                onTap: () {
                  Navigator.pop(context);
                  _deleteImage(imageIndex);
                },
              ),
            ],

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }



  /// مشاركة الصورة
  Future<void> _shareImage(int imageIndex) async {
    try {
      final imageUrl = widget.imageUrls[imageIndex];
      await Share.share(
        imageUrl,
        subject: 'Shared from ArzaTalk',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error sharing: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// حذف الصورة
  void _deleteImage(int imageIndex) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Image'),
        content: const Text('Are you sure you want to delete this image?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // هنا يمكن إضافة منطق حذف الصورة من Firebase
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🗑️ Image deleted'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

/// معرض صور بملء الشاشة
class _FullscreenImageGallery extends StatelessWidget {
  final List<String> imageUrls;
  final int initialIndex;

  const _FullscreenImageGallery({
    required this.imageUrls,
    required this.initialIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '${initialIndex + 1} / ${imageUrls.length}',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          // ثلاث نقاط في ملء الشاشة
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: () => _showFullscreenImageOptions(context, initialIndex),
          ),
        ],
      ),
      body: PhotoViewGallery.builder(
        scrollPhysics: const BouncingScrollPhysics(),
        builder: (BuildContext context, int index) {
          return PhotoViewGalleryPageOptions(
            imageProvider: CachedNetworkImageProvider(imageUrls[index]),
            initialScale: PhotoViewComputedScale.contained,
            minScale: PhotoViewComputedScale.contained * 0.8,
            maxScale: PhotoViewComputedScale.covered * 2,
          );
        },
        itemCount: imageUrls.length,
        loadingBuilder: (context, event) => const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
        pageController: PageController(initialPage: initialIndex),
      ),
    );
  }

  /// عرض خيارات الصورة في ملء الشاشة
  void _showFullscreenImageOptions(BuildContext context, int imageIndex) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const SizedBox(height: 16),

            ListTile(
              leading: const Icon(Icons.download, color: Colors.grey),
              title: const Text('Save to Phone (Coming Soon)'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('💡 Save feature coming soon!'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
            ),

            ListTile(
              leading: const Icon(Icons.share, color: Color(0xFFD32F2F)),
              title: const Text('Share'),
              onTap: () {
                Navigator.pop(context);
                _shareFullscreenImage(imageIndex);
              },
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }



  /// مشاركة الصورة من ملء الشاشة
  Future<void> _shareFullscreenImage(int imageIndex) async {
    try {
      final imageUrl = imageUrls[imageIndex];
      await Share.share(
        imageUrl,
        subject: 'Shared from ArzaTalk',
      );
    } catch (e) {
      debugPrint('Error sharing: $e');
    }
  }
}
