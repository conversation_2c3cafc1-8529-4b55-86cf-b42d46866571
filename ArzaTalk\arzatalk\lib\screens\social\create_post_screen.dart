import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../../services/social_feed_service.dart';
import '../../services/auth_service.dart';
import '../../services/media_service.dart';
import '../../services/link_preview_service.dart';
import '../../models/post_model.dart';
import '../../models/link_preview_model.dart';
import '../../widgets/link_preview_widget.dart';
import '../../widgets/facebook_style_background_selector.dart';

/// شاشة إنشاء منشور جديد
class CreatePostScreen extends StatefulWidget {
  final bool withImage;
  final bool withVideo;
  final bool withFeeling;
  final String? selectedFeeling;
  final PostModel? editPost;

  const CreatePostScreen({
    super.key,
    this.withImage = false,
    this.withVideo = false,
    this.withFeeling = false,
    this.selectedFeeling,
    this.editPost,
  });

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final TextEditingController _contentController = TextEditingController();
  final List<String> _selectedImages = [];
  final List<String> _selectedVideos = [];
  final List<String> _taggedUsers = [];
  PostPrivacy _selectedPrivacy = PostPrivacy.public;
  String? _selectedLocation;
  String? _selectedFeeling;
  String? _selectedMusic;
  String? _selectedMusicTitle;
  String? _selectedBackgroundColor;
  bool _isLiveStream = false;
  bool _isLoading = false;
  final List<LinkPreviewModel> _linkPreviews = [];
  bool _isLoadingLinkPreview = false;
  BackgroundColorOption _selectedBackgroundOption = FacebookBackgroundColors.colors.first;
  bool _showBackgroundSelector = false;
  bool get _isEditing => widget.editPost != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _contentController.text = widget.editPost!.content;
      _selectedImages.addAll(widget.editPost!.images);
      _selectedPrivacy = widget.editPost!.privacy;
    }

    if (widget.withImage) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _pickImage();
      });
    }

    // تعيين المشاعر المحددة
    if (widget.selectedFeeling != null) {
      _selectedFeeling = widget.selectedFeeling;
    }
  }

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  /// التعامل مع زر الرجوع - حفظ المسودة
  Future<bool> _onWillPop() async {
    // إذا لم يكن هناك محتوى، اخرج مباشرة
    if (_contentController.text.trim().isEmpty &&
        _selectedImages.isEmpty &&
        _selectedVideos.isEmpty) {
      return true;
    }

    // عرض حوار حفظ المسودة
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Save Draft?'),
        content: const Text('Do you want to save your post as a draft or discard it?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(true), // تجاهل
            child: const Text('Discard', style: TextStyle(color: Colors.red)),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(false), // متابعة التعديل
            child: const Text('Continue Editing'),
          ),
          TextButton(
            onPressed: () {
              // حفظ كمسودة (يمكن إضافة منطق الحفظ هنا)
              Navigator.of(context).pop(true);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('💾 Draft saved for later'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Save Draft'),
          ),
        ],
      ),
    );

    return result ?? false; // إذا ألغى المستخدم، لا تخرج
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldPop = await _onWillPop();
          if (shouldPop && mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Edit Post' : 'Create Post',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFFD32F2F),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _canPost() ? _handlePost : null,
            child: Text(
              _isEditing ? 'Update' : 'Post',
              style: TextStyle(
                color: _canPost() ? Colors.white : Colors.white54,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // معلومات المستخدم
                        _buildUserInfo(),

                        const SizedBox(height: 16),

                        // حقل النص مع خلفية
                        _buildContentFieldWithBackground(),

                        // محدد الخلفيات
                        if (_showBackgroundSelector) _buildBackgroundSelector(),

                        const SizedBox(height: 16),

                        // الميزات المختارة
                        _buildSelectedFeatures(),

                        // الصور المحددة
                        if (_selectedImages.isNotEmpty) _buildSelectedImages(),

                        // الفيديوهات المختارة
                        if (_selectedVideos.isNotEmpty) _buildSelectedVideos(),

                        // معاينات الروابط
                        if (_linkPreviews.isNotEmpty) _buildLinkPreviews(),

                        // مؤشر تحميل معاينة الروابط
                        if (_isLoadingLinkPreview) _buildLinkPreviewLoader(),

                        const SizedBox(height: 16),

                        // خيارات الخصوصية
                        _buildPrivacySelector(),
                      ],
                    ),
                  ),
                ),

                // شريط الأدوات السفلي
                _buildBottomToolbar(),
              ],
            ),
      ),
    );
  }

  /// بناء معلومات المستخدم
  Widget _buildUserInfo() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final currentUser = authService.currentUser;
        if (currentUser == null) return const SizedBox.shrink();

        return Row(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: const Color(0xFFD32F2F),
              child: Text(
                currentUser.name.isNotEmpty ? currentUser.name[0].toUpperCase() : '?',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    currentUser.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Row(
                    children: [
                      Icon(
                        _getPrivacyIcon(_selectedPrivacy),
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getPrivacyText(_selectedPrivacy),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  /// بناء حقل المحتوى مع خلفية
  Widget _buildContentFieldWithBackground() {
    return Container(
      decoration: _selectedBackgroundOption.decoration,
      child: Container(
        padding: const EdgeInsets.all(16),
        constraints: const BoxConstraints(minHeight: 120),
        child: TextField(
          controller: _contentController,
          decoration: InputDecoration(
            hintText: 'What\'s on your mind?',
            hintStyle: TextStyle(
              color: _selectedBackgroundOption.id == 'none'
                  ? Colors.grey[500]
                  : _selectedBackgroundOption.textColor.withValues(alpha: 0.7),
              fontSize: 18,
            ),
            border: InputBorder.none,
          ),
          style: TextStyle(
            fontSize: 18,
            color: _selectedBackgroundOption.textColor,
            fontWeight: _selectedBackgroundOption.id != 'none'
                ? FontWeight.w500
                : FontWeight.normal,
          ),
          maxLines: null,
          minLines: 3,
          onChanged: _onTextChanged,
        ),
      ),
    );
  }

  /// بناء محدد الخلفيات
  Widget _buildBackgroundSelector() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: FacebookStyleBackgroundSelector(
        selectedColorId: _selectedBackgroundOption.id,
        onColorSelected: (colorOption) {
          setState(() {
            _selectedBackgroundOption = colorOption;
          });
        },
        showTitle: true,
      ),
    );
  }

  /// بناء الصور المحددة
  Widget _buildSelectedImages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Images (${_selectedImages.length})',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            TextButton(
              onPressed: _pickImage,
              child: const Text('Add More'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedImages.length,
            itemBuilder: (context, index) {
              return Container(
                width: 100,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: File(_selectedImages[index]).existsSync()
                          ? Image.file(
                              File(_selectedImages[index]),
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => Container(
                                width: 100,
                                height: 100,
                                color: Colors.grey[300],
                                child: const Icon(
                                  Icons.broken_image,
                                  size: 40,
                                  color: Colors.grey,
                                ),
                              ),
                            )
                          : Container(
                              width: 100,
                              height: 100,
                              color: Colors.grey[300],
                              child: const Icon(
                                Icons.image_not_supported,
                                size: 40,
                                color: Colors.grey,
                              ),
                            ),
                    ),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () => _removeImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.black54,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء الميزات المختارة
  Widget _buildSelectedFeatures() {
    final hasFeatures = _selectedLocation != null ||
        _selectedFeeling != null ||
        _selectedMusicTitle != null ||
        _selectedBackgroundColor != null ||
        _taggedUsers.isNotEmpty ||
        _isLiveStream;

    if (!hasFeatures) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Selected Features:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: [
              // الموقع
              if (_selectedLocation != null)
                _buildFeatureChip(
                  icon: Icons.location_on,
                  label: _selectedLocation!,
                  color: Colors.red,
                  onRemove: () => setState(() => _selectedLocation = null),
                ),

              // المشاعر
              if (_selectedFeeling != null)
                _buildFeatureChip(
                  icon: Icons.emoji_emotions,
                  label: _selectedFeeling!,
                  color: Colors.orange,
                  onRemove: () => setState(() => _selectedFeeling = null),
                ),

              // الموسيقى
              if (_selectedMusicTitle != null)
                _buildFeatureChip(
                  icon: Icons.music_note,
                  label: _selectedMusicTitle!,
                  color: Colors.purple,
                  onRemove: () => setState(() {
                    _selectedMusic = null;
                    _selectedMusicTitle = null;
                  }),
                ),

              // الخلفية
              if (_selectedBackgroundColor != null)
                _buildFeatureChip(
                  icon: Icons.palette,
                  label: _selectedBackgroundColor!,
                  color: Colors.indigo,
                  onRemove: () => setState(() => _selectedBackgroundColor = null),
                ),

              // الأشخاص المشار إليهم
              if (_taggedUsers.isNotEmpty)
                _buildFeatureChip(
                  icon: Icons.person_add,
                  label: '${_taggedUsers.length} people',
                  color: Colors.teal,
                  onRemove: () => setState(() => _taggedUsers.clear()),
                ),

              // البث المباشر
              if (_isLiveStream)
                _buildFeatureChip(
                  icon: Icons.live_tv,
                  label: 'Live Stream',
                  color: Colors.red,
                  onRemove: () => setState(() => _isLiveStream = false),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة الميزة
  Widget _buildFeatureChip({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onRemove,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close,
              size: 14,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء الفيديوهات المختارة
  Widget _buildSelectedVideos() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Videos (${_selectedVideos.length})',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            TextButton(
              onPressed: _pickVideo,
              child: const Text('Add More'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedVideos.length,
            itemBuilder: (context, index) {
              return Container(
                width: 120,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.black87,
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 120,
                        height: 120,
                        color: Colors.black87,
                        child: const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.videocam,
                              size: 40,
                              color: Colors.white,
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Video',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // زر التشغيل
                    Center(
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          size: 20,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    // زر الحذف
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () => _removeVideo(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.black54,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  /// إزالة فيديو
  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  /// بناء محدد الخصوصية
  Widget _buildPrivacySelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Who can see this post?',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          ...PostPrivacy.values.map((privacy) => RadioListTile<PostPrivacy>(
            title: Row(
              children: [
                Icon(
                  _getPrivacyIcon(privacy),
                  size: 20,
                  color: const Color(0xFFD32F2F),
                ),
                const SizedBox(width: 8),
                Text(_getPrivacyText(privacy)),
              ],
            ),
            subtitle: Text(_getPrivacyDescription(privacy)),
            value: privacy,
            groupValue: _selectedPrivacy,
            onChanged: (value) {
              setState(() {
                _selectedPrivacy = value!;
              });
            },
            activeColor: const Color(0xFFD32F2F),
          )),
        ],
      ),
    );
  }

  /// بناء شريط الأدوات السفلي
  Widget _buildBottomToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Column(
        children: [
          // الصف الأول - ملصقات احترافية LinkedIn-style (بدون Live)
          Row(
            children: [
              _buildLinkedInToolbarButton(
                icon: Icons.photo_camera,
                label: 'Photo',
                color: const Color(0xFF45BD62),
                onTap: _pickImage,
              ),
              const SizedBox(width: 16),
              _buildLinkedInToolbarButton(
                icon: Icons.video_library,
                label: 'Video',
                color: const Color(0xFF1877F2),
                onTap: _pickVideo,
              ),
              const SizedBox(width: 16),
              _buildLinkedInToolbarButton(
                icon: Icons.palette,
                label: 'Background',
                color: const Color(0xFF9C27B0),
                onTap: () {
                  setState(() {
                    _showBackgroundSelector = !_showBackgroundSelector;
                  });
                },
                isSelected: _selectedBackgroundOption.id != 'none',
              ),
              const SizedBox(width: 16),
              _buildLinkedInToolbarButton(
                icon: Icons.emoji_emotions,
                label: 'Feeling',
                color: const Color(0xFFF7B928),
                onTap: _pickFeeling,
              ),
            ],
          ),
          const SizedBox(height: 12),
          // الصف الثاني - معطل مؤقتاً
          // Row(
          //   children: [
          //     _buildToolbarButton(
          //       icon: Icons.location_on,
          //       label: 'Location',
          //       onTap: _pickLocation,
          //     ),
          //     const SizedBox(width: 16),
          //     _buildToolbarButton(
          //       icon: Icons.emoji_emotions,
          //       label: 'Feeling',
          //       onTap: _pickFeeling,
          //     ),
          //     const SizedBox(width: 16),
          //     _buildToolbarButton(
          //       icon: Icons.music_note,
          //       label: 'Music',
          //       onTap: _pickMusic,
          //     ),
          //   ],
          // ),
          const SizedBox(height: 12),
          // الصف الثالث
          Row(
            children: [
              _buildToolbarButton(
                icon: Icons.palette,
                label: 'Background',
                onTap: _pickBackgroundColor,
              ),
              const SizedBox(width: 16),
              _buildToolbarButton(
                icon: Icons.person_add,
                label: 'Tag People',
                onTap: _tagPeople,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء زر شريط أدوات LinkedIn احترافي
  Widget _buildLinkedInToolbarButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected ? color.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: isSelected ? Border.all(color: color, width: 1.5) : null,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // ملصق احترافي LinkedIn-style
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: color.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                ),
                child: Center(
                  child: Icon(
                    icon,
                    size: 20,
                    color: color,
                  ),
                ),
              ),
              const SizedBox(height: 6),
              // النص
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? color : const Color(0xFF65676B),
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء زر شريط الأدوات (احتياطي)
  Widget _buildToolbarButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    final color = isSelected ? Colors.orange : const Color(0xFFD32F2F);

    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// التحقق من إمكانية النشر
  bool _canPost() {
    return _contentController.text.trim().isNotEmpty && !_isLoading;
  }

  /// التعامل مع النشر
  void _handlePost() async {
    if (!_canPost()) return;

    setState(() => _isLoading = true);

    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser == null) {
      setState(() => _isLoading = false);
      return;
    }

    bool success;
    if (_isEditing) {
      success = await socialService.updatePost(
        postId: widget.editPost!.id,
        content: _contentController.text.trim(),
        images: _selectedImages,
        privacy: _selectedPrivacy,
      );
    } else {
      PostType postType = PostType.text;
      if (_selectedVideos.isNotEmpty) {
        postType = PostType.video;
      } else if (_selectedImages.isNotEmpty) {
        postType = PostType.image;
      } else if (_isLiveStream) {
        postType = PostType.liveStream;
      } else if (_selectedMusic != null) {
        postType = PostType.music;
      } else if (_selectedLocation != null) {
        postType = PostType.location;
      } else if (_selectedFeeling != null) {
        postType = PostType.feeling;
      }

      success = await socialService.createPostAdvanced(
        authorId: currentUser.phoneNumber,
        authorName: currentUser.name,
        content: _contentController.text.trim(),
        images: _selectedImages,
        videos: _selectedVideos,
        musicUrl: _selectedMusic,
        musicTitle: _selectedMusicTitle,
        location: _selectedLocation,
        feeling: _selectedFeeling,
        backgroundColor: _selectedBackgroundColor,
        taggedUsers: _taggedUsers,
        isLiveStream: _isLiveStream,
        type: postType,
        privacy: _selectedPrivacy,
        linkPreviews: _linkPreviews,
      );
    }

    setState(() => _isLoading = false);

    if (mounted) {
      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isEditing ? 'Post updated!' : 'Post created!'),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save post. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// اختيار صورة ورفعها لـ Firebase
  void _pickImage() async {
    try {
      // إظهار شريط التقدم
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD32F2F)),
              ),
              const SizedBox(height: 16),
              const Text('🖼️ Uploading image...'),
              const SizedBox(height: 8),
              Text(
                'Please wait while we upload your image to Firebase',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );

      final mediaService = MediaService();
      final imageUrl = await mediaService.pickImage();

      // إغلاق شريط التقدم
      if (mounted) Navigator.of(context).pop();

      if (imageUrl != null && mounted) {
        setState(() {
          _selectedImages.add(imageUrl);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🖼️ Image uploaded successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error uploading image: $e');
      // إغلاق شريط التقدم في حالة الخطأ
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ Error uploading image'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إزالة صورة
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// اختيار فيديو
  void _pickVideo() async {
    final mediaService = MediaService();
    final videoPath = await mediaService.pickVideo();

    if (videoPath != null && mounted) {
      setState(() {
        _selectedVideos.add(videoPath);
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('🎥 Video added!')),
      );
    }
  }

  /// بدء البث المباشر
  void _startLiveStream() async {
    final mediaService = MediaService();
    final streamId = await mediaService.startLiveStream();

    if (streamId != null && mounted) {
      setState(() {
        _isLiveStream = true;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🔴 Live stream started!'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// اختيار الموقع
  void _pickLocation() async {
    final mediaService = MediaService();
    final location = await mediaService.getCurrentLocation();

    if (location != null && mounted) {
      setState(() {
        _selectedLocation = location['name'];
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('📍 Location: ${location['name']}')),
      );
    }
  }

  /// اختيار المشاعر
  void _pickFeeling() {
    final mediaService = MediaService();
    final feelings = mediaService.getFeelings();

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'How are you feeling?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 2.5,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: feelings.length,
              itemBuilder: (context, index) {
                final feeling = feelings[index];
                return GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedFeeling = '${feeling['emoji']} ${feeling['name']}';
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('😊 Feeling: ${feeling['name']}')),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(feeling['color']).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Color(feeling['color']),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '${feeling['emoji']} ${feeling['name']}',
                        style: TextStyle(
                          color: Color(feeling['color']),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// اختيار الموسيقى
  void _pickMusic() async {
    final mediaService = MediaService();
    final music = await mediaService.pickMusic();

    if (music != null && mounted) {
      setState(() {
        _selectedMusic = music['url'];
        _selectedMusicTitle = music['title'];
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('🎵 Music: ${music['title']}')),
      );
    }
  }

  /// اختيار لون الخلفية
  void _pickBackgroundColor() {
    final mediaService = MediaService();
    final colors = mediaService.getBackgroundColors();

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Choose Background Color',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: colors.length,
              itemBuilder: (context, index) {
                final colorData = colors[index];
                final colorList = colorData['colors'] as List<int>;
                return GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedBackgroundColor = colorData['name'];
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('🎨 Background: ${colorData['name']}')),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: colorList.map((c) => Color(c)).toList(),
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        colorData['name'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              color: Colors.black54,
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// الإشارة إلى الأشخاص
  void _tagPeople() async {
    final mediaService = MediaService();
    final contacts = await mediaService.getContacts();

    if (!mounted) return;

    if (contacts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No contacts found')),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.7,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Text(
                'Tag People',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: contacts.length,
                  itemBuilder: (context, index) {
                    final contact = contacts[index];
                    final isTagged = _taggedUsers.contains(contact['name']);

                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: const Color(0xFFD32F2F),
                        child: Text(
                          contact['name']!.isNotEmpty
                              ? contact['name']![0].toUpperCase()
                              : '?',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      title: Text(contact['name']!),
                      subtitle: Text(contact['phone']!),
                      trailing: isTagged
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : const Icon(Icons.radio_button_unchecked, color: Colors.grey),
                      onTap: () {
                        setModalState(() {
                          if (isTagged) {
                            _taggedUsers.remove(contact['name']);
                          } else {
                            _taggedUsers.add(contact['name']!);
                          }
                        });
                        setState(() {}); // تحديث الشاشة الرئيسية أيض<|im_start|>
                      },
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Selected: ${_taggedUsers.length} people',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      if (_taggedUsers.isNotEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('👥 Tagged ${_taggedUsers.length} people')),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFD32F2F),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Done'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على أيقونة الخصوصية
  IconData _getPrivacyIcon(PostPrivacy privacy) {
    switch (privacy) {
      case PostPrivacy.public:
        return Icons.public;
      case PostPrivacy.friends:
        return Icons.people;
      case PostPrivacy.private:
        return Icons.lock;
    }
  }

  /// الحصول على نص الخصوصية
  String _getPrivacyText(PostPrivacy privacy) {
    switch (privacy) {
      case PostPrivacy.public:
        return 'Public';
      case PostPrivacy.friends:
        return 'Friends';
      case PostPrivacy.private:
        return 'Only me';
    }
  }

  /// الحصول على وصف الخصوصية
  String _getPrivacyDescription(PostPrivacy privacy) {
    switch (privacy) {
      case PostPrivacy.public:
        return 'Anyone can see this post';
      case PostPrivacy.friends:
        return 'Only your friends can see this post';
      case PostPrivacy.private:
        return 'Only you can see this post';
    }
  }

  /// معالجة تغيير النص واستخراج الروابط
  void _onTextChanged(String text) {
    setState(() {});
    _extractLinksFromText(text);
  }

  /// استخراج الروابط من النص
  void _extractLinksFromText(String text) async {
    final urls = LinkPreviewService.extractUrls(text);

    if (urls.isEmpty) {
      setState(() {
        _linkPreviews.clear();
        _isLoadingLinkPreview = false;
      });
      return;
    }

    // إزالة الروابط المكررة
    final newUrls = urls.where((url) =>
      !_linkPreviews.any((preview) => preview.url == url)
    ).toList();

    if (newUrls.isEmpty) return;

    setState(() {
      _isLoadingLinkPreview = true;
    });

    try {
      for (final url in newUrls) {
        final preview = await LinkPreviewService.fetchLinkPreview(url);
        if (preview != null && mounted) {
          setState(() {
            _linkPreviews.add(preview);
          });
        }
      }
    } catch (e) {
      debugPrint('❌ Error fetching link previews: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingLinkPreview = false;
        });
      }
    }
  }

  /// بناء معاينات الروابط
  Widget _buildLinkPreviews() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Link Previews',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        ...(_linkPreviews.map((preview) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: LinkPreviewWidget(
            linkPreview: preview,
            showCloseButton: true,
            onClose: () {
              setState(() {
                _linkPreviews.remove(preview);
              });
            },
          ),
        )).toList()),
      ],
    );
  }

  /// بناء مؤشر تحميل معاينة الروابط
  Widget _buildLinkPreviewLoader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: const Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD32F2F)),
            ),
          ),
          SizedBox(width: 12),
          Text(
            'Loading link preview...',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}
