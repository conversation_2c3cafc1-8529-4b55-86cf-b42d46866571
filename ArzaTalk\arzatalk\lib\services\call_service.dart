import 'package:flutter/material.dart';
import 'sound_service.dart';

enum CallType { voice, video }
enum CallStatus { calling, ringing, connected, ended, declined, missed }

class CallModel {
  final String id;
  final String callerId;
  final String? receiverId; // null for group calls
  final String? groupId; // null for individual calls
  final CallType type;
  final CallStatus status;
  final DateTime startTime;
  final DateTime? endTime;
  final int? duration; // in seconds

  CallModel({
    required this.id,
    required this.callerId,
    this.receiverId,
    this.groupId,
    required this.type,
    required this.status,
    required this.startTime,
    this.endTime,
    this.duration,
  });

  CallModel copyWith({
    String? id,
    String? callerId,
    String? receiverId,
    String? groupId,
    CallType? type,
    CallStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    int? duration,
  }) {
    return CallModel(
      id: id ?? this.id,
      callerId: callerId ?? this.callerId,
      receiverId: receiverId ?? this.receiverId,
      groupId: groupId ?? this.groupId,
      type: type ?? this.type,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
    );
  }
}

class CallService extends ChangeNotifier {
  static final List<CallModel> _callHistory = [];
  CallModel? _currentCall;
  final SoundService _soundService = SoundService();

  List<CallModel> get callHistory => _callHistory;
  CallModel? get currentCall => _currentCall;

  // Start individual voice call
  Future<void> startVoiceCall(String callerId, String receiverId) async {
    try {
      final call = CallModel(
        id: 'call_${DateTime.now().millisecondsSinceEpoch}',
        callerId: callerId,
        receiverId: receiverId,
        type: CallType.voice,
        status: CallStatus.calling,
        startTime: DateTime.now(),
      );

      _currentCall = call;
      _callHistory.insert(0, call);
      notifyListeners();

      // تشغيل صوت بدء المكالمة
      _soundService.playCallSound(type: CallSoundType.outgoing);

      // Simulate call process
      await _simulateCall(call);
    } catch (e) {
      throw Exception('Failed to start voice call: $e');
    }
  }

  // Start individual video call
  Future<void> startVideoCall(String callerId, String receiverId) async {
    try {
      final call = CallModel(
        id: 'call_${DateTime.now().millisecondsSinceEpoch}',
        callerId: callerId,
        receiverId: receiverId,
        type: CallType.video,
        status: CallStatus.calling,
        startTime: DateTime.now(),
      );

      _currentCall = call;
      _callHistory.insert(0, call);
      notifyListeners();

      // Simulate call process
      await _simulateCall(call);
    } catch (e) {
      throw Exception('Failed to start video call: $e');
    }
  }

  // Start group voice call
  Future<void> startGroupVoiceCall(String callerId, String groupId) async {
    try {
      final call = CallModel(
        id: 'call_${DateTime.now().millisecondsSinceEpoch}',
        callerId: callerId,
        groupId: groupId,
        type: CallType.voice,
        status: CallStatus.calling,
        startTime: DateTime.now(),
      );

      _currentCall = call;
      _callHistory.insert(0, call);
      notifyListeners();

      // Simulate call process
      await _simulateCall(call);
    } catch (e) {
      throw Exception('Failed to start group voice call: $e');
    }
  }

  // Start group video call
  Future<void> startGroupVideoCall(String callerId, String groupId) async {
    try {
      final call = CallModel(
        id: 'call_${DateTime.now().millisecondsSinceEpoch}',
        callerId: callerId,
        groupId: groupId,
        type: CallType.video,
        status: CallStatus.calling,
        startTime: DateTime.now(),
      );

      _currentCall = call;
      _callHistory.insert(0, call);
      notifyListeners();

      // Simulate call process
      await _simulateCall(call);
    } catch (e) {
      throw Exception('Failed to start group video call: $e');
    }
  }

  // End current call
  Future<void> endCall() async {
    if (_currentCall != null) {
      final endTime = DateTime.now();
      final duration = endTime.difference(_currentCall!.startTime).inSeconds;

      final updatedCall = _currentCall!.copyWith(
        status: CallStatus.ended,
        endTime: endTime,
        duration: duration,
      );

      // Update in history
      final index = _callHistory.indexWhere((call) => call.id == _currentCall!.id);
      if (index != -1) {
        _callHistory[index] = updatedCall;
      }

      _currentCall = null;
      notifyListeners();

      // تشغيل صوت إنهاء المكالمة
      _soundService.playCallSound(type: CallSoundType.ended);
    }
  }

  // Decline call
  Future<void> declineCall() async {
    if (_currentCall != null) {
      final updatedCall = _currentCall!.copyWith(
        status: CallStatus.declined,
        endTime: DateTime.now(),
      );

      // Update in history
      final index = _callHistory.indexWhere((call) => call.id == _currentCall!.id);
      if (index != -1) {
        _callHistory[index] = updatedCall;
      }

      _currentCall = null;
      notifyListeners();
    }
  }

  // Simulate call process (for demo purposes)
  Future<void> _simulateCall(CallModel call) async {
    // Simulate ringing
    await Future.delayed(const Duration(seconds: 2));

    if (_currentCall?.id == call.id) {
      _currentCall = call.copyWith(status: CallStatus.ringing);

      // Update in history
      final index = _callHistory.indexWhere((c) => c.id == call.id);
      if (index != -1) {
        _callHistory[index] = _currentCall!;
      }

      notifyListeners();

      // تشغيل صوت الرنين
      _soundService.playCallSound(type: CallSoundType.incoming);

      // Simulate answer (50% chance)
      await Future.delayed(const Duration(seconds: 3));

      if (_currentCall?.id == call.id) {
        final random = DateTime.now().millisecond % 2;

        if (random == 0) {
          // Call answered
          _currentCall = call.copyWith(status: CallStatus.connected);

          // Update in history
          final index = _callHistory.indexWhere((c) => c.id == call.id);
          if (index != -1) {
            _callHistory[index] = _currentCall!;
          }

          notifyListeners();

          // تشغيل صوت الاتصال
          _soundService.playCallSound(type: CallSoundType.connected);
        } else {
          // Call missed/declined
          _currentCall = call.copyWith(
            status: CallStatus.missed,
            endTime: DateTime.now(),
          );

          // Update in history
          final index = _callHistory.indexWhere((c) => c.id == call.id);
          if (index != -1) {
            _callHistory[index] = _currentCall!;
          }

          _currentCall = null;
          notifyListeners();
        }
      }
    }
  }

  // Get call history for a specific user
  List<CallModel> getCallHistoryForUser(String userId) {
    return _callHistory.where((call) =>
      call.callerId == userId || call.receiverId == userId
    ).toList();
  }

  // Get call history for a specific group
  List<CallModel> getCallHistoryForGroup(String groupId) {
    return _callHistory.where((call) =>
      call.groupId == groupId
    ).toList();
  }

  // Clear call history
  void clearCallHistory() {
    _callHistory.clear();
    notifyListeners();
  }

  // Get call status text
  String getCallStatusText(CallModel call) {
    switch (call.status) {
      case CallStatus.calling:
        return 'Calling...';
      case CallStatus.ringing:
        return 'Ringing...';
      case CallStatus.connected:
        return 'Connected';
      case CallStatus.ended:
        return call.duration != null ? _formatDuration(call.duration!) : 'Ended';
      case CallStatus.declined:
        return 'Declined';
      case CallStatus.missed:
        return 'Missed';
    }
  }

  // Format call duration
  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  // Get call type icon
  IconData getCallTypeIcon(CallModel call) {
    switch (call.type) {
      case CallType.voice:
        return Icons.call;
      case CallType.video:
        return Icons.videocam;
    }
  }

  // Get call status color
  Color getCallStatusColor(CallModel call) {
    switch (call.status) {
      case CallStatus.calling:
      case CallStatus.ringing:
      case CallStatus.connected:
        return Colors.green;
      case CallStatus.ended:
        return Colors.blue;
      case CallStatus.declined:
      case CallStatus.missed:
        return Colors.red;
    }
  }

  // Add demo call history
  static void addDemoCallHistory() {
    final now = DateTime.now();

    final demoCalls = [
      CallModel(
        id: 'call_1',
        callerId: '+1234567890',
        receiverId: '+1987654321',
        type: CallType.voice,
        status: CallStatus.ended,
        startTime: now.subtract(const Duration(hours: 2)),
        endTime: now.subtract(const Duration(hours: 2, minutes: -5)),
        duration: 300, // 5 minutes
      ),
      CallModel(
        id: 'call_2',
        callerId: '+1987654321',
        receiverId: '+1234567890',
        type: CallType.video,
        status: CallStatus.missed,
        startTime: now.subtract(const Duration(hours: 5)),
        endTime: now.subtract(const Duration(hours: 5)),
      ),
      CallModel(
        id: 'call_3',
        callerId: '+1234567890',
        groupId: 'group_1',
        type: CallType.voice,
        status: CallStatus.ended,
        startTime: now.subtract(const Duration(days: 1)),
        endTime: now.subtract(const Duration(days: 1, minutes: -10)),
        duration: 600, // 10 minutes
      ),
    ];

    for (final call in demoCalls) {
      if (!_callHistory.any((c) => c.id == call.id)) {
        _callHistory.add(call);
      }
    }
  }
}
