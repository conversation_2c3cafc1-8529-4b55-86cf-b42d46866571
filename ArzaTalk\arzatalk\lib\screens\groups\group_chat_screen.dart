import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/auth_service.dart';
import '../../services/group_service.dart';
import '../../services/storage_service.dart';
import '../../services/call_service.dart';
import '../../models/group_model.dart';
import '../../models/message_model.dart';
import '../../widgets/message_bubble.dart';
import '../../widgets/voice_recorder.dart';
import '../calls/call_screen.dart';
import 'group_info_screen.dart';

class GroupChatScreen extends StatefulWidget {
  final GroupModel group;

  const GroupChatScreen({
    super.key,
    required this.group,
  });

  @override
  State<GroupChatScreen> createState() => _GroupChatScreenState();
}

class _GroupChatScreenState extends State<GroupChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final StorageService _storageService = StorageService();

  bool _isSending = false;
  bool _isRecording = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> _sendTextMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isSending) return;

    _messageController.clear();
    setState(() {
      _isSending = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final groupService = Provider.of<GroupService>(context, listen: false);

      await groupService.sendGroupMessage(
        groupId: widget.group.id,
        senderId: authService.currentUser!.phoneNumber,
        message: message,
        type: MessageType.text,
      );

      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error sending message: $e')),
      );
    } finally {
      setState(() {
        _isSending = false;
      });
    }
  }

  Future<void> _sendImageMessage() async {
    final imageFile = await _storageService.pickImageFromGallery();
    if (imageFile == null) return;

    setState(() {
      _isSending = true;
    });

    try {
      final imageUrl = await _storageService.uploadImage(
        imageFile,
        'group_images',
      );

      if (imageUrl != null) {
        final authService = Provider.of<AuthService>(context, listen: false);
        final groupService = Provider.of<GroupService>(context, listen: false);

        await groupService.sendGroupMessage(
          groupId: widget.group.id,
          senderId: authService.currentUser!.phoneNumber,
          message: 'Image',
          type: MessageType.image,
          mediaUrl: imageUrl,
        );
        _scrollToBottom();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error sending image: $e')),
      );
    } finally {
      setState(() {
        _isSending = false;
      });
    }
  }

  Future<void> _sendVoiceMessage(String voicePath, int duration) async {
    setState(() {
      _isSending = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final groupService = Provider.of<GroupService>(context, listen: false);

      await groupService.sendGroupMessage(
        groupId: widget.group.id,
        senderId: authService.currentUser!.phoneNumber,
        message: 'Voice message',
        type: MessageType.voice,
        mediaUrl: voicePath,
      );
      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error sending voice message: $e')),
      );
    } finally {
      setState(() {
        _isSending = false;
      });
    }
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library, color: Color(0xFFD32F2F)),
                title: const Text('Photo from Gallery'),
                onTap: () {
                  Navigator.of(context).pop();
                  _sendImageMessage();
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Color(0xFFD32F2F)),
                title: const Text('Take Photo'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final imageFile = await _storageService.takePhoto();
                  if (imageFile != null) {
                    setState(() {
                      _isSending = true;
                    });

                    final imageUrl = await _storageService.uploadImage(
                      imageFile,
                      'group_images',
                    );

                    if (imageUrl != null) {
                      final authService = Provider.of<AuthService>(context, listen: false);
                      final groupService = Provider.of<GroupService>(context, listen: false);

                      await groupService.sendGroupMessage(
                        groupId: widget.group.id,
                        senderId: authService.currentUser!.phoneNumber,
                        message: 'Image',
                        type: MessageType.image,
                        mediaUrl: imageUrl,
                      );
                      _scrollToBottom();
                    }

                    setState(() {
                      _isSending = false;
                    });
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.white,
              backgroundImage: widget.group.groupImageUrl != null
                  ? NetworkImage(widget.group.groupImageUrl!)
                  : null,
              child: widget.group.groupImageUrl == null
                  ? Text(
                      widget.group.name.isNotEmpty
                          ? widget.group.name[0].toUpperCase()
                          : 'G',
                      style: const TextStyle(
                        color: Color(0xFFD32F2F),
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.group.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${widget.group.members.length} members',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.call),
            onPressed: () => _startGroupVoiceCall(),
          ),
          IconButton(
            icon: const Icon(Icons.videocam),
            onPressed: () => _startGroupVideoCall(),
          ),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => GroupInfoScreen(group: widget.group),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages area
          Expanded(
            child: Consumer2<AuthService, GroupService>(
              builder: (context, authService, groupService, child) {
                final currentUser = authService.currentUser;
                if (currentUser == null) return const SizedBox();

                return StreamBuilder<List<MessageModel>>(
                  stream: groupService.getGroupMessages(widget.group.id),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text('Error: ${snapshot.error}'),
                      );
                    }

                    final messages = snapshot.data ?? [];

                    if (messages.isEmpty) {
                      return const Center(
                        child: Text(
                          'Start the group conversation by sending a message',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                      );
                    }

                    return ListView.builder(
                      controller: _scrollController,
                      reverse: true,
                      itemCount: messages.length,
                      itemBuilder: (context, index) {
                        final message = messages[index];
                        final isMe = message.senderId == currentUser.phoneNumber;

                        return MessageBubble(
                          message: message,
                          isMe: isMe,
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),

          // Message input area
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Text field
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    textAlign: TextAlign.left,
                    decoration: InputDecoration(
                      hintText: 'Type a message...',
                      prefixIcon: IconButton(
                        icon: const Icon(
                          Icons.attach_file,
                          color: Color(0xFFD32F2F),
                        ),
                        onPressed: _showAttachmentOptions,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 10,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendTextMessage(),
                  ),
                ),

                const SizedBox(width: 8),

                // Send button or voice recorder
                ValueListenableBuilder<TextEditingValue>(
                  valueListenable: _messageController,
                  builder: (context, value, child) {
                    return value.text.trim().isNotEmpty || _isSending
                        ? IconButton(
                            icon: _isSending
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(
                                    Icons.send,
                                    color: Color(0xFFD32F2F),
                                  ),
                            onPressed: _isSending ? null : _sendTextMessage,
                          )
                        : VoiceRecorder(
                            onVoiceRecorded: _sendVoiceMessage,
                            isRecording: _isRecording,
                            onRecordingStateChanged: (isRecording) {
                              setState(() {
                                _isRecording = isRecording;
                              });
                            },
                          );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startGroupVoiceCall() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final callService = Provider.of<CallService>(context, listen: false);

      await callService.startGroupVoiceCall(
        authService.currentUser!.phoneNumber,
        widget.group.id,
      );

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CallScreen(
              group: widget.group,
              callType: CallType.voice,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting call: $e')),
        );
      }
    }
  }

  Future<void> _startGroupVideoCall() async {
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final callService = Provider.of<CallService>(context, listen: false);

      await callService.startGroupVideoCall(
        authService.currentUser!.phoneNumber,
        widget.group.id,
      );

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CallScreen(
              group: widget.group,
              callType: CallType.video,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting call: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
