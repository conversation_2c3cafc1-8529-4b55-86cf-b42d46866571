/// نموذج الإشعارات للشبكة الاجتماعية
class NotificationModel {
  final String id;
  final String userId; // المستخدم الذي سيستقبل الإشعار
  final String fromUserId; // المستخدم الذي قام بالإجراء
  final String fromUserName;
  final String fromUserAvatar;
  final String type; // نوع الإشعار
  final String title;
  final String message;
  final String? postId; // معرف المنشور (إن وجد)
  final String? commentId; // معرف التعليق (إن وجد)
  final DateTime createdAt;
  final bool isRead;
  final Map<String, dynamic> data; // بيانات إضافية

  NotificationModel({
    required this.id,
    required this.userId,
    required this.fromUserId,
    required this.fromUserName,
    required this.fromUserAvatar,
    required this.type,
    required this.title,
    required this.message,
    this.postId,
    this.commentId,
    required this.createdAt,
    this.isRead = false,
    this.data = const {},
  });

  /// تحويل من JSON
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      fromUserId: json['fromUserId'] ?? '',
      fromUserName: json['fromUserName'] ?? '',
      fromUserAvatar: json['fromUserAvatar'] ?? '',
      type: json['type'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      postId: json['postId'],
      commentId: json['commentId'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      isRead: json['isRead'] ?? false,
      data: Map<String, dynamic>.from(json['data'] ?? {}),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'fromUserId': fromUserId,
      'fromUserName': fromUserName,
      'fromUserAvatar': fromUserAvatar,
      'type': type,
      'title': title,
      'message': message,
      'postId': postId,
      'commentId': commentId,
      'createdAt': createdAt.toIso8601String(),
      'isRead': isRead,
      'data': data,
    };
  }

  /// نسخ مع تعديل
  NotificationModel copyWith({
    String? id,
    String? userId,
    String? fromUserId,
    String? fromUserName,
    String? fromUserAvatar,
    String? type,
    String? title,
    String? message,
    String? postId,
    String? commentId,
    DateTime? createdAt,
    bool? isRead,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fromUserId: fromUserId ?? this.fromUserId,
      fromUserName: fromUserName ?? this.fromUserName,
      fromUserAvatar: fromUserAvatar ?? this.fromUserAvatar,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      postId: postId ?? this.postId,
      commentId: commentId ?? this.commentId,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
    );
  }

  /// الحصول على أيقونة الإشعار
  String get iconEmoji {
    switch (type) {
      case 'like':
        return '👍';
      case 'love':
        return '❤️';
      case 'comment':
        return '💬';
      case 'reply':
        return '↩️';
      case 'share':
        return '🔄';
      case 'post':
        return '📝';
      case 'story':
        return '📸';
      case 'follow':
        return '👥';
      case 'mention':
        return '@';
      default:
        return '🔔';
    }
  }

  /// الحصول على لون الإشعار
  String get colorHex {
    switch (type) {
      case 'like':
        return '#1877F2'; // أزرق Facebook
      case 'love':
        return '#E91E63'; // وردي
      case 'comment':
      case 'reply':
        return '#42B883'; // أخضر
      case 'share':
        return '#FF9800'; // برتقالي
      case 'post':
      case 'story':
        return '#9C27B0'; // بنفسجي
      case 'follow':
        return '#2196F3'; // أزرق فاتح
      case 'mention':
        return '#FF5722'; // أحمر برتقالي
      default:
        return '#65676B'; // رمادي
    }
  }

  /// الحصول على الوقت النسبي
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      return '${(difference.inDays / 7).floor()}w';
    }
  }
}

/// أنواع الإشعارات
class NotificationTypes {
  static const String like = 'like';
  static const String love = 'love';
  static const String comment = 'comment';
  static const String reply = 'reply';
  static const String share = 'share';
  static const String post = 'post';
  static const String story = 'story';
  static const String follow = 'follow';
  static const String mention = 'mention';
}
