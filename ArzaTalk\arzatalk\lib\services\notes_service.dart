import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/note_model.dart';

/// خدمة إدارة الملاحظات (مثل Facebook Notes)
class NotesService extends ChangeNotifier {
  List<NoteModel> _notes = [];
  bool _isLoading = false;

  List<NoteModel> get notes => _notes;
  bool get isLoading => _isLoading;

  /// الحصول على الملاحظات النشطة فقط
  List<NoteModel> get activeNotes {
    return _notes.where((note) => note.isActive && !note.isExpired).toList();
  }

  /// الحصول على ملاحظات مستخدم معين
  List<NoteModel> getUserNotes(String userId) {
    return _notes.where((note) => 
      note.userId == userId && note.isActive && !note.isExpired
    ).toList();
  }

  /// تحميل الملاحظات من التخزين المحلي
  Future<void> loadNotes() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final notesJson = prefs.getString('user_notes');
      
      if (notesJson != null) {
        final decoded = json.decode(notesJson) as List;
        _notes = decoded.map((noteMap) => NoteModel.fromMap(noteMap)).toList();
        
        // إزالة الملاحظات المنتهية الصلاحية
        _notes.removeWhere((note) => note.isExpired);
        await _saveNotes();
      }
    } catch (e) {
      debugPrint('❌ Error loading notes: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// حفظ الملاحظات في التخزين المحلي
  Future<void> _saveNotes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notesJson = json.encode(_notes.map((note) => note.toMap()).toList());
      await prefs.setString('user_notes', notesJson);
    } catch (e) {
      debugPrint('❌ Error saving notes: $e');
    }
  }

  /// إضافة ملاحظة جديدة
  Future<bool> addNote({
    required String userId,
    required String userName,
    required String content,
    required Color backgroundColor,
    String? userProfileImage,
  }) async {
    try {
      final note = NoteModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: userId,
        userName: userName,
        userProfileImage: userProfileImage,
        content: content,
        backgroundColor: backgroundColor,
        createdAt: DateTime.now(),
      );

      // إزالة الملاحظة السابقة للمستخدم إن وجدت
      _notes.removeWhere((n) => n.userId == userId);
      
      // إضافة الملاحظة الجديدة في المقدمة
      _notes.insert(0, note);
      
      await _saveNotes();
      notifyListeners();
      
      debugPrint('✅ Note added successfully for user: $userName');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding note: $e');
      return false;
    }
  }

  /// تحديث ملاحظة
  Future<bool> updateNote(String noteId, {
    String? content,
    Color? backgroundColor,
  }) async {
    try {
      final noteIndex = _notes.indexWhere((note) => note.id == noteId);
      if (noteIndex == -1) return false;

      final updatedNote = _notes[noteIndex].copyWith(
        content: content,
        backgroundColor: backgroundColor,
        updatedAt: DateTime.now(),
      );

      _notes[noteIndex] = updatedNote;
      await _saveNotes();
      notifyListeners();
      
      return true;
    } catch (e) {
      debugPrint('❌ Error updating note: $e');
      return false;
    }
  }

  /// حذف ملاحظة
  Future<bool> deleteNote(String noteId) async {
    try {
      _notes.removeWhere((note) => note.id == noteId);
      await _saveNotes();
      notifyListeners();
      
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting note: $e');
      return false;
    }
  }

  /// إضافة مشاهد للملاحظة
  Future<void> addNoteViewer(String noteId, String viewerId) async {
    try {
      final noteIndex = _notes.indexWhere((note) => note.id == noteId);
      if (noteIndex == -1) return;

      final updatedNote = _notes[noteIndex].addViewer(viewerId);
      _notes[noteIndex] = updatedNote;
      
      await _saveNotes();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error adding note viewer: $e');
    }
  }

  /// الحصول على ملاحظة بالمعرف
  NoteModel? getNoteById(String noteId) {
    try {
      return _notes.firstWhere((note) => note.id == noteId);
    } catch (e) {
      return null;
    }
  }

  /// تنظيف الملاحظات المنتهية الصلاحية
  Future<void> cleanExpiredNotes() async {
    final initialCount = _notes.length;
    _notes.removeWhere((note) => note.isExpired);
    
    if (_notes.length != initialCount) {
      await _saveNotes();
      notifyListeners();
      debugPrint('🧹 Cleaned ${initialCount - _notes.length} expired notes');
    }
  }

  /// إحصائيات الملاحظات
  Map<String, int> get notesStats {
    return {
      'total': _notes.length,
      'active': activeNotes.length,
      'expired': _notes.where((note) => note.isExpired).length,
    };
  }
}
