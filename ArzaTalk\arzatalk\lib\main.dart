import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/auth_service.dart';
import 'services/chat_service.dart';
import 'services/group_service.dart';
import 'services/call_service.dart';
// import 'services/sound_service.dart'; // معطل مؤقتاً
import 'services/theme_service.dart';
import 'services/storage_service.dart';
import 'services/ringtone_service.dart';
import 'services/chat_lock_service.dart';
import 'services/story_service.dart';
import 'services/stories_service.dart';
import 'services/ai_service.dart';
import 'services/app_lock_service.dart';
import 'services/advanced_theme_service.dart';
import 'services/usage_stats_service.dart';
import 'services/quick_replies_service.dart';
import 'services/reactions_service.dart';
import 'services/pinned_messages_service.dart';
import 'services/chat_themes_service.dart';
import 'services/stickers_service.dart';
import 'services/social_feed_service.dart';
import 'services/users_service.dart';
import 'services/post_management_service.dart';
import 'services/notifications_service.dart';
import 'services/saved_items_service.dart';
import 'services/video_settings_service.dart';
import 'services/profile_customization_service.dart';
import 'services/media_service.dart';
import 'services/notes_service.dart';

import 'screens/welcome/welcome_screen.dart';
import 'screens/auth/phone_auth_screen.dart';
import 'screens/home/<USER>';
import 'screens/custom_splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('✅ Firebase initialized successfully');
  } catch (e) {
    debugPrint('❌ Error initializing Firebase: $e');
  }

  // إضافة مستخدمين تجريبيين - معطل لاستخدام Firebase
  // _addDemoUsers();

  runApp(const ArzaTalkApp());
}

// Demo users disabled - using Firebase only

class ArzaTalkApp extends StatelessWidget {
  const ArzaTalkApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthService()),
        ChangeNotifierProvider(create: (_) => ChatService()),
        ChangeNotifierProvider(create: (_) => GroupService()),
        // ChangeNotifierProvider(create: (_) => ContactService()), // معطل مؤقتاً
        ChangeNotifierProvider(create: (_) => CallService()),
        ChangeNotifierProvider(create: (_) => ThemeService()),
        ChangeNotifierProvider(create: (_) => RingtoneService()),
        ChangeNotifierProvider(create: (_) => ChatLockService()),
        ChangeNotifierProvider(create: (_) => StoryService()),
        ChangeNotifierProvider(create: (_) => StoriesService()),
        ChangeNotifierProvider(create: (_) => AIService()),
        ChangeNotifierProvider(create: (_) => AppLockService()),
        ChangeNotifierProvider(create: (_) => AdvancedThemeService()),
        ChangeNotifierProvider(create: (_) => UsageStatsService()),
        ChangeNotifierProvider(create: (_) => QuickRepliesService()),
        ChangeNotifierProvider(create: (_) => ReactionsService()),
        ChangeNotifierProvider(create: (_) => PinnedMessagesService()),
        ChangeNotifierProvider(create: (_) => ChatThemesService()),
        ChangeNotifierProvider(create: (_) => StickersService()),
        ChangeNotifierProvider(create: (_) => SocialFeedService()),
        ChangeNotifierProvider(create: (_) => UsersService()),
        ChangeNotifierProvider(create: (_) => PostManagementService()),
        ChangeNotifierProvider(create: (_) => ProfileCustomizationService()),
        ChangeNotifierProvider(create: (_) => MediaService()),
        ChangeNotifierProvider(create: (_) => NotesService()),
        ChangeNotifierProvider(create: (_) => NotificationsService()),
        ChangeNotifierProvider(create: (_) => SavedItemsService()),
        ChangeNotifierProvider(create: (_) => VideoSettingsService()),
        Provider(create: (_) => StorageService()),
      ],
      child: Consumer<ThemeService>(
        builder: (context, themeService, child) {
          return MaterialApp(
            title: 'ArzaTalk',
            debugShowCheckedModeBanner: false,

            // Language and direction settings
            locale: const Locale('en', 'US'), // English US
            supportedLocales: const [
              Locale('en', 'US'), // English
              Locale('ar', 'SA'), // Arabic
            ],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],

            // Theme settings
            theme: themeService.lightTheme,
            darkTheme: themeService.darkTheme,
            themeMode: themeService.currentTheme == AppTheme.system
                ? ThemeMode.system
                : themeService.isDarkMode
                    ? ThemeMode.dark
                    : ThemeMode.light,

            // Force left-to-right direction
            builder: (context, child) {
              return Directionality(
                textDirection: TextDirection.ltr,
                child: child!,
              );
            },

            home: const CustomSplashScreen(),
          );
        },
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isLoggedIn = false;
  bool _isLoading = true;
  bool _isFirstTime = true;

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final phoneNumber = prefs.getString('user_phone');
    final hasSeenWelcome = prefs.getBool('has_seen_welcome') ?? false;

    if (phoneNumber != null && phoneNumber.isNotEmpty) {
      // تحديث AuthService بمعلومات المستخدم
      if (mounted) {
        final authService = Provider.of<AuthService>(context, listen: false);
        final notesService = Provider.of<NotesService>(context, listen: false);

        await authService.loadUserFromPrefs();
        await notesService.loadNotes(); // تحميل الملاحظات

        setState(() {
          _isLoggedIn = true;
          _isLoading = false;
          _isFirstTime = false;
        });
      }
    } else {
      setState(() {
        _isLoggedIn = false;
        _isLoading = false;
        _isFirstTime = !hasSeenWelcome;
      });
    }
  }

  Future<void> _markWelcomeSeen() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_seen_welcome', true);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            color: Color(0xFFD32F2F),
          ),
        ),
      );
    }

    if (_isLoggedIn) {
      return const HomeScreen();
    } else if (_isFirstTime) {
      // Mark welcome as seen when showing welcome screen
      _markWelcomeSeen();
      return const WelcomeScreen();
    } else {
      return const PhoneAuthScreen();
    }
  }
}
