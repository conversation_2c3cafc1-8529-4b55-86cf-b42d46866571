import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../services/auth_service.dart';
import '../services/social_feed_service.dart';

/// ويدجت إدخال التعليقات مع الوسائط (مثل Facebook)
class CommentInputWidget extends StatefulWidget {
  final String postId;
  final String? parentCommentId;
  final VoidCallback? onCommentAdded;
  final String hintText;

  const CommentInputWidget({
    super.key,
    required this.postId,
    this.parentCommentId,
    this.onCommentAdded,
    this.hintText = 'Write a comment...',
  });

  @override
  State<CommentInputWidget> createState() => _CommentInputWidgetState();
}

class _CommentInputWidgetState extends State<CommentInputWidget> {
  final TextEditingController _controller = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  List<XFile> _selectedImages = [];
  List<XFile> _selectedVideos = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// إرسال التعليق
  Future<void> _sendComment() async {
    final content = _controller.text.trim();
    if (content.isEmpty && _selectedImages.isEmpty && _selectedVideos.isEmpty) return;

    setState(() => _isLoading = true);

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final socialService = Provider.of<SocialFeedService>(context, listen: false);
      final currentUser = authService.currentUser;

      if (currentUser != null) {
        // إرسال التعليق مع الوسائط
        final success = await socialService.addCommentWithMedia(
          postId: widget.postId,
          authorId: currentUser.phoneNumber,
          authorName: currentUser.name,
          content: content,
          authorAvatar: currentUser.profileImageUrl,
          parentCommentId: widget.parentCommentId,
          images: _selectedImages,
          videos: _selectedVideos,
        );

        if (success) {
          _controller.clear();
          _selectedImages.clear();
          _selectedVideos.clear();
          widget.onCommentAdded?.call();

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Comment posted successfully!'),
              backgroundColor: Color(0xFF42B883),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to post comment'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// اختيار الصور
  Future<void> _pickImages() async {
    try {
      final images = await _picker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error picking images: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// اختيار الفيديوهات
  Future<void> _pickVideo() async {
    try {
      final video = await _picker.pickVideo(source: ImageSource.gallery);
      if (video != null) {
        setState(() {
          _selectedVideos.add(video);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error picking video: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إزالة صورة
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  /// إزالة فيديو
  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Column(
        children: [
          // عرض الوسائط المختارة
          if (_selectedImages.isNotEmpty || _selectedVideos.isNotEmpty) ...[
            _buildSelectedMedia(),
            const SizedBox(height: 8),
          ],

          // منطقة الإدخال
          Row(
            children: [
              // صورة المستخدم
              Consumer<AuthService>(
                builder: (context, authService, child) {
                  final user = authService.currentUser;
                  return CircleAvatar(
                    radius: 16,
                    backgroundColor: const Color(0xFFE4E6EA),
                    backgroundImage: user?.profileImageUrl?.isNotEmpty == true
                        ? NetworkImage(user!.profileImageUrl!)
                        : null,
                    child: user?.profileImageUrl?.isEmpty != false
                        ? Text(
                            user?.name.isNotEmpty == true ? user!.name[0].toUpperCase() : 'U',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF65676B),
                            ),
                          )
                        : null,
                  );
                },
              ),

              const SizedBox(width: 8),

              // حقل الإدخال
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFF0F2F5),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: const Color(0xFFE4E6EA)),
                  ),
                  child: Row(
                    children: [
                      // النص
                      Expanded(
                        child: TextField(
                          controller: _controller,
                          decoration: InputDecoration(
                            hintText: widget.hintText,
                            hintStyle: const TextStyle(
                              color: Color(0xFF65676B),
                              fontSize: 14,
                            ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          maxLines: null,
                          textInputAction: TextInputAction.newline,
                        ),
                      ),

                      // أزرار الوسائط مثل Facebook
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // زر الصور
                          GestureDetector(
                            onTap: _pickImages,
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1877F2).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Icon(
                                Icons.photo_library,
                                color: Color(0xFF1877F2),
                                size: 18,
                              ),
                            ),
                          ),

                          const SizedBox(width: 8),

                          // زر الفيديو
                          GestureDetector(
                            onTap: _pickVideo,
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1877F2).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Icon(
                                Icons.videocam,
                                color: Color(0xFF1877F2),
                                size: 18,
                              ),
                            ),
                          ),

                          const SizedBox(width: 8),

                          // زر الملصقات
                          GestureDetector(
                            onTap: () {
                              // يمكن إضافة ملصقات لاحق<|im_start|>
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Stickers coming soon!'),
                                  backgroundColor: Color(0xFF1877F2),
                                ),
                              );
                            },
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1877F2).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: const Icon(
                                Icons.emoji_emotions,
                                color: Color(0xFF1877F2),
                                size: 18,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // زر الإرسال
              GestureDetector(
                onTap: _isLoading ? null : _sendComment,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: _isLoading
                        ? Colors.grey[400]
                        : const Color(0xFF1877F2),
                    shape: BoxShape.circle,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(
                          Icons.send,
                          color: Colors.white,
                          size: 16,
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عرض الوسائط المختارة
  Widget _buildSelectedMedia() {
    return Container(
      height: 80,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          // الصور
          ..._selectedImages.asMap().entries.map((entry) {
            final index = entry.key;
            final image = entry.value;
            return Container(
              margin: const EdgeInsets.only(right: 8),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(image.path),
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: GestureDetector(
                      onTap: () => _removeImage(index),
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: Colors.black54,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),

          // الفيديوهات
          ..._selectedVideos.asMap().entries.map((entry) {
            final index = entry.key;
            final video = entry.value;
            return Container(
              margin: const EdgeInsets.only(right: 8),
              child: Stack(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.black12,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.play_circle_fill,
                      color: Color(0xFF1877F2),
                      size: 32,
                    ),
                  ),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: GestureDetector(
                      onTap: () => _removeVideo(index),
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: Colors.black54,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
