import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// خدمة إدارة المنشورات والتفاعلات
class PostManagementService extends ChangeNotifier {
  static final PostManagementService _instance = PostManagementService._internal();
  factory PostManagementService() => _instance;
  PostManagementService._internal();

  // قوائم المنشورات المخفية والمحظورة
  final Set<String> _hiddenPosts = {};
  final Set<String> _notInterestedPosts = {};
  final Set<String> _blockedUsers = {};
  final Set<String> _reportedPosts = {};
  final Map<String, DateTime> _temporaryHiddenPosts = {};

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadData();
  }

  /// إخفاء منشور نهائياً
  Future<bool> hidePost(String postId, String reason) async {
    try {
      _hiddenPosts.add(postId);
      await _saveData();
      notifyListeners();
      
      debugPrint('🙈 Post hidden: $postId (Reason: $reason)');
      return true;
    } catch (e) {
      debugPrint('❌ Error hiding post: $e');
      return false;
    }
  }

  /// إخفاء منشور مؤقتاً لمدة 30 يوم
  Future<bool> hidePostTemporarily(String postId) async {
    try {
      final hideUntil = DateTime.now().add(const Duration(days: 30));
      _temporaryHiddenPosts[postId] = hideUntil;
      await _saveData();
      notifyListeners();
      
      debugPrint('⏰ Post hidden temporarily: $postId until $hideUntil');
      return true;
    } catch (e) {
      debugPrint('❌ Error hiding post temporarily: $e');
      return false;
    }
  }

  /// عدم الاهتمام بالمنشور
  Future<bool> markNotInterested(String postId, String authorId) async {
    try {
      _notInterestedPosts.add(postId);
      await _saveData();
      notifyListeners();
      
      debugPrint('😐 Marked not interested: $postId from $authorId');
      return true;
    } catch (e) {
      debugPrint('❌ Error marking not interested: $e');
      return false;
    }
  }

  /// الإبلاغ عن منشور
  Future<bool> reportPost(String postId, String authorId, String reason) async {
    try {
      _reportedPosts.add(postId);
      
      // حفظ تفاصيل البلاغ
      final prefs = await SharedPreferences.getInstance();
      final reportData = {
        'postId': postId,
        'authorId': authorId,
        'reason': reason,
        'reportedAt': DateTime.now().toIso8601String(),
      };
      
      final reportsJson = prefs.getString('reported_posts_details') ?? '[]';
      final reportsList = json.decode(reportsJson) as List;
      reportsList.add(reportData);
      
      await prefs.setString('reported_posts_details', json.encode(reportsList));
      await _saveData();
      notifyListeners();
      
      debugPrint('🚨 Post reported: $postId (Reason: $reason)');
      return true;
    } catch (e) {
      debugPrint('❌ Error reporting post: $e');
      return false;
    }
  }

  /// حظر مستخدم
  Future<bool> blockUser(String userId) async {
    try {
      _blockedUsers.add(userId);
      await _saveData();
      notifyListeners();
      
      debugPrint('🚫 User blocked: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ Error blocking user: $e');
      return false;
    }
  }

  /// إلغاء حظر مستخدم
  Future<bool> unblockUser(String userId) async {
    try {
      _blockedUsers.remove(userId);
      await _saveData();
      notifyListeners();
      
      debugPrint('✅ User unblocked: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ Error unblocking user: $e');
      return false;
    }
  }

  /// إظهار منشور مخفي
  Future<bool> unhidePost(String postId) async {
    try {
      _hiddenPosts.remove(postId);
      _temporaryHiddenPosts.remove(postId);
      await _saveData();
      notifyListeners();
      
      debugPrint('👁️ Post unhidden: $postId');
      return true;
    } catch (e) {
      debugPrint('❌ Error unhiding post: $e');
      return false;
    }
  }

  /// التراجع عن عدم الاهتمام
  Future<bool> undoNotInterested(String postId) async {
    try {
      _notInterestedPosts.remove(postId);
      await _saveData();
      notifyListeners();
      
      debugPrint('🔄 Undo not interested: $postId');
      return true;
    } catch (e) {
      debugPrint('❌ Error undoing not interested: $e');
      return false;
    }
  }

  /// التحقق من إخفاء المنشور
  bool isPostHidden(String postId) {
    // التحقق من الإخفاء النهائي
    if (_hiddenPosts.contains(postId)) return true;
    
    // التحقق من الإخفاء المؤقت
    if (_temporaryHiddenPosts.containsKey(postId)) {
      final hideUntil = _temporaryHiddenPosts[postId]!;
      if (DateTime.now().isBefore(hideUntil)) {
        return true;
      } else {
        // انتهت مدة الإخفاء المؤقت
        _temporaryHiddenPosts.remove(postId);
        _saveData();
      }
    }
    
    return false;
  }

  /// التحقق من عدم الاهتمام بالمنشور
  bool isNotInterested(String postId) {
    return _notInterestedPosts.contains(postId);
  }

  /// التحقق من حظر المستخدم
  bool isUserBlocked(String userId) {
    return _blockedUsers.contains(userId);
  }

  /// التحقق من الإبلاغ عن المنشور
  bool isPostReported(String postId) {
    return _reportedPosts.contains(postId);
  }

  /// فلترة المنشورات حسب الإعدادات
  List<T> filterPosts<T>(List<T> posts, String Function(T) getPostId, String Function(T) getAuthorId) {
    return posts.where((post) {
      final postId = getPostId(post);
      final authorId = getAuthorId(post);
      
      // إخفاء المنشورات المخفية
      if (isPostHidden(postId)) return false;
      
      // إخفاء منشورات المستخدمين المحظورين
      if (isUserBlocked(authorId)) return false;
      
      // إخفاء المنشورات غير المهتم بها (اختياري)
      // if (isNotInterested(postId)) return false;
      
      return true;
    }).toList();
  }

  /// الحصول على إحصائيات الإدارة
  Map<String, dynamic> getManagementStats() {
    // تنظيف المنشورات المؤقتة المنتهية الصلاحية
    _cleanExpiredTemporaryHides();
    
    return {
      'hiddenPosts': _hiddenPosts.length,
      'temporaryHiddenPosts': _temporaryHiddenPosts.length,
      'notInterestedPosts': _notInterestedPosts.length,
      'blockedUsers': _blockedUsers.length,
      'reportedPosts': _reportedPosts.length,
    };
  }

  /// تنظيف المنشورات المؤقتة المنتهية الصلاحية
  void _cleanExpiredTemporaryHides() {
    final now = DateTime.now();
    final expiredPosts = _temporaryHiddenPosts.entries
        .where((entry) => now.isAfter(entry.value))
        .map((entry) => entry.key)
        .toList();
    
    for (final postId in expiredPosts) {
      _temporaryHiddenPosts.remove(postId);
    }
    
    if (expiredPosts.isNotEmpty) {
      _saveData();
      debugPrint('🧹 Cleaned ${expiredPosts.length} expired temporary hides');
    }
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل المنشورات المخفية
      final hiddenPostsJson = prefs.getString('hidden_posts') ?? '[]';
      final hiddenPostsList = json.decode(hiddenPostsJson) as List;
      _hiddenPosts.clear();
      _hiddenPosts.addAll(hiddenPostsList.cast<String>());
      
      // تحميل المنشورات غير المهتم بها
      final notInterestedJson = prefs.getString('not_interested_posts') ?? '[]';
      final notInterestedList = json.decode(notInterestedJson) as List;
      _notInterestedPosts.clear();
      _notInterestedPosts.addAll(notInterestedList.cast<String>());
      
      // تحميل المستخدمين المحظورين
      final blockedUsersJson = prefs.getString('blocked_users') ?? '[]';
      final blockedUsersList = json.decode(blockedUsersJson) as List;
      _blockedUsers.clear();
      _blockedUsers.addAll(blockedUsersList.cast<String>());
      
      // تحميل المنشورات المبلغ عنها
      final reportedPostsJson = prefs.getString('reported_posts') ?? '[]';
      final reportedPostsList = json.decode(reportedPostsJson) as List;
      _reportedPosts.clear();
      _reportedPosts.addAll(reportedPostsList.cast<String>());
      
      // تحميل المنشورات المخفية مؤقتاً
      final tempHiddenJson = prefs.getString('temporary_hidden_posts') ?? '{}';
      final tempHiddenMap = json.decode(tempHiddenJson) as Map<String, dynamic>;
      _temporaryHiddenPosts.clear();
      tempHiddenMap.forEach((key, value) {
        _temporaryHiddenPosts[key] = DateTime.parse(value);
      });
      
      debugPrint('📊 Loaded post management data');
    } catch (e) {
      debugPrint('❌ Error loading post management data: $e');
    }
  }

  /// حفظ البيانات
  Future<void> _saveData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // حفظ المنشورات المخفية
      await prefs.setString('hidden_posts', json.encode(_hiddenPosts.toList()));
      
      // حفظ المنشورات غير المهتم بها
      await prefs.setString('not_interested_posts', json.encode(_notInterestedPosts.toList()));
      
      // حفظ المستخدمين المحظورين
      await prefs.setString('blocked_users', json.encode(_blockedUsers.toList()));
      
      // حفظ المنشورات المبلغ عنها
      await prefs.setString('reported_posts', json.encode(_reportedPosts.toList()));
      
      // حفظ المنشورات المخفية مؤقتاً
      final tempHiddenMap = <String, String>{};
      _temporaryHiddenPosts.forEach((key, value) {
        tempHiddenMap[key] = value.toIso8601String();
      });
      await prefs.setString('temporary_hidden_posts', json.encode(tempHiddenMap));
      
    } catch (e) {
      debugPrint('❌ Error saving post management data: $e');
    }
  }

  // Getters
  Set<String> get hiddenPosts => Set.from(_hiddenPosts);
  Set<String> get notInterestedPosts => Set.from(_notInterestedPosts);
  Set<String> get blockedUsers => Set.from(_blockedUsers);
  Set<String> get reportedPosts => Set.from(_reportedPosts);
  Map<String, DateTime> get temporaryHiddenPosts => Map.from(_temporaryHiddenPosts);
}
