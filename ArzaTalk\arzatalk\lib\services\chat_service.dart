import 'package:flutter/material.dart';
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/message_model.dart';
import '../models/chat_model.dart';
import '../models/user_model.dart';
import 'auth_service.dart';
import 'sound_service.dart';
import 'ringtone_service.dart';
import 'simple_sound_service.dart';

class ChatService extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // قوائم محلية لتخزين البيانات
  static final List<MessageModel> _localMessages = [];
  static final List<ChatModel> _localChats = [];
  final SoundService _soundService = SoundService();
  final RingtoneService _ringtoneService = RingtoneService();
  final SimpleSoundService _simpleSoundService = SimpleSoundService();

  // Stream controllers للبيانات المباشرة
  final StreamController<List<MessageModel>> _messagesController =
      StreamController<List<MessageModel>>.broadcast();
  final StreamController<List<ChatModel>> _chatsController =
      StreamController<List<ChatModel>>.broadcast();

  // إرسال رسالة
  Future<bool> sendMessage({
    required String senderId,
    required String receiverId,
    required String message,
    required MessageType type,
    String? mediaUrl,
    int? duration,
  }) async {
    try {
      final messageModel = MessageModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        senderId: senderId,
        receiverId: receiverId,
        message: message,
        type: type,
        timestamp: DateTime.now(),
        mediaUrl: mediaUrl,
        duration: duration,
      );

      // حفظ الرسالة في Firestore
      await _saveMessageToFirestore(messageModel);

      // إضافة الرسالة إلى القائمة المحلية
      _localMessages.add(messageModel);

      // تحديث أو إنشاء المحادثة
      _updateOrCreateChat(messageModel);

      // إشعار المستمعين
      _updateStreams();

      // تشغيل صوت إرسال الرسالة
      _soundService.playMessageSound(isOutgoing: true);

      // تشغيل نغمة الرسالة المخصصة
      _ringtoneService.playMessageNotification();

      // تشغيل الصوت البسيط المضمون
      _simpleSoundService.playMessageNotification();

      return true;
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');
      return false;
    }
  }

  /// حفظ الرسالة في Firestore
  Future<void> _saveMessageToFirestore(MessageModel message) async {
    try {
      final chatId = ChatModel.generateChatId(message.senderId, message.receiverId);

      // حفظ الرسالة في مجموعة الرسائل
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(message.id)
          .set(message.toMap());

      // تحديث معلومات المحادثة
      await _firestore.collection('chats').doc(chatId).set({
        'participants': [message.senderId, message.receiverId],
        'lastMessage': message.toMap(),
        'lastMessageTime': message.timestamp,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      debugPrint('✅ Message saved to Firestore: ${message.id}');
    } catch (e) {
      debugPrint('❌ Error saving message to Firestore: $e');
      // لا نرمي خطأ هنا لأننا لا نريد إيقاف إرسال الرسالة
    }
  }

  // تحديث أو إنشاء المحادثة
  void _updateOrCreateChat(MessageModel message) {
    final chatId = ChatModel.generateChatId(message.senderId, message.receiverId);

    final existingChatIndex = _localChats.indexWhere((chat) => chat.chatId == chatId);

    final chatModel = ChatModel(
      chatId: chatId,
      participants: [message.senderId, message.receiverId],
      lastMessage: message,
      lastMessageTime: message.timestamp,
      unreadCount: 0,
    );

    if (existingChatIndex != -1) {
      _localChats[existingChatIndex] = chatModel;
    } else {
      _localChats.add(chatModel);
    }
  }

  // تحديث البيانات المباشرة
  void _updateStreams() {
    _messagesController.add(List.from(_localMessages));
    _chatsController.add(List.from(_localChats));
  }

  // الحصول على الرسائل بين مستخدمين من Firestore
  Stream<List<MessageModel>> getMessages(String userId1, String userId2) {
    try {
      final chatId = ChatModel.generateChatId(userId1, userId2);

      return _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .orderBy('timestamp', descending: true)
          .snapshots()
          .map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data();
          return MessageModel.fromMap(data, doc.id);
        }).toList();
      });
    } catch (e) {
      debugPrint('❌ Error getting messages from Firestore: $e');
      // العودة إلى البيانات المحلية في حالة الخطأ
      final filteredMessages = _localMessages.where((message) =>
        (message.senderId == userId1 && message.receiverId == userId2) ||
        (message.senderId == userId2 && message.receiverId == userId1)
      ).toList();

      filteredMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      return Stream.value(filteredMessages);
    }
  }

  // الحصول على المحادثات للمستخدم الحالي
  Stream<List<ChatModel>> getUserChats(String userId) {
    final userChats = _localChats.where((chat) =>
      chat.participants.contains(userId)
    ).toList();

    // ترتيب المحادثات حسب آخر رسالة
    userChats.sort((a, b) => b.lastMessageTime.compareTo(a.lastMessageTime));

    return Stream.value(userChats);
  }

  // Alias for getUserChats for compatibility
  Stream<List<ChatModel>> getChats(String userPhoneNumber) {
    return getUserChats(userPhoneNumber);
  }

  // الحصول على جميع المستخدمين (باستثناء المستخدم الحالي)
  Stream<List<UserModel>> getAllUsers(String currentUserId) async* {
    // الحصول على المستخدمين من AuthService
    final allUsers = await AuthService.getAllUsersStatic();
    final filteredUsers = allUsers.where((user) =>
      user.phoneNumber != currentUserId
    ).toList();

    yield filteredUsers;
  }

  // البحث عن المستخدمين
  Future<List<UserModel>> searchUsers(String query, String currentUserId) async {
    final allUsers = await AuthService.getAllUsersStatic();
    return allUsers.where((user) =>
      user.phoneNumber != currentUserId &&
      user.name.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  // تحديث حالة قراءة الرسائل
  Future<void> markMessagesAsRead(String chatId, String userId) async {
    for (int i = 0; i < _localMessages.length; i++) {
      final message = _localMessages[i];
      if (message.receiverId == userId && !message.isRead) {
        _localMessages[i] = message.copyWith(isRead: true);
      }
    }
    _updateStreams();
  }

  // الحصول على عدد الرسائل غير المقروءة
  Stream<int> getUnreadMessagesCount(String userId) {
    final unreadCount = _localMessages.where((message) =>
      message.receiverId == userId && !message.isRead
    ).length;

    return Stream.value(unreadCount);
  }

  // حذف رسالة
  Future<bool> deleteMessage(String messageId) async {
    try {
      _localMessages.removeWhere((message) => message.id == messageId);
      _updateStreams();
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف الرسالة: $e');
      return false;
    }
  }

  // حذف محادثة كاملة
  Future<bool> deleteChat(String chatId) async {
    try {
      // حذف جميع الرسائل في المحادثة
      _localMessages.removeWhere((message) => message.chatId == chatId);

      // حذف المحادثة
      _localChats.removeWhere((chat) => chat.chatId == chatId);

      _updateStreams();
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف المحادثة: $e');
      return false;
    }
  }

  // Archive/unarchive chat
  Future<void> toggleChatArchive(String chatId) async {
    try {
      final chatIndex = _localChats.indexWhere((chat) => chat.chatId == chatId);
      if (chatIndex != -1) {
        _localChats[chatIndex] = _localChats[chatIndex].copyWith(
          isArchived: !_localChats[chatIndex].isArchived,
        );
        _updateStreams();
      }
    } catch (e) {
      throw Exception('Failed to toggle chat archive: $e');
    }
  }

  // Mute/unmute chat
  Future<void> toggleChatMute(String chatId) async {
    try {
      final chatIndex = _localChats.indexWhere((chat) => chat.chatId == chatId);
      if (chatIndex != -1) {
        _localChats[chatIndex] = _localChats[chatIndex].copyWith(
          isMuted: !_localChats[chatIndex].isMuted,
        );
        _updateStreams();
      }
    } catch (e) {
      throw Exception('Failed to toggle chat mute: $e');
    }
  }

  // Clear chat messages
  Future<void> clearChatMessages(String chatId) async {
    try {
      _localMessages.removeWhere((message) => message.chatId == chatId);
      _updateStreams();
    } catch (e) {
      throw Exception('Failed to clear chat messages: $e');
    }
  }

  // Edit message
  Future<void> editMessage(String messageId, String newMessage) async {
    try {
      final messageIndex = _localMessages.indexWhere((msg) => msg.id == messageId);
      if (messageIndex != -1) {
        _localMessages[messageIndex] = _localMessages[messageIndex].copyWith(
          message: newMessage,
          isEdited: true,
        );
        _updateStreams();

        // تشغيل صوت تحرير الرسالة
        _soundService.playMessageEditedSound();
      }
    } catch (e) {
      throw Exception('Failed to edit message: $e');
    }
  }

  // Delete individual message
  Future<void> deleteIndividualMessage(String messageId) async {
    try {
      final messageIndex = _localMessages.indexWhere((msg) => msg.id == messageId);
      if (messageIndex != -1) {
        _localMessages[messageIndex] = _localMessages[messageIndex].copyWith(
          message: '',
          isDeleted: true,
        );
        _updateStreams();

        // تشغيل صوت حذف الرسالة
        _soundService.playMessageDeletedSound();
      }
    } catch (e) {
      throw Exception('Failed to delete message: $e');
    }
  }

  // إضافة مستخدمين وهميين للاختبار
  void addDemoUsers() {
    final demoUsers = [
      UserModel(
        phoneNumber: '+966501234567',
        name: 'أحمد محمد',
        lastSeen: DateTime.now().subtract(const Duration(minutes: 5)),
        isOnline: true,
        gender: Gender.male,
        age: 28,
        country: 'Saudi Arabia',
        city: 'Riyadh',
        registrationDate: DateTime.now().subtract(const Duration(days: 30)),
      ),
      UserModel(
        phoneNumber: '+966507654321',
        name: 'فاطمة علي',
        lastSeen: DateTime.now().subtract(const Duration(hours: 1)),
        isOnline: false,
        gender: Gender.female,
        age: 25,
        country: 'Saudi Arabia',
        city: 'Jeddah',
        registrationDate: DateTime.now().subtract(const Duration(days: 45)),
      ),
      UserModel(
        phoneNumber: '+966509876543',
        name: 'محمد أحمد',
        lastSeen: DateTime.now().subtract(const Duration(minutes: 30)),
        isOnline: true,
        gender: Gender.male,
        age: 32,
        country: 'Saudi Arabia',
        city: 'Dammam',
        registrationDate: DateTime.now().subtract(const Duration(days: 20)),
      ),
    ];

    // إضافة المستخدمين إلى AuthService
    for (final user in demoUsers) {
      AuthService.addUserStatic(user);
    }
  }

  @override
  void dispose() {
    _messagesController.close();
    _chatsController.close();
    super.dispose();
  }
}
