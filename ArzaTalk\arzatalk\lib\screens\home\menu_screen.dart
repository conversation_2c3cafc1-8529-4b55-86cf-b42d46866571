import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../services/auth_service.dart';
import '../../services/saved_items_service.dart';
import '../../models/user_model.dart';
import '../../models/post_model.dart';
import '../profile/profile_screen.dart';
import '../settings/settings_screen.dart';
import '../auth/phone_auth_screen.dart';

/// شاشة القائمة الاحترافية مثل Facebook
class MenuScreen extends StatelessWidget {
  const MenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Menu',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1C1E21),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        iconTheme: const IconThemeData(color: Color(0xFF1C1E21)),
      ),
      body: Consumer<AuthService>(
        builder: (context, authService, child) {
          final currentUser = authService.currentUser;

          if (currentUser == null) {
            return const Center(
              child: Text('Please login to access menu'),
            );
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // قسم الملف الشخصي
                _buildProfileSection(context, currentUser),

                const SizedBox(height: 8),

                // قسم الميزات الرئيسية
                _buildMainFeaturesSection(context),

                const SizedBox(height: 8),

                // قسم المساعدة والدعم
                _buildHelpSupportSection(context),

                const SizedBox(height: 8),

                // قسم الإعدادات
                _buildSettingsSection(context),

                const SizedBox(height: 8),

                // قسم تسجيل الخروج
                _buildLogoutSection(context, authService),

                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء قسم الملف الشخصي
  Widget _buildProfileSection(BuildContext context, UserModel user) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          radius: 30,
          backgroundColor: const Color(0xFF1877F2),
          backgroundImage: user.profileImageUrl != null
              ? NetworkImage(user.profileImageUrl!)
              : null,
          child: user.profileImageUrl == null
              ? Text(
                  user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Text(
          user.name,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1C1E21),
          ),
        ),
        subtitle: Text(
          'View your profile',
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF65676B),
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Color(0xFF65676B),
          size: 16,
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const ProfileScreen(),
            ),
          );
        },
      ),
    );
  }

  /// بناء قسم الميزات الرئيسية
  Widget _buildMainFeaturesSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuTile(
            icon: Icons.bookmark,
            iconColor: const Color(0xFF1877F2),
            title: 'Saved Items',
            subtitle: 'View your saved posts and media',
            onTap: () => _showSavedItems(context),
          ),
        ],
      ),
    );
  }

  /// بناء قسم المساعدة والدعم
  Widget _buildHelpSupportSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuTile(
            icon: Icons.help_center,
            iconColor: const Color(0xFF42B883),
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onTap: () => _showHelpSupport(context),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الإعدادات
  Widget _buildSettingsSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuTile(
            icon: Icons.settings,
            iconColor: const Color(0xFF65676B),
            title: 'Settings & Privacy',
            subtitle: 'Manage your account settings',
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// بناء قسم تسجيل الخروج
  Widget _buildLogoutSection(BuildContext context, AuthService authService) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuTile(
            icon: Icons.logout,
            iconColor: Colors.red,
            title: 'Log Out',
            subtitle: 'Sign out of your account',
            onTap: () => _showLogoutDialog(context, authService),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر القائمة
  Widget _buildMenuTile({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: iconColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1C1E21),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFF65676B),
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        color: Color(0xFF65676B),
        size: 16,
      ),
      onTap: onTap,
    );
  }

  /// عرض العناصر المحفوظة
  void _showSavedItems(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SavedItemsScreen(),
      ),
    );
  }

  /// عرض المساعدة والدعم
  void _showHelpSupport(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const HelpSupportScreen(),
      ),
    );
  }

  /// عرض حوار تسجيل الخروج
  void _showLogoutDialog(BuildContext context, AuthService authService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Log Out'),
        content: const Text('Are you sure you want to log out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await authService.logout();
              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(
                    builder: (context) => const PhoneAuthScreen(),
                  ),
                  (route) => false,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Log Out'),
          ),
        ],
      ),
    );
  }
}

/// شاشة العناصر المحفوظة المتقدمة
class SavedItemsScreen extends StatefulWidget {
  const SavedItemsScreen({super.key});

  @override
  State<SavedItemsScreen> createState() => _SavedItemsScreenState();
}

class _SavedItemsScreenState extends State<SavedItemsScreen> {
  String _selectedFilter = 'all';
  String _sortBy = 'newest';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // تحميل المنشورات المحفوظة عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final savedItemsService = Provider.of<SavedItemsService>(context, listen: false);
      savedItemsService.loadSavedPosts();
      // إضافة منشورات تجريبية إذا كانت القائمة فارغة
      if (savedItemsService.savedPosts.isEmpty) {
        savedItemsService.addDemoSavedPosts();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text(
          'Saved Items',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1C1E21),
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        iconTheme: const IconThemeData(color: Color(0xFF1C1E21)),
        actions: [
          // زر البحث
          IconButton(
            onPressed: _showSearchDialog,
            icon: const Icon(Icons.search),
          ),
          // قائمة الخيارات
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'sort':
                  _showSortDialog();
                  break;
                case 'clear_all':
                  _showClearAllDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'sort',
                child: Row(
                  children: [
                    Icon(Icons.sort, color: Color(0xFF65676B)),
                    SizedBox(width: 8),
                    Text('Sort'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Clear All'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<SavedItemsService>(
        builder: (context, savedItemsService, child) {
          final allPosts = _searchQuery.isEmpty
              ? savedItemsService.savedPosts
              : savedItemsService.searchSavedPosts(_searchQuery);

          final filteredPosts = _selectedFilter == 'all'
              ? allPosts
              : savedItemsService.getSavedPostsByType(_selectedFilter);

          if (filteredPosts.isEmpty && _searchQuery.isEmpty) {
            return _buildEmptyState();
          }

          if (filteredPosts.isEmpty && _searchQuery.isNotEmpty) {
            return _buildNoSearchResults();
          }

          return Column(
            children: [
              // شريط الفلاتر
              _buildFilterBar(savedItemsService),

              // قائمة المنشورات المحفوظة
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: filteredPosts.length,
                  itemBuilder: (context, index) {
                    final post = filteredPosts[index];
                    return _buildSavedPostTile(post, savedItemsService);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80,
            color: Color(0xFF65676B),
          ),
          SizedBox(height: 16),
          Text(
            'No saved items yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1C1E21),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Save posts, photos, and videos to view them here',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF65676B),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حالة عدم وجود نتائج بحث
  Widget _buildNoSearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.search_off,
            size: 80,
            color: Color(0xFF65676B),
          ),
          const SizedBox(height: 16),
          Text(
            'No results for "$_searchQuery"',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1C1E21),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Try searching for something else',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF65676B),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الفلاتر
  Widget _buildFilterBar(SavedItemsService service) {
    final stats = service.savedItemsStats;

    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildFilterChip('all', 'All', stats['total'] ?? 0),
          _buildFilterChip('text', 'Text', stats['text'] ?? 0),
          _buildFilterChip('image', 'Photos', stats['image'] ?? 0),
          _buildFilterChip('video', 'Videos', stats['video'] ?? 0),
          _buildFilterChip('link', 'Links', stats['link'] ?? 0),
        ],
      ),
    );
  }

  /// بناء رقاقة الفلتر
  Widget _buildFilterChip(String filter, String label, int count) {
    final isSelected = _selectedFilter == filter;

    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        selected: isSelected,
        label: Text('$label ($count)'),
        onSelected: (selected) {
          setState(() {
            _selectedFilter = filter;
          });
        },
        backgroundColor: Colors.white,
        selectedColor: const Color(0xFF1877F2).withValues(alpha: 0.1),
        checkmarkColor: const Color(0xFF1877F2),
        labelStyle: TextStyle(
          color: isSelected ? const Color(0xFF1877F2) : const Color(0xFF65676B),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  /// بناء عنصر المنشور المحفوظ
  Widget _buildSavedPostTile(PostModel post, SavedItemsService service) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: const Color(0xFF1877F2),
          backgroundImage: post.authorAvatar.isNotEmpty
              ? NetworkImage(post.authorAvatar)
              : null,
          child: post.authorAvatar.isEmpty
              ? Text(
                  post.authorName.isNotEmpty ? post.authorName[0].toUpperCase() : 'U',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Text(
          post.authorName,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Color(0xFF1C1E21),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              post.content,
              style: const TextStyle(
                color: Color(0xFF1C1E21),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                if (post.images.isNotEmpty) ...[
                  const Icon(Icons.image, size: 16, color: Color(0xFF65676B)),
                  const SizedBox(width: 4),
                  Text('${post.images.length} photos'),
                  const SizedBox(width: 12),
                ],
                if (post.videos.isNotEmpty) ...[
                  const Icon(Icons.play_circle, size: 16, color: Color(0xFF65676B)),
                  const SizedBox(width: 4),
                  const Text('Video'),
                  const SizedBox(width: 12),
                ],
                Text(
                  _formatTime(post.createdAt),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF65676B),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'unsave':
                service.unsavePost(post.id);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Post removed from saved items'),
                    backgroundColor: Colors.red,
                  ),
                );
                break;
              case 'view':
                // يمكن إضافة عرض المنشور الكامل هنا
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Opening post...'),
                    backgroundColor: Color(0xFF1877F2),
                  ),
                );
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.open_in_new, size: 20),
                  SizedBox(width: 8),
                  Text('View Post'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'unsave',
              child: Row(
                children: [
                  Icon(Icons.bookmark_remove, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Remove from Saved', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () {
          // عرض المنشور الكامل
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Opening full post...'),
              backgroundColor: Color(0xFF1877F2),
            ),
          );
        },
      ),
    );
  }

  /// تنسيق الوقت
  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${(difference.inDays / 7).floor()}w ago';
    }
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Saved Items'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Search by content or author...',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _searchQuery = '';
                _searchController.clear();
              });
            },
            child: const Text('Clear'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _searchQuery = _searchController.text;
              });
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الترتيب
  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort Saved Items'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSortOption('newest', 'Newest First'),
            _buildSortOption('oldest', 'Oldest First'),
            _buildSortOption('most_liked', 'Most Liked'),
            _buildSortOption('author', 'By Author'),
          ],
        ),
      ),
    );
  }

  /// بناء خيار الترتيب
  Widget _buildSortOption(String value, String label) {
    return RadioListTile<String>(
      title: Text(label),
      value: value,
      groupValue: _sortBy,
      onChanged: (newValue) {
        if (newValue != null) {
          setState(() {
            _sortBy = newValue;
          });
          final service = Provider.of<SavedItemsService>(context, listen: false);
          service.sortSavedPosts(_sortBy);
          Navigator.pop(context);
        }
      },
    );
  }

  /// عرض حوار مسح الكل
  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Saved Items'),
        content: const Text('Are you sure you want to remove all saved items? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final service = Provider.of<SavedItemsService>(context, listen: false);
              service.clearAllSavedPosts();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All saved items cleared'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

/// شاشة المساعدة والدعم
class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1C1E21),
        elevation: 1,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),

            // قسم المساعدة
            _buildHelpSection(),

            const SizedBox(height: 16),

            // قسم الاتصال
            _buildContactSection(),

            const SizedBox(height: 16),

            // قسم حول التطبيق
            _buildAboutSection(),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Get Help',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1C1E21),
              ),
            ),
          ),
          _buildHelpTile(
            icon: Icons.help_outline,
            title: 'Help Center',
            subtitle: 'Find answers to common questions',
            onTap: () => _launchURL('https://help.arzapress.com'),
          ),
          _buildHelpTile(
            icon: Icons.bug_report,
            title: 'Report a Problem',
            subtitle: 'Let us know about issues you\'re experiencing',
            onTap: () => _launchURL('mailto:<EMAIL>?subject=Problem Report'),
          ),
          _buildHelpTile(
            icon: Icons.feedback,
            title: 'Send Feedback',
            subtitle: 'Share your thoughts and suggestions',
            onTap: () => _launchURL('mailto:<EMAIL>?subject=Feedback'),
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Contact Us',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1C1E21),
              ),
            ),
          ),
          _buildHelpTile(
            icon: Icons.email,
            title: 'Email Support',
            subtitle: '<EMAIL>',
            onTap: () => _launchURL('mailto:<EMAIL>'),
          ),
          _buildHelpTile(
            icon: Icons.business,
            title: 'Business Inquiries',
            subtitle: '<EMAIL>',
            onTap: () => _launchURL('mailto:<EMAIL>'),
          ),
          _buildHelpTile(
            icon: Icons.phone,
            title: 'Phone Support',
            subtitle: '+212638813823',
            onTap: () => _launchURL('tel:+212638813823'),
          ),
          _buildHelpTile(
            icon: Icons.phone_android,
            title: 'Alternative Phone',
            subtitle: '+212771878802',
            onTap: () => _launchURL('tel:+212771878802'),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'About ArzaTalk',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1C1E21),
              ),
            ),
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'ArzaTalk is a comprehensive messaging and social media application designed to connect people through chat, groups, stories, and social feeds. Built with a focus on Arabic-speaking communities while supporting multiple languages.',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF65676B),
                height: 1.4,
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildHelpTile(
            icon: Icons.info,
            title: 'App Version',
            subtitle: '1.0.0',
            onTap: () {},
          ),
          _buildHelpTile(
            icon: Icons.policy,
            title: 'Privacy Policy',
            subtitle: 'Learn how we protect your data',
            onTap: () => _launchURL('https://arzapress.com/privacy'),
          ),
          _buildHelpTile(
            icon: Icons.description,
            title: 'Terms of Service',
            subtitle: 'Read our terms and conditions',
            onTap: () => _launchURL('https://arzapress.com/terms'),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: const Color(0xFF1877F2),
        size: 24,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF1C1E21),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFF65676B),
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        color: Color(0xFF65676B),
        size: 16,
      ),
      onTap: onTap,
    );
  }

  void _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }
}
