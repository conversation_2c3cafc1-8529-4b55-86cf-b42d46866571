Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9DD0, 0007FFFF8CD0) msys-2.0.dll+0x1FE8E
0007FFFF9DD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x67F9
0007FFFF9DD0  000210046832 (000210286019, 0007FFFF9C88, 0007FFFF9DD0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DD0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DD0  000210068E24 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0B0  00021006A225 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBC8700000 ntdll.dll
7FFBC79B0000 KERNEL32.DLL
7FFBC5AE0000 KERNELBASE.dll
7FFBC64C0000 USER32.dll
7FFBC5AB0000 win32u.dll
7FFBC83D0000 GDI32.dll
7FFBC6230000 gdi32full.dll
7FFBC6000000 msvcp_win.dll
7FFBC5EB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBC8400000 advapi32.dll
7FFBC7F20000 msvcrt.dll
7FFBC72F0000 sechost.dll
7FFBC7FD0000 RPCRT4.dll
7FFBC5020000 CRYPTBASE.DLL
7FFBC5A10000 bcryptPrimitives.dll
7FFBC7240000 IMM32.DLL
