<!DOCTYPE html>
<html>
<head>
    <title>ArzaTalk Icon Generator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .container {
            text-align: center;
        }
        #iconCanvas {
            border: 2px solid #ccc;
            border-radius: 10px;
            background: white;
        }
        .download-btn {
            margin-top: 20px;
            padding: 10px 20px;
            background: #E53E3E;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 ArzaTalk Icon Generator</h1>
        <canvas id="iconCanvas" width="512" height="512"></canvas>
        <br>
        <button class="download-btn" onclick="downloadIcon()">📥 Download Icon</button>
    </div>

    <script>
        const canvas = document.getElementById('iconCanvas');
        const ctx = canvas.getContext('2d');

        function createIcon() {
            // مسح الكانفاس
            ctx.clearRect(0, 0, 512, 512);

            // إنشاء تدرج أحمر
            const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 240);
            gradient.addColorStop(0, '#FF4444');
            gradient.addColorStop(0.5, '#E53E3E');
            gradient.addColorStop(1, '#C53030');

            // رسم الخلفية الدائرية
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(256, 256, 240, 0, 2 * Math.PI);
            ctx.fill();

            // رسم حلقة داخلية
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(256, 256, 220, 0, 2 * Math.PI);
            ctx.stroke();

            // رسم فقاعة الدردشة الكبيرة
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.roundRect(176, 156, 160, 160, 25);
            ctx.fill();

            // رسم ذيل الفقاعة
            ctx.beginPath();
            ctx.moveTo(156, 316);
            ctx.lineTo(176, 296);
            ctx.lineTo(176, 316);
            ctx.closePath();
            ctx.fill();

            // رسم فقاعة صغيرة
            ctx.fillStyle = '#FFE5E5';
            ctx.beginPath();
            ctx.roundRect(276, 216, 60, 35, 15);
            ctx.fill();

            // رسم ذيل الفقاعة الصغيرة
            ctx.beginPath();
            ctx.moveTo(286, 251);
            ctx.lineTo(301, 266);
            ctx.lineTo(306, 251);
            ctx.closePath();
            ctx.fill();

            // رسم نقاط الدردشة
            ctx.fillStyle = '#E53E3E';
            ctx.beginPath();
            ctx.arc(216, 216, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(246, 216, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(276, 216, 8, 0, 2 * Math.PI);
            ctx.fill();

            // رسم خطوط النص
            ctx.fillStyle = 'rgba(229, 62, 62, 0.6)';
            ctx.fillRect(196, 236, 80, 6);
            ctx.fillRect(196, 251, 60, 6);
            ctx.fillRect(196, 266, 70, 6);

            // رسم قلب صغير
            ctx.fillStyle = '#FF6B6B';
            ctx.beginPath();
            ctx.arc(301, 241, 8, 0, 2 * Math.PI);
            ctx.fill();

            // إضافة تأثير اللمعان
            const shineGradient = ctx.createRadialGradient(200, 180, 0, 200, 180, 60);
            shineGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
            shineGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            
            ctx.fillStyle = shineGradient;
            ctx.save();
            ctx.translate(200, 180);
            ctx.rotate(-Math.PI / 6);
            ctx.fillRect(-30, -15, 60, 30);
            ctx.restore();

            // إضافة نص ArzaTalk
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.font = 'bold 32px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('ArzaTalk', 256, 450);
        }

        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'app_icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // إنشاء الأيقونة عند تحميل الصفحة
        createIcon();
    </script>
</body>
</html>
