import 'package:flutter/material.dart';

class AppConstants {
  // ألوان التطبيق
  static const Color primaryColor = Color(0xFFD32F2F);
  static const Color secondaryColor = Color(0xFFE57373);
  static const Color accentColor = Color(0xFFB71C1C);
  static const Color backgroundColor = Color(0xFFFCE4EC);
  static const Color messageBackgroundColor = Color(0xFFFFEBEE);

  // أحجام النصوص
  static const double titleFontSize = 20.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 12.0;

  // المسافات
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // أحجام الصور
  static const double profileImageSize = 40.0;
  static const double largeProfileImageSize = 80.0;

  // مدة الرسوم المتحركة
  static const Duration animationDuration = Duration(milliseconds: 300);

  // أقصى حجم للملفات (بالبايت)
  static const int maxImageSize = 5 * 1024 * 1024; // 5 MB
  static const int maxAudioSize = 10 * 1024 * 1024; // 10 MB

  // أقصى مدة للتسجيل الصوتي (بالثواني)
  static const int maxRecordingDuration = 300; // 5 دقائق

  // رسائل الخطأ
  static const String networkError = 'خطأ في الاتصال بالإنترنت';
  static const String unknownError = 'حدث خطأ غير متوقع';
  static const String permissionDenied = 'تم رفض الصلاحية';
  static const String fileNotFound = 'لم يتم العثور على الملف';

  // مفاتيح SharedPreferences
  static const String userPhoneKey = 'user_phone';
  static const String userNameKey = 'user_name';
  static const String userProfileImageKey = 'user_profile_image';
  static const String isFirstTimeKey = 'is_first_time';

  // مجموعات Firebase
  static const String usersCollection = 'users';
  static const String messagesCollection = 'messages';
  static const String chatsCollection = 'chats';

  // مجلدات Firebase Storage
  static const String profileImagesFolder = 'profile_images';
  static const String chatImagesFolder = 'chat_images';
  static const String voiceMessagesFolder = 'voice_messages';
  static const String videosFolder = 'videos';

  // أنواع الملفات المدعومة
  static const List<String> supportedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> supportedAudioTypes = ['mp3', 'm4a', 'wav', 'aac'];
  static const List<String> supportedVideoTypes = ['mp4', 'mov', 'avi'];

  // إعدادات Agora (للمكالمات)
  static const String agoraAppId = 'YOUR_AGORA_APP_ID'; // يجب استبداله بالمعرف الحقيقي

  // رسائل النجاح
  static const String messageSentSuccess = 'تم إرسال الرسالة بنجاح';
  static const String imageUploadSuccess = 'تم رفع الصورة بنجاح';
  static const String voiceRecordedSuccess = 'تم تسجيل الرسالة الصوتية بنجاح';

  // نصوص واجهة المستخدم
  static const String appName = 'ArzaTalk';
  static const String loginTitle = 'تسجيل الدخول';
  static const String phoneNumberHint = 'رقم الهاتف';
  static const String nameHint = 'الاسم';
  static const String messageHint = 'اكتب رسالة...';
  static const String searchHint = 'البحث...';

  // حالات المستخدم
  static const String onlineStatus = 'متصل الآن';
  static const String lastSeenPrefix = 'آخر ظهور: ';
  static const String typingStatus = 'يكتب...';

  // أنواع الرسائل
  static const String textMessage = 'رسالة نصية';
  static const String imageMessage = 'صورة';
  static const String voiceMessage = 'رسالة صوتية';
  static const String videoMessage = 'مقطع فيديو';

  // إعدادات التطبيق
  static const bool enableNotifications = true;
  static const bool enableSoundEffects = true;
  static const bool enableVibration = true;

  // تنسيقات التاريخ والوقت
  static const String timeFormat = 'HH:mm';
  static const String dateFormat = 'dd/MM/yyyy';
  static const String fullDateTimeFormat = 'dd/MM/yyyy HH:mm';
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.green,
      primaryColor: AppConstants.primaryColor,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppConstants.primaryColor,
            width: 2,
          ),
        ),
      ),
      useMaterial3: true,
    );
  }
}

class AppValidators {
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال رقم الهاتف';
    }

    final cleanedPhone = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanedPhone.length < 9) {
      return 'رقم الهاتف غير صحيح';
    }

    return null;
  }

  static String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'يرجى إدخال الاسم';
    }

    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون أكثر من حرفين';
    }

    return null;
  }

  static String? validateMessage(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'لا يمكن إرسال رسالة فارغة';
    }

    return null;
  }
}
