import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:local_auth/local_auth.dart';

class ChatLockService extends ChangeNotifier {
  static const String _lockEnabledKey = 'chat_lock_enabled';
  static const String _lockPasswordKey = 'chat_lock_password';
  static const String _lockedChatsKey = 'locked_chats';
  static const String _biometricEnabledKey = 'biometric_enabled';

  bool _isLockEnabled = false;
  String? _lockPassword;
  Set<String> _lockedChats = {};
  bool _isBiometricEnabled = false;
  bool _isAuthenticated = false;
  
  final LocalAuthentication _localAuth = LocalAuthentication();

  bool get isLockEnabled => _isLockEnabled;
  bool get isBiometricEnabled => _isBiometricEnabled;
  bool get isAuthenticated => _isAuthenticated;
  Set<String> get lockedChats => _lockedChats;

  ChatLockService() {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isLockEnabled = prefs.getBool(_lockEnabledKey) ?? false;
      _lockPassword = prefs.getString(_lockPasswordKey);
      _isBiometricEnabled = prefs.getBool(_biometricEnabledKey) ?? false;
      
      final lockedChatsString = prefs.getStringList(_lockedChatsKey) ?? [];
      _lockedChats = lockedChatsString.toSet();
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading chat lock settings: $e');
    }
  }

  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.isDeviceSupported();
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      debugPrint('Error getting available biometrics: $e');
      return [];
    }
  }

  Future<bool> setupChatLock(String password, bool enableBiometric) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_lockEnabledKey, true);
      await prefs.setString(_lockPasswordKey, password);
      await prefs.setBool(_biometricEnabledKey, enableBiometric);
      
      _isLockEnabled = true;
      _lockPassword = password;
      _isBiometricEnabled = enableBiometric;
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error setting up chat lock: $e');
      return false;
    }
  }

  Future<bool> disableChatLock() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_lockEnabledKey, false);
      await prefs.remove(_lockPasswordKey);
      await prefs.setBool(_biometricEnabledKey, false);
      await prefs.remove(_lockedChatsKey);
      
      _isLockEnabled = false;
      _lockPassword = null;
      _isBiometricEnabled = false;
      _lockedChats.clear();
      _isAuthenticated = false;
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error disabling chat lock: $e');
      return false;
    }
  }

  Future<bool> authenticateWithPassword(String password) async {
    if (_lockPassword == password) {
      _isAuthenticated = true;
      notifyListeners();
      return true;
    }
    return false;
  }

  Future<bool> authenticateWithBiometric() async {
    if (!_isBiometricEnabled) return false;
    
    try {
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'Authenticate to access locked chats',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      
      if (isAuthenticated) {
        _isAuthenticated = true;
        notifyListeners();
      }
      
      return isAuthenticated;
    } catch (e) {
      debugPrint('Error authenticating with biometric: $e');
      return false;
    }
  }

  void logout() {
    _isAuthenticated = false;
    notifyListeners();
  }

  Future<bool> lockChat(String chatId) async {
    if (!_isLockEnabled) return false;
    
    try {
      _lockedChats.add(chatId);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_lockedChatsKey, _lockedChats.toList());
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error locking chat: $e');
      return false;
    }
  }

  Future<bool> unlockChat(String chatId) async {
    try {
      _lockedChats.remove(chatId);
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_lockedChatsKey, _lockedChats.toList());
      
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error unlocking chat: $e');
      return false;
    }
  }

  bool isChatLocked(String chatId) {
    return _isLockEnabled && _lockedChats.contains(chatId);
  }

  bool canAccessLockedChats() {
    return !_isLockEnabled || _isAuthenticated;
  }

  Future<bool> changePassword(String oldPassword, String newPassword) async {
    if (_lockPassword != oldPassword) return false;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lockPasswordKey, newPassword);
      
      _lockPassword = newPassword;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error changing password: $e');
      return false;
    }
  }

  Future<bool> toggleBiometric(bool enable) async {
    if (enable && !await isBiometricAvailable()) {
      return false;
    }
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_biometricEnabledKey, enable);
      
      _isBiometricEnabled = enable;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error toggling biometric: $e');
      return false;
    }
  }
}
