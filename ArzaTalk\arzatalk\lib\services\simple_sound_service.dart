import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibration/vibration.dart';

/// خدمة بسيطة لتشغيل الأصوات والاهتزاز
class SimpleSoundService {
  static final SimpleSoundService _instance = SimpleSoundService._internal();
  factory SimpleSoundService() => _instance;
  SimpleSoundService._internal();

  /// تشغيل صوت إشعار الرسالة
  Future<void> playMessageNotification() async {
    try {
      debugPrint('🔊 Playing message notification sound...');
      
      // محاولة تشغيل الصوت النظام
      await SystemSound.play(SystemSoundType.alert);
      debugPrint('✅ System sound played successfully');
      
      // إضافة اهتزاز خفيف
      if (await Vibration.hasVibrator() ?? false) {
        await Vibration.vibrate(duration: 200);
        debugPrint('✅ Vibration played successfully');
      }
      
    } catch (e) {
      debugPrint('❌ Error playing message notification: $e');
      
      // fallback إلى اهتزاز فقط
      try {
        await Vibration.vibrate(duration: 300);
        debugPrint('✅ Fallback vibration successful');
      } catch (vibError) {
        debugPrint('❌ Fallback vibration failed: $vibError');
      }
    }
  }

  /// تشغيل صوت إشعار المكالمة
  Future<void> playCallNotification() async {
    try {
      debugPrint('📞 Playing call notification sound...');
      
      // محاولة تشغيل الصوت النظام
      await SystemSound.play(SystemSoundType.click);
      debugPrint('✅ Call system sound played successfully');
      
      // إضافة اهتزاز أطول للمكالمات
      if (await Vibration.hasVibrator() ?? false) {
        await Vibration.vibrate(duration: 500);
        debugPrint('✅ Call vibration played successfully');
      }
      
    } catch (e) {
      debugPrint('❌ Error playing call notification: $e');
      
      // fallback إلى اهتزاز فقط
      try {
        await Vibration.vibrate(duration: 800);
        debugPrint('✅ Call fallback vibration successful');
      } catch (vibError) {
        debugPrint('❌ Call fallback vibration failed: $vibError');
      }
    }
  }

  /// تشغيل صوت النقر
  Future<void> playClickSound() async {
    try {
      debugPrint('🖱️ Playing click sound...');
      await SystemSound.play(SystemSoundType.click);
      debugPrint('✅ Click sound played successfully');
    } catch (e) {
      debugPrint('❌ Error playing click sound: $e');
    }
  }

  /// تشغيل صوت التنبيه
  Future<void> playAlertSound() async {
    try {
      debugPrint('⚠️ Playing alert sound...');
      await SystemSound.play(SystemSoundType.alert);
      debugPrint('✅ Alert sound played successfully');
    } catch (e) {
      debugPrint('❌ Error playing alert sound: $e');
    }
  }

  /// اهتزاز بسيط
  Future<void> simpleVibrate() async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        await Vibration.vibrate(duration: 100);
        debugPrint('✅ Simple vibration successful');
      }
    } catch (e) {
      debugPrint('❌ Simple vibration failed: $e');
    }
  }

  /// اهتزاز مزدوج
  Future<void> doubleVibrate() async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        await Vibration.vibrate(duration: 100);
        await Future.delayed(const Duration(milliseconds: 100));
        await Vibration.vibrate(duration: 100);
        debugPrint('✅ Double vibration successful');
      }
    } catch (e) {
      debugPrint('❌ Double vibration failed: $e');
    }
  }

  /// اختبار جميع الأصوات
  Future<void> testAllSounds() async {
    debugPrint('🧪 Testing all sounds...');
    
    await playMessageNotification();
    await Future.delayed(const Duration(seconds: 1));
    
    await playCallNotification();
    await Future.delayed(const Duration(seconds: 1));
    
    await playClickSound();
    await Future.delayed(const Duration(seconds: 1));
    
    await playAlertSound();
    await Future.delayed(const Duration(seconds: 1));
    
    await simpleVibrate();
    await Future.delayed(const Duration(seconds: 1));
    
    await doubleVibrate();
    
    debugPrint('✅ All sound tests completed');
  }
}
