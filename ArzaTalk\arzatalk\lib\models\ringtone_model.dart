class RingtoneModel {
  final String id;
  final String name;
  final String fileName;
  final RingtoneType type;
  final int duration; // in seconds

  const RingtoneModel({
    required this.id,
    required this.name,
    required this.fileName,
    required this.type,
    required this.duration,
  });

  factory RingtoneModel.fromJson(Map<String, dynamic> json) {
    return RingtoneModel(
      id: json['id'],
      name: json['name'],
      fileName: json['fileName'],
      type: RingtoneType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      duration: json['duration'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'fileName': fileName,
      'type': type.toString().split('.').last,
      'duration': duration,
    };
  }

  RingtoneModel copyWith({
    String? id,
    String? name,
    String? fileName,
    RingtoneType? type,
    int? duration,
  }) {
    return RingtoneModel(
      id: id ?? this.id,
      name: name ?? this.name,
      fileName: fileName ?? this.fileName,
      type: type ?? this.type,
      duration: duration ?? this.duration,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RingtoneModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'RingtoneModel(id: $id, name: $name, type: $type)';
  }
}

enum RingtoneType {
  message,
  call,
}

class RingtoneData {
  static const List<RingtoneModel> messageRingtones = [
    RingtoneModel(
      id: 'msg_1',
      name: 'Default Message',
      fileName: 'message_default.mp3',
      type: RingtoneType.message,
      duration: 2,
    ),
    RingtoneModel(
      id: 'msg_2',
      name: 'Gentle Chime',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 1,
    ),
    RingtoneModel(
      id: 'msg_3',
      name: 'Pop Sound',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 1,
    ),
    RingtoneModel(
      id: 'msg_4',
      name: 'Bell Notification',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 2,
    ),
    RingtoneModel(
      id: 'msg_5',
      name: 'Soft Ding',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 1,
    ),
    RingtoneModel(
      id: 'msg_6',
      name: 'Water Drop',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 1,
    ),
    RingtoneModel(
      id: 'msg_7',
      name: 'Quick Beep',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 1,
    ),
    RingtoneModel(
      id: 'msg_8',
      name: 'Whistle',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 2,
    ),
    RingtoneModel(
      id: 'msg_9',
      name: 'Coin Sound',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 1,
    ),
    RingtoneModel(
      id: 'msg_10',
      name: 'Glass Tap',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.message,
      duration: 1,
    ),
  ];

  static const List<RingtoneModel> callRingtones = [
    RingtoneModel(
      id: 'call_1',
      name: 'Default Ring',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 30,
    ),
    RingtoneModel(
      id: 'call_2',
      name: 'Classic Phone',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 25,
    ),
    RingtoneModel(
      id: 'call_3',
      name: 'Modern Ring',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 20,
    ),
    RingtoneModel(
      id: 'call_4',
      name: 'Gentle Melody',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 35,
    ),
    RingtoneModel(
      id: 'call_5',
      name: 'Electronic Beat',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 28,
    ),
    RingtoneModel(
      id: 'call_6',
      name: 'Piano Notes',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 32,
    ),
    RingtoneModel(
      id: 'call_7',
      name: 'Vibrant Tone',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 24,
    ),
    RingtoneModel(
      id: 'call_8',
      name: 'Soft Chimes',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 30,
    ),
    RingtoneModel(
      id: 'call_9',
      name: 'Digital Ring',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 22,
    ),
    RingtoneModel(
      id: 'call_10',
      name: 'Nature Sound',
      fileName: 'message_default.mp3', // Use same file
      type: RingtoneType.call,
      duration: 40,
    ),
  ];

  static List<RingtoneModel> getAllRingtones() {
    return [...messageRingtones, ...callRingtones];
  }

  static List<RingtoneModel> getRingtonesByType(RingtoneType type) {
    switch (type) {
      case RingtoneType.message:
        return messageRingtones;
      case RingtoneType.call:
        return callRingtones;
    }
  }

  static RingtoneModel? getRingtoneById(String id) {
    try {
      return getAllRingtones().firstWhere((ringtone) => ringtone.id == id);
    } catch (e) {
      return null;
    }
  }
}
