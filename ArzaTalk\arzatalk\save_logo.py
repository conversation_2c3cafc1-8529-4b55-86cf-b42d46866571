#!/usr/bin/env python3
import base64
import requests

# تحميل الشعار من Google Drive
url = "https://drive.google.com/uc?export=download&id=14ilYMf36Y2lEAhXQoPROhJ3BEStrqGly"

try:
    response = requests.get(url)
    if response.status_code == 200:
        with open('new_logo.png', 'wb') as f:
            f.write(response.content)
        print("✅ تم تحميل الشعار بنجاح!")
    else:
        print(f"❌ خطأ في التحميل: {response.status_code}")
except Exception as e:
    print(f"❌ خطأ: {e}")
