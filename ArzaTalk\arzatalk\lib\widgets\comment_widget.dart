import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../services/auth_service.dart';
import '../services/social_feed_service.dart';
import 'facebook_reactions.dart';
import 'comment_input_widget.dart';

/// ويدجت التعليق مع التفاعلات (مثل Facebook)
class CommentWidget extends StatefulWidget {
  final CommentModel comment;
  final String? postId; // معرف المنشور للردود
  final Function(String emoji)? onReaction;
  final Function(String commentId)? onReply;
  final Function(String commentId)? onEdit;
  final Function(String commentId)? onDelete;
  final Function(String userId)? onBlock;
  final Function(String commentId)? onReport;
  final bool isReply;
  final int depth;
  final bool showAllReplies;

  const CommentWidget({
    super.key,
    required this.comment,
    this.postId,
    this.onReaction,
    this.onReply,
    this.onEdit,
    this.onDelete,
    this.onBlock,
    this.onReport,
    this.isReply = false,
    this.depth = 0,
    this.showAllReplies = false,
  });

  @override
  State<CommentWidget> createState() => _CommentWidgetState();
}

class _CommentWidgetState extends State<CommentWidget> {
  bool _showReactions = false;
  bool _showAllReplies = false;
  bool _showReplyInput = false; // إظهار إدخال الرد
  // تم إزالة _maxVisibleReplies لأننا نعرض رد واحد فقط افتراضي<|im_start|>

  @override
  Widget build(BuildContext context) {
    final isReply = widget.isReply;
    final avatarRadius = isReply ? 12.0 : 16.0; // الردود أصغر
    final fontSize = isReply ? 12.0 : 14.0; // نص الردود أصغر
    final nameSize = isReply ? 11.0 : 13.0; // اسم الردود أصغر

    return Container(
      margin: EdgeInsets.symmetric(
        vertical: isReply ? 2 : 4,
        horizontal: isReply ? 8 : 16
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // خط الربط للردود
          if (isReply) ...[
            SizedBox(
              width: 20,
              height: avatarRadius * 2 + 16, // ارتفاع يغطي الملف الشخصي
              child: CustomPaint(
                painter: ReplyLinePainter(avatarRadius: avatarRadius),
              ),
            ),
            const SizedBox(width: 4),
          ],

          // صورة المستخدم
          CircleAvatar(
            radius: avatarRadius,
            backgroundColor: const Color(0xFF1877F2),
            backgroundImage: widget.comment.authorAvatar.isNotEmpty
                ? NetworkImage(widget.comment.authorAvatar)
                : null,
            child: widget.comment.authorAvatar.isEmpty
                ? Text(
                    widget.comment.authorName.isNotEmpty
                        ? widget.comment.authorName[0].toUpperCase()
                        : '?',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isReply ? 10 : 12,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),

          const SizedBox(width: 8),

          // محتوى التعليق
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // فقاعة التعليق
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF0F2F5),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم المستخدم
                      Text(
                        widget.comment.authorName,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: nameSize,
                          color: const Color(0xFF1C1E21),
                        ),
                      ),
                      const SizedBox(height: 2),

                      // نص التعليق
                      if (widget.comment.content.isNotEmpty) ...[
                        Text(
                          widget.comment.content,
                          style: TextStyle(
                            fontSize: fontSize,
                            color: const Color(0xFF1C1E21),
                          ),
                        ),
                        if (widget.comment.hasMedia) const SizedBox(height: 8),
                      ],

                      // الوسائط (صور وفيديوهات)
                      if (widget.comment.hasMedia) ...[
                        _buildCommentMedia(),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 4),

                // أزرار التفاعل والوقت
                Row(
                  children: [
                    // الوقت
                    Text(
                      widget.comment.timeAgo,
                      style: const TextStyle(
                        color: Color(0xFF65676B),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // زر الإعجاب
                    GestureDetector(
                      onTap: () => _handleLike(),
                      onLongPress: () => _showReactionPicker(),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _hasUserReacted() ? Icons.thumb_up : Icons.thumb_up_outlined,
                            size: 14,
                            color: _hasUserReacted() ? const Color(0xFF1877F2) : const Color(0xFF65676B),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Like',
                            style: TextStyle(
                              color: _hasUserReacted() ? const Color(0xFF1877F2) : const Color(0xFF65676B),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 16),

                    // زر الرد
                    GestureDetector(
                      onTap: () => _toggleReplyInput(),
                      child: const Text(
                        'Reply',
                        style: TextStyle(
                          color: Color(0xFF65676B),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),

                    // عدد التفاعلات
                    if (widget.comment.reactions.isNotEmpty) ...[
                      const SizedBox(width: 16),
                      _buildReactionsSummary(),
                    ],

                    const Spacer(),

                    // قائمة الثلاث نقاط
                    PopupMenuButton<String>(
                      icon: const Icon(
                        Icons.more_horiz,
                        color: Color(0xFF65676B),
                        size: 16,
                      ),
                      onSelected: (value) => _handleMenuAction(value),
                      itemBuilder: (context) => _buildMenuItems(),
                    ),
                  ],
                ),

                // عرض منتقي التفاعلات للتعليقات (4 تفاعلات فقط)
                if (_showReactions)
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    child: _buildCommentReactionBar(),
                  ),

                // إدخال الرد مع الوسائط
                if (_showReplyInput && !widget.isReply) ...[
                  const SizedBox(height: 8),
                  Container(
                    margin: const EdgeInsets.only(left: 40),
                    child: CommentInputWidget(
                      postId: widget.postId ?? widget.comment.postId,
                      parentCommentId: widget.comment.id,
                      hintText: 'Write a reply...',
                      onCommentAdded: () {
                        setState(() {
                          _showReplyInput = false;
                        });
                      },
                    ),
                  ),
                ],

                // الردود على التعليق (متسلسلة عمودياً مثل Facebook)
                if (widget.comment.replies.isNotEmpty && !widget.isReply) ...[
                  const SizedBox(height: 8),
                  _buildRepliesSection(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// التحقق من تفاعل المستخدم الحالي
  bool _hasUserReacted() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser == null) return false;

    return widget.comment.reactions.containsKey(currentUser.phoneNumber);
  }

  /// التعامل مع الإعجاب
  void _handleLike() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      final currentReaction = widget.comment.reactions[currentUser.phoneNumber];
      if (currentReaction == '👍') {
        // إزالة التفاعل
        _handleReaction('');
      } else {
        // إضافة إعجاب
        _handleReaction('👍');
      }
    }
  }

  /// التعامل مع التفاعل
  void _handleReaction(String emoji) {
    final authService = Provider.of<AuthService>(context, listen: false);
    final socialService = Provider.of<SocialFeedService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser != null) {
      // استخدام الطريقة الصحيحة للتفاعل مع التعليقات
      socialService.addCommentReaction(
        commentId: widget.comment.id,
        emoji: emoji,
        userId: currentUser.phoneNumber,
      );

      if (widget.onReaction != null) {
        widget.onReaction!(emoji);
      }
    }
  }

  /// إظهار منتقي التفاعلات
  void _showReactionPicker() {
    setState(() => _showReactions = true);
  }

  /// تبديل إظهار إدخال الرد
  void _toggleReplyInput() {
    setState(() {
      _showReplyInput = !_showReplyInput;
    });
  }

  /// بناء ملخص التفاعلات مع الملصقات الاحترافية
  Widget _buildReactionsSummary() {
    final reactions = widget.comment.reactions;
    if (reactions.isEmpty) return const SizedBox.shrink();

    // تجميع التفاعلات حسب النوع
    final reactionCounts = <String, int>{};
    for (final emoji in reactions.values) {
      reactionCounts[emoji] = (reactionCounts[emoji] ?? 0) + 1;
    }

    // أخذ أكثر 3 تفاعلات
    final topReactions = reactionCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final displayReactions = topReactions.take(3).toList();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // عرض التفاعلات كملصقات احترافية
          ...displayReactions.map((entry) => Padding(
            padding: const EdgeInsets.only(right: 2),
            child: _buildProfessionalReactionSticker(entry.key),
          )),

          const SizedBox(width: 4),

          // العدد الإجمالي
          Text(
            '${reactions.length}',
            style: const TextStyle(
              fontSize: 11,
              color: Color(0xFF65676B),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ملصق تفاعل احترافي
  Widget _buildProfessionalReactionSticker(String emoji) {
    Color stickerColor;
    IconData stickerIcon;

    switch (emoji) {
      case '👍':
        stickerColor = const Color(0xFF1877F2);
        stickerIcon = Icons.thumb_up;
        break;
      case '❤️':
        stickerColor = const Color(0xFFE91E63);
        stickerIcon = Icons.favorite;
        break;
      case '😂':
        stickerColor = const Color(0xFFFFC107);
        stickerIcon = Icons.sentiment_very_satisfied;
        break;
      case '😮':
        stickerColor = const Color(0xFFFF9800);
        stickerIcon = Icons.sentiment_very_dissatisfied;
        break;
      case '😢':
        stickerColor = const Color(0xFF2196F3);
        stickerIcon = Icons.sentiment_very_dissatisfied;
        break;
      case '😡':
        stickerColor = const Color(0xFFF44336);
        stickerIcon = Icons.sentiment_very_dissatisfied;
        break;
      default:
        stickerColor = const Color(0xFF1877F2);
        stickerIcon = Icons.thumb_up;
    }

    return Container(
      width: 16,
      height: 16,
      decoration: BoxDecoration(
        color: stickerColor,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 1),
      ),
      child: Icon(
        stickerIcon,
        size: 10,
        color: Colors.white,
      ),
    );
  }

  /// بناء عناصر القائمة حسب المستخدم (مثل Facebook)
  List<PopupMenuEntry<String>> _buildMenuItems() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final currentUser = authService.currentUser;
    final isOwner = currentUser?.phoneNumber == widget.comment.authorId;

    if (isOwner) {
      // خيارات صاحب التعليق فقط - تعديل وحذف
      return [
        const PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16, color: Color(0xFF1877F2)),
              SizedBox(width: 8),
              Text('Edit Comment', style: TextStyle(fontSize: 14)),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: Colors.red),
              SizedBox(width: 8),
              Text('Delete Comment', style: TextStyle(fontSize: 14)),
            ],
          ),
        ),
      ];
    } else {
      // خيارات للمستخدمين الآخرين فقط - حظر وإبلاغ
      return [
        const PopupMenuItem<String>(
          value: 'block',
          child: Row(
            children: [
              Icon(Icons.block, size: 16, color: Colors.orange),
              SizedBox(width: 8),
              Text('Block User', style: TextStyle(fontSize: 14)),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'report',
          child: Row(
            children: [
              Icon(Icons.report, size: 16, color: Colors.red),
              SizedBox(width: 8),
              Text('Report Comment', style: TextStyle(fontSize: 14)),
            ],
          ),
        ),
      ];
    }
  }

  /// التعامل مع إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        _showEditDialog();
        break;
      case 'delete':
        _showDeleteConfirmation();
        break;
      case 'block':
        _showBlockConfirmation();
        break;
      case 'report':
        _showReportDialog();
        break;
    }
  }

  /// إظهار تأكيد الحذف
  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Comment'),
        content: const Text('Are you sure you want to delete this comment?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onDelete?.call(widget.comment.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// إظهار حوار التعديل
  void _showEditDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Comment'),
        content: TextField(
          controller: TextEditingController(text: widget.comment.content),
          decoration: const InputDecoration(
            hintText: 'Edit your comment...',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Comment edited successfully'),
                  backgroundColor: Color(0xFF1877F2),
                ),
              );
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  /// إظهار تأكيد الحظر
  void _showBlockConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: Text('Are you sure you want to block ${widget.comment.authorName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onBlock?.call(widget.comment.authorId);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${widget.comment.authorName} has been blocked'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Block'),
          ),
        ],
      ),
    );
  }

  /// إظهار حوار الإبلاغ
  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Comment'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Why are you reporting this comment?'),
            SizedBox(height: 16),
            // يمكن إضافة خيارات الإبلاغ هنا
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onReport?.call(widget.comment.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Comment reported successfully'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Report'),
          ),
        ],
      ),
    );
  }

  /// بناء شريط التفاعلات الاحترافية للتعليقات (4 تفاعلات فقط)
  Widget _buildCommentReactionBar() {
    final commentReactions = [
      {'id': 'like', 'emoji': '👍', 'color': const Color(0xFF1877F2), 'icon': Icons.thumb_up},
      {'id': 'love', 'emoji': '❤️', 'color': const Color(0xFFE91E63), 'icon': Icons.favorite},
      {'id': 'haha', 'emoji': '😂', 'color': const Color(0xFFFFC107), 'icon': Icons.sentiment_very_satisfied},
      {'id': 'angry', 'emoji': '😡', 'color': const Color(0xFFF44336), 'icon': Icons.sentiment_very_dissatisfied},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: const Color(0xFFE4E6EA), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: commentReactions.map((reaction) {
          return GestureDetector(
            onTap: () {
              _handleReaction(reaction['emoji'] as String);
              setState(() => _showReactions = false);
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 6),
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: reaction['color'] as Color,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: (reaction['color'] as Color).withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                reaction['icon'] as IconData,
                color: Colors.white,
                size: 18,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// بناء عرض الوسائط في التعليق
  Widget _buildCommentMedia() {
    final hasImages = widget.comment.images.isNotEmpty;
    final hasVideos = widget.comment.videos.isNotEmpty;

    if (!hasImages && !hasVideos) return const SizedBox.shrink();

    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الصور
          if (hasImages) ...[
            _buildImagesGrid(),
            if (hasVideos) const SizedBox(height: 8),
          ],

          // الفيديوهات
          if (hasVideos) ...[
            _buildVideosGrid(),
          ],
        ],
      ),
    );
  }

  /// بناء شبكة الصور
  Widget _buildImagesGrid() {
    final images = widget.comment.images;
    if (images.isEmpty) return const SizedBox.shrink();

    return SizedBox(
      height: images.length == 1 ? 120 : 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 4),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                images[index],
                width: images.length == 1 ? 160 : 80,
                height: images.length == 1 ? 120 : 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: images.length == 1 ? 160 : 80,
                    height: images.length == 1 ? 120 : 80,
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.broken_image,
                      color: Colors.grey,
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  /// بناء شبكة الفيديوهات
  Widget _buildVideosGrid() {
    final videos = widget.comment.videos;
    if (videos.isEmpty) return const SizedBox.shrink();

    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: videos.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 4),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 80,
                    height: 80,
                    color: Colors.black12,
                    child: const Icon(
                      Icons.play_circle_fill,
                      color: Color(0xFF1877F2),
                      size: 32,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.videocam,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء قسم الردود المتسلسلة مثل Facebook مع دعم الردود المتعددة
  Widget _buildRepliesSection() {
    final replies = widget.comment.replies;
    if (replies.isEmpty) return const SizedBox.shrink();

    // تحديد عدد الردود المرئية (رد واحد فقط افتراضي<|im_start|>)
    final visibleReplies = _showAllReplies || widget.showAllReplies
        ? replies
        : replies.take(1).toList(); // عرض رد واحد فقط

    final hasMoreReplies = replies.length > 1 && !_showAllReplies && !widget.showAllReplies;

    return Container(
      margin: const EdgeInsets.only(left: 40, top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الردود المرئية
          ...visibleReplies.asMap().entries.map((entry) {
            final index = entry.key;
            final reply = entry.value;
            final isLastVisible = index == visibleReplies.length - 1;

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // خط الربط العمودي المحسن (مثل Facebook)
                  SizedBox(
                    width: 24,
                    child: CustomPaint(
                      painter: FacebookReplyConnectionPainter(
                        isFirst: index == 0,
                        isLast: isLastVisible && !hasMoreReplies,
                        hasMore: hasMoreReplies && isLastVisible,
                        replyIndex: index,
                        totalReplies: visibleReplies.length,
                      ),
                      child: const SizedBox(height: 80), // ارتفاع أكبر
                    ),
                  ),

                  // محتوى الرد
                  Expanded(
                    child: CommentWidget(
                      comment: reply,
                      postId: widget.postId ?? widget.comment.postId,
                      isReply: true,
                      depth: widget.depth + 1,
                      onReaction: widget.onReaction,
                      onReply: widget.onReply,
                      onEdit: widget.onEdit,
                      onDelete: widget.onDelete,
                      onBlock: widget.onBlock,
                      onReport: widget.onReport,
                      showAllReplies: _showAllReplies || widget.showAllReplies,
                    ),
                  ),
                ],
              ),
            );
          }),

          // أزرار عرض/إخفاء المزيد من الردود
          if (hasMoreReplies || (_showAllReplies && replies.length > 1)) ...[
            Container(
              margin: const EdgeInsets.only(left: 24, top: 4),
              child: Row(
                children: [
                  // خط للزر
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CustomPaint(
                      painter: FacebookReplyConnectionPainter(
                        isFirst: false,
                        isLast: true,
                        hasMore: false,
                        isShowMoreButton: true,
                        replyIndex: 0,
                        totalReplies: 1,
                      ),
                    ),
                  ),

                  // زر عرض المزيد أو إخفاء
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _showAllReplies = !_showAllReplies;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF0F2F5),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: const Color(0xFFE4E6EA)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _showAllReplies ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                            size: 16,
                            color: const Color(0xFF1877F2),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _showAllReplies
                                ? 'Hide replies'
                                : 'View ${replies.length - 1} more replies',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1877F2),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// رسام خط الربط للردود (مثل Facebook) - يصل للملف الشخصي
class ReplyLinePainter extends CustomPainter {
  final double avatarRadius;

  const ReplyLinePainter({required this.avatarRadius});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFE4E6EA)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final path = Path();

    // حساب موضع منتصف الملف الشخصي
    final avatarCenter = avatarRadius + 8; // 8 للمساحة العلوية

    // خط عمودي من الأعلى يصل لمنتصف الملف الشخصي
    path.moveTo(10, 0);
    path.lineTo(10, avatarCenter);

    // خط أفقي للرد يصل للملف الشخصي
    path.lineTo(size.width, avatarCenter);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// رسام خطوط الربط للردود المتسلسلة (مثل Facebook)
class ReplyConnectionPainter extends CustomPainter {
  final bool isFirst;
  final bool isLast;
  final bool hasMore;
  final bool isShowMoreButton;

  const ReplyConnectionPainter({
    required this.isFirst,
    required this.isLast,
    required this.hasMore,
    this.isShowMoreButton = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFE4E6EA)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final path = Path();

    if (isShowMoreButton) {
      // خط للزر "عرض المزيد"
      path.moveTo(10, 0);
      path.lineTo(10, size.height * 0.5);
      path.lineTo(size.width, size.height * 0.5);
    } else {
      // خط عمودي رئيسي
      if (!isFirst) {
        path.moveTo(10, 0);
      }

      if (!isLast || hasMore) {
        path.lineTo(10, size.height);
      } else {
        path.lineTo(10, size.height * 0.3);
      }

      // خط أفقي للرد
      path.moveTo(10, size.height * 0.3);
      path.lineTo(size.width, size.height * 0.3);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// رسام خطوط الربط المحسن مثل Facebook
class FacebookReplyConnectionPainter extends CustomPainter {
  final bool isFirst;
  final bool isLast;
  final bool hasMore;
  final bool isShowMoreButton;
  final int replyIndex;
  final int totalReplies;

  const FacebookReplyConnectionPainter({
    required this.isFirst,
    required this.isLast,
    required this.hasMore,
    this.isShowMoreButton = false,
    required this.replyIndex,
    required this.totalReplies,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFE4E6EA)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final path = Path();

    if (isShowMoreButton) {
      // خط للزر "عرض المزيد" - يصل من الخط العمودي
      path.moveTo(12, 0);
      path.lineTo(12, size.height * 0.5);
      path.lineTo(size.width, size.height * 0.5);
    } else {
      // خط عمودي رئيسي (يربط التعليق الأصلي بالردود)
      if (!isFirst) {
        // استمرار الخط من الأعلى
        path.moveTo(12, 0);
      } else {
        // بداية الخط من منتصف التعليق الأصلي
        path.moveTo(12, 20);
      }

      if (!isLast || hasMore) {
        // استمرار الخط للأسفل
        path.lineTo(12, size.height);
      } else {
        // انتهاء الخط عند منتصف الرد الأخير
        path.lineTo(12, size.height * 0.4);
      }

      // خط أفقي يصل للملف الشخصي (يلمس الصورة مباشرة)
      path.moveTo(12, size.height * 0.4);
      path.lineTo(size.width - 4, size.height * 0.4); // يصل تقريب<|im_start|> للصورة
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}