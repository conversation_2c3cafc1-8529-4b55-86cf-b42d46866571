import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:typed_data';

/// خدمة تشخيص مشاكل Firebase
class FirebaseDebugService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// اختبار شامل لمعرفة سبب عدم حفظ البيانات
  static Future<Map<String, dynamic>> diagnoseFirebaseIssues() async {
    final results = <String, dynamic>{};

    debugPrint('🔍 بدء تشخيص مشاكل Firebase...');

    try {
      // 1. اختبار الاتصال
      results['connection'] = await _testConnection();

      // 2. اختبار قواعد Firestore
      results['firestore_rules'] = await _testFirestoreRules();

      // 3. اختبار قواعد Storage
      results['storage_rules'] = await _testStorageRules();

      // 4. اختبار حفظ منشور حقيقي
      results['post_save'] = await _testPostSave();

      // 5. اختبار قراءة المنشورات
      results['post_read'] = await _testPostRead();

      // 6. اختبار حفظ تفاعل
      results['reaction_save'] = await _testReactionSave();

      // 7. اختبار رفع صورة
      results['image_upload'] = await _testImageUpload();

      debugPrint('✅ انتهى التشخيص');
      return results;

    } catch (e) {
      debugPrint('❌ خطأ في التشخيص: $e');
      results['error'] = e.toString();
      return results;
    }
  }

  /// اختبار الاتصال
  static Future<Map<String, dynamic>> _testConnection() async {
    try {
      debugPrint('🔗 اختبار الاتصال...');

      await _firestore.enableNetwork();

      return {
        'success': true,
        'message': 'الاتصال يعمل',
        'project_id': _firestore.app.options.projectId,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'فشل الاتصال',
        'error': e.toString(),
      };
    }
  }

  /// اختبار قواعد Firestore
  static Future<Map<String, dynamic>> _testFirestoreRules() async {
    try {
      debugPrint('📋 اختبار قواعد Firestore...');

      // محاولة كتابة مستند اختبار
      final testDoc = _firestore.collection('test').doc('rules_test');
      await testDoc.set({
        'test': true,
        'timestamp': Timestamp.now(),
      });

      // محاولة قراءة المستند
      final doc = await testDoc.get();

      // حذف المستند
      await testDoc.delete();

      return {
        'success': true,
        'message': 'قواعد Firestore تسمح بالقراءة والكتابة',
        'can_write': true,
        'can_read': doc.exists,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'قواعد Firestore تمنع العمليات',
        'error': e.toString(),
      };
    }
  }

  /// اختبار قواعد Storage
  static Future<Map<String, dynamic>> _testStorageRules() async {
    try {
      debugPrint('📁 اختبار قواعد Storage...');

      // محاولة رفع ملف اختبار
      final testData = 'test file content'.codeUnits;
      final ref = _storage.ref().child('test/rules_test.txt');

      await ref.putData(Uint8List.fromList(testData));
      final downloadUrl = await ref.getDownloadURL();

      // حذف الملف
      await ref.delete();

      return {
        'success': true,
        'message': 'قواعد Storage تسمح بالرفع',
        'can_upload': true,
        'download_url': downloadUrl,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'قواعد Storage تمنع الرفع',
        'error': e.toString(),
      };
    }
  }

  /// اختبار حفظ منشور
  static Future<Map<String, dynamic>> _testPostSave() async {
    try {
      debugPrint('📝 اختبار حفظ منشور...');

      final postId = 'test_post_${DateTime.now().millisecondsSinceEpoch}';
      final postData = {
        'id': postId,
        'authorId': '+212638813823',
        'authorName': 'مستخدم اختبار',
        'content': 'منشور اختبار Firebase',
        'images': <String>[],
        'videos': <String>[],
        'reactions': <String, int>{},
        'commentsCount': 0,
        'sharesCount': 0,
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
        'type': 'text',
        'privacy': 'public',
        'isDeleted': false,
        'isEdited': false,
        'tags': <String>[],
        'location': null,
        'feeling': null,
        'musicUrl': null,
        'musicTitle': null,
        'backgroundColor': null,
        'taggedUsers': <String>[],
        'isLiveStream': false,
        'originalPostId': null,
        'isRepost': false,
        'linkPreviews': <Map<String, dynamic>>[],
      };

      await _firestore.collection('posts').doc(postId).set(postData);

      // التحقق من الحفظ
      final savedDoc = await _firestore.collection('posts').doc(postId).get();

      // حذف المنشور التجريبي
      await _firestore.collection('posts').doc(postId).delete();

      return {
        'success': savedDoc.exists,
        'message': savedDoc.exists ? 'تم حفظ المنشور بنجاح' : 'فشل حفظ المنشور',
        'post_id': postId,
        'data_saved': savedDoc.exists ? savedDoc.data() : null,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في حفظ المنشور',
        'error': e.toString(),
      };
    }
  }

  /// اختبار قراءة المنشورات
  static Future<Map<String, dynamic>> _testPostRead() async {
    try {
      debugPrint('📖 اختبار قراءة المنشورات...');

      final snapshot = await _firestore.collection('posts')
          .orderBy('createdAt', descending: true)
          .limit(5)
          .get();

      return {
        'success': true,
        'message': 'تم قراءة المنشورات بنجاح',
        'posts_count': snapshot.docs.length,
        'posts': snapshot.docs.map((doc) => {
          'id': doc.id,
          'data': doc.data(),
        }).toList(),
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في قراءة المنشورات',
        'error': e.toString(),
      };
    }
  }

  /// اختبار حفظ تفاعل
  static Future<Map<String, dynamic>> _testReactionSave() async {
    try {
      debugPrint('👍 اختبار حفظ تفاعل...');

      // إنشاء منشور اختبار
      final postId = 'test_reaction_post_${DateTime.now().millisecondsSinceEpoch}';
      await _firestore.collection('posts').doc(postId).set({
        'id': postId,
        'content': 'منشور اختبار التفاعلات',
        'reactions': <String, int>{},
        'createdAt': Timestamp.now(),
      });

      // إضافة تفاعل
      final reactions = {'👍': 1, '❤️': 1};
      await _firestore.collection('posts').doc(postId).update({
        'reactions': reactions,
        'updatedAt': Timestamp.now(),
      });

      // التحقق من الحفظ
      final updatedDoc = await _firestore.collection('posts').doc(postId).get();
      final savedReactions = updatedDoc.data()?['reactions'];

      // حذف المنشور التجريبي
      await _firestore.collection('posts').doc(postId).delete();

      return {
        'success': savedReactions != null && savedReactions['👍'] == 1,
        'message': 'تم حفظ التفاعل بنجاح',
        'reactions_saved': savedReactions,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في حفظ التفاعل',
        'error': e.toString(),
      };
    }
  }

  /// اختبار رفع صورة
  static Future<Map<String, dynamic>> _testImageUpload() async {
    try {
      debugPrint('🖼️ اختبار رفع صورة...');

      // إنشاء صورة اختبار صغيرة (1x1 pixel)
      final testImageData = [
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG header
        0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 pixel
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53,
        0xDE, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41,
        0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
        0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42,
        0x60, 0x82
      ];

      final fileName = 'test_images/test_${DateTime.now().millisecondsSinceEpoch}.png';
      final ref = _storage.ref().child('chat_media/$fileName');

      await ref.putData(Uint8List.fromList(testImageData));
      final downloadUrl = await ref.getDownloadURL();

      // حذف الصورة التجريبية
      await ref.delete();

      return {
        'success': true,
        'message': 'تم رفع الصورة بنجاح',
        'download_url': downloadUrl,
        'file_name': fileName,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في رفع الصورة',
        'error': e.toString(),
      };
    }
  }

  /// طباعة تقرير التشخيص
  static void printDiagnosisReport(Map<String, dynamic> results) {
    debugPrint('\n🔍 ===== تقرير تشخيص Firebase =====');

    results.forEach((test, result) {
      final success = result['success'] ?? false;
      final message = result['message'] ?? 'لا توجد رسالة';
      final status = success ? '✅' : '❌';

      debugPrint('$status $test: $message');

      if (!success && result['error'] != null) {
        debugPrint('   خطأ: ${result['error']}');
      }
    });

    debugPrint('=====================================\n');
  }
}
