import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

/// نوع قفل التطبيق
enum LockType {
  none,
  pin,
  password,
  fingerprint,
  face,
}

/// حالة القفل
enum LockState {
  locked,
  unlocked,
  authenticating,
}

/// خدمة قفل التطبيق
class AppLockService extends ChangeNotifier {
  static final AppLockService _instance = AppLockService._internal();
  factory AppLockService() => _instance;
  AppLockService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  
  // حالة القفل
  LockState _lockState = LockState.unlocked;
  LockType _lockType = LockType.none;
  
  // إعدادات القفل
  String? _pin;
  String? _password;
  bool _autoLockEnabled = false;
  int _autoLockDuration = 5; // بالدقائق
  bool _lockOnAppStart = false;
  bool _lockOnBackground = false;
  
  // مؤقت القفل التلقائي
  Timer? _autoLockTimer;
  DateTime? _lastActiveTime;
  
  // محاولات فشل الدخول
  int _failedAttempts = 0;
  DateTime? _lockoutUntil;
  static const int maxFailedAttempts = 5;
  static const int lockoutDurationMinutes = 30;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadSettings();
    _updateLockState();
    _startAutoLockTimer();
  }

  /// تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final lockTypeIndex = prefs.getInt('lock_type') ?? 0;
      _lockType = LockType.values[lockTypeIndex];
      
      _pin = prefs.getString('lock_pin');
      _password = prefs.getString('lock_password');
      _autoLockEnabled = prefs.getBool('auto_lock_enabled') ?? false;
      _autoLockDuration = prefs.getInt('auto_lock_duration') ?? 5;
      _lockOnAppStart = prefs.getBool('lock_on_app_start') ?? false;
      _lockOnBackground = prefs.getBool('lock_on_background') ?? false;
      _failedAttempts = prefs.getInt('failed_attempts') ?? 0;
      
      final lockoutTime = prefs.getInt('lockout_until');
      if (lockoutTime != null) {
        _lockoutUntil = DateTime.fromMillisecondsSinceEpoch(lockoutTime);
      }
      
      debugPrint('🔐 App lock settings loaded');
    } catch (e) {
      debugPrint('❌ Error loading lock settings: $e');
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setInt('lock_type', _lockType.index);
      await prefs.setString('lock_pin', _pin ?? '');
      await prefs.setString('lock_password', _password ?? '');
      await prefs.setBool('auto_lock_enabled', _autoLockEnabled);
      await prefs.setInt('auto_lock_duration', _autoLockDuration);
      await prefs.setBool('lock_on_app_start', _lockOnAppStart);
      await prefs.setBool('lock_on_background', _lockOnBackground);
      await prefs.setInt('failed_attempts', _failedAttempts);
      
      if (_lockoutUntil != null) {
        await prefs.setInt('lockout_until', _lockoutUntil!.millisecondsSinceEpoch);
      } else {
        await prefs.remove('lockout_until');
      }
      
      debugPrint('🔐 App lock settings saved');
    } catch (e) {
      debugPrint('❌ Error saving lock settings: $e');
    }
  }

  /// تعيين PIN
  Future<bool> setPIN(String pin) async {
    if (pin.length < 4 || pin.length > 8) return false;
    
    _pin = pin;
    _lockType = LockType.pin;
    await _saveSettings();
    _updateLockState();
    
    debugPrint('🔐 PIN set successfully');
    return true;
  }

  /// تعيين كلمة مرور
  Future<bool> setPassword(String password) async {
    if (password.length < 6) return false;
    
    _password = password;
    _lockType = LockType.password;
    await _saveSettings();
    _updateLockState();
    
    debugPrint('🔐 Password set successfully');
    return true;
  }

  /// تفعيل البصمة
  Future<bool> enableFingerprint() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) return false;
      
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      if (!availableBiometrics.contains(BiometricType.fingerprint)) return false;
      
      _lockType = LockType.fingerprint;
      await _saveSettings();
      _updateLockState();
      
      debugPrint('🔐 Fingerprint enabled successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error enabling fingerprint: $e');
      return false;
    }
  }

  /// تفعيل التعرف على الوجه
  Future<bool> enableFaceID() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) return false;
      
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      if (!availableBiometrics.contains(BiometricType.face)) return false;
      
      _lockType = LockType.face;
      await _saveSettings();
      _updateLockState();
      
      debugPrint('🔐 Face ID enabled successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error enabling Face ID: $e');
      return false;
    }
  }

  /// إلغاء القفل
  Future<void> disableLock() async {
    _lockType = LockType.none;
    _pin = null;
    _password = null;
    _lockState = LockState.unlocked;
    await _saveSettings();
    _stopAutoLockTimer();
    
    debugPrint('🔐 App lock disabled');
    notifyListeners();
  }

  /// محاولة فتح القفل
  Future<bool> unlock(String input) async {
    if (_isLockedOut()) {
      debugPrint('🔐 App is locked out');
      return false;
    }
    
    _lockState = LockState.authenticating;
    notifyListeners();
    
    bool success = false;
    
    try {
      switch (_lockType) {
        case LockType.pin:
          success = input == _pin;
          break;
        case LockType.password:
          success = input == _password;
          break;
        case LockType.fingerprint:
        case LockType.face:
          success = await _authenticateWithBiometrics();
          break;
        case LockType.none:
          success = true;
          break;
      }
      
      if (success) {
        _lockState = LockState.unlocked;
        _failedAttempts = 0;
        _lockoutUntil = null;
        _lastActiveTime = DateTime.now();
        _startAutoLockTimer();
        debugPrint('🔐 App unlocked successfully');
      } else {
        _lockState = LockState.locked;
        _failedAttempts++;
        
        if (_failedAttempts >= maxFailedAttempts) {
          _lockoutUntil = DateTime.now().add(const Duration(minutes: lockoutDurationMinutes));
          debugPrint('🔐 Too many failed attempts. Locked out until $_lockoutUntil');
        }
        
        debugPrint('🔐 Failed unlock attempt: $_failedAttempts/$maxFailedAttempts');
      }
      
      await _saveSettings();
      notifyListeners();
      return success;
    } catch (e) {
      _lockState = LockState.locked;
      notifyListeners();
      debugPrint('❌ Error during unlock: $e');
      return false;
    }
  }

  /// المصادقة بالبيومترية
  Future<bool> _authenticateWithBiometrics() async {
    try {
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to unlock ArzaTalk',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      
      return isAuthenticated;
    } catch (e) {
      debugPrint('❌ Biometric authentication error: $e');
      return false;
    }
  }

  /// قفل التطبيق
  void lock() {
    if (_lockType != LockType.none) {
      _lockState = LockState.locked;
      _stopAutoLockTimer();
      notifyListeners();
      debugPrint('🔐 App locked');
    }
  }

  /// تحديث النشاط
  void updateActivity() {
    if (_lockState == LockState.unlocked) {
      _lastActiveTime = DateTime.now();
      _resetAutoLockTimer();
    }
  }

  /// بدء مؤقت القفل التلقائي
  void _startAutoLockTimer() {
    if (!_autoLockEnabled || _lockType == LockType.none) return;
    
    _stopAutoLockTimer();
    _autoLockTimer = Timer(Duration(minutes: _autoLockDuration), () {
      lock();
    });
  }

  /// إيقاف مؤقت القفل التلقائي
  void _stopAutoLockTimer() {
    _autoLockTimer?.cancel();
    _autoLockTimer = null;
  }

  /// إعادة تعيين مؤقت القفل التلقائي
  void _resetAutoLockTimer() {
    if (_autoLockEnabled && _lockState == LockState.unlocked) {
      _startAutoLockTimer();
    }
  }

  /// تحديث حالة القفل
  void _updateLockState() {
    if (_lockType == LockType.none) {
      _lockState = LockState.unlocked;
    } else if (_lockOnAppStart || _shouldAutoLock()) {
      _lockState = LockState.locked;
    } else {
      _lockState = LockState.unlocked;
    }
    
    notifyListeners();
  }

  /// التحقق من ضرورة القفل التلقائي
  bool _shouldAutoLock() {
    if (!_autoLockEnabled || _lastActiveTime == null) return false;
    
    final timeSinceLastActivity = DateTime.now().difference(_lastActiveTime!);
    return timeSinceLastActivity.inMinutes >= _autoLockDuration;
  }

  /// التحقق من حالة الحظر
  bool _isLockedOut() {
    if (_lockoutUntil == null) return false;
    
    if (DateTime.now().isAfter(_lockoutUntil!)) {
      _lockoutUntil = null;
      _failedAttempts = 0;
      _saveSettings();
      return false;
    }
    
    return true;
  }

  /// الحصول على الوقت المتبقي للحظر
  Duration? getLockoutTimeRemaining() {
    if (_lockoutUntil == null) return null;
    
    final remaining = _lockoutUntil!.difference(DateTime.now());
    return remaining.isNegative ? null : remaining;
  }

  /// التحقق من توفر البيومترية
  Future<Map<String, bool>> getAvailableBiometrics() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        return {'fingerprint': false, 'face': false};
      }
      
      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      
      return {
        'fingerprint': availableBiometrics.contains(BiometricType.fingerprint),
        'face': availableBiometrics.contains(BiometricType.face),
      };
    } catch (e) {
      debugPrint('❌ Error checking biometrics: $e');
      return {'fingerprint': false, 'face': false};
    }
  }

  /// تعيين إعدادات القفل التلقائي
  Future<void> setAutoLockSettings({
    required bool enabled,
    required int durationMinutes,
    required bool lockOnAppStart,
    required bool lockOnBackground,
  }) async {
    _autoLockEnabled = enabled;
    _autoLockDuration = durationMinutes;
    _lockOnAppStart = lockOnAppStart;
    _lockOnBackground = lockOnBackground;
    
    await _saveSettings();
    
    if (enabled) {
      _startAutoLockTimer();
    } else {
      _stopAutoLockTimer();
    }
    
    notifyListeners();
  }

  // Getters
  LockState get lockState => _lockState;
  LockType get lockType => _lockType;
  bool get isLocked => _lockState == LockState.locked;
  bool get isUnlocked => _lockState == LockState.unlocked;
  bool get isAuthenticating => _lockState == LockState.authenticating;
  bool get autoLockEnabled => _autoLockEnabled;
  int get autoLockDuration => _autoLockDuration;
  bool get lockOnAppStart => _lockOnAppStart;
  bool get lockOnBackground => _lockOnBackground;
  int get failedAttempts => _failedAttempts;
  int get remainingAttempts => maxFailedAttempts - _failedAttempts;
  bool get isLockedOut => _isLockedOut();

  @override
  void dispose() {
    _stopAutoLockTimer();
    super.dispose();
  }
}
