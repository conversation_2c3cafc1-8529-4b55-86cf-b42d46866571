import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/call_service.dart';
import '../../models/user_model.dart';
import '../../models/group_model.dart';

class CallScreen extends StatefulWidget {
  final UserModel? otherUser;
  final GroupModel? group;
  final CallType callType;

  const CallScreen({
    super.key,
    this.otherUser,
    this.group,
    required this.callType,
  });

  @override
  State<CallScreen> createState() => _CallScreenState();
}

class _CallScreenState extends State<CallScreen> with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isVideoOn = true;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Consumer<CallService>(
        builder: (context, callService, child) {
          final currentCall = callService.currentCall;
          
          return SafeArea(
            child: Column(
              children: [
                // Top section with user info
                Expanded(
                  flex: 2,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // User/Group avatar
                        AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: currentCall?.status == CallStatus.ringing 
                                  ? _pulseAnimation.value 
                                  : 1.0,
                              child: CircleAvatar(
                                radius: 80,
                                backgroundColor: const Color(0xFFD32F2F),
                                backgroundImage: _getAvatarImage(),
                                child: _getAvatarImage() == null
                                    ? Text(
                                        _getDisplayName()[0].toUpperCase(),
                                        style: const TextStyle(
                                          fontSize: 60,
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      )
                                    : null,
                              ),
                            );
                          },
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Name
                        Text(
                          _getDisplayName(),
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Status
                        Text(
                          currentCall != null 
                              ? callService.getCallStatusText(currentCall)
                              : 'Connecting...',
                          style: const TextStyle(
                            fontSize: 18,
                            color: Colors.white70,
                          ),
                        ),
                        
                        if (widget.group != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            '${widget.group!.members.length} members',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white60,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                
                // Video area (for video calls)
                if (widget.callType == CallType.video)
                  Expanded(
                    flex: 1,
                    child: Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[900],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: _isVideoOn
                            ? const Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.videocam,
                                    size: 48,
                                    color: Colors.white60,
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Video Preview',
                                    style: TextStyle(
                                      color: Colors.white60,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              )
                            : const Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.videocam_off,
                                    size: 48,
                                    color: Colors.white60,
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Video Off',
                                    style: TextStyle(
                                      color: Colors.white60,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                
                // Control buttons
                Container(
                  padding: const EdgeInsets.all(24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Mute button
                      _buildControlButton(
                        icon: _isMuted ? Icons.mic_off : Icons.mic,
                        onPressed: () {
                          setState(() {
                            _isMuted = !_isMuted;
                          });
                        },
                        backgroundColor: _isMuted ? Colors.red : Colors.white24,
                      ),
                      
                      // Speaker button (voice calls only)
                      if (widget.callType == CallType.voice)
                        _buildControlButton(
                          icon: _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
                          onPressed: () {
                            setState(() {
                              _isSpeakerOn = !_isSpeakerOn;
                            });
                          },
                          backgroundColor: _isSpeakerOn ? const Color(0xFFD32F2F) : Colors.white24,
                        ),
                      
                      // Video toggle (video calls only)
                      if (widget.callType == CallType.video)
                        _buildControlButton(
                          icon: _isVideoOn ? Icons.videocam : Icons.videocam_off,
                          onPressed: () {
                            setState(() {
                              _isVideoOn = !_isVideoOn;
                            });
                          },
                          backgroundColor: _isVideoOn ? Colors.white24 : Colors.red,
                        ),
                      
                      // End call button
                      _buildControlButton(
                        icon: Icons.call_end,
                        onPressed: () {
                          callService.endCall();
                          Navigator.of(context).pop();
                        },
                        backgroundColor: Colors.red,
                        size: 64,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color backgroundColor,
    double size = 56,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: Colors.white,
          size: size * 0.4,
        ),
        onPressed: onPressed,
      ),
    );
  }

  String _getDisplayName() {
    if (widget.otherUser != null) {
      return widget.otherUser!.name;
    } else if (widget.group != null) {
      return widget.group!.name;
    }
    return 'Unknown';
  }

  ImageProvider? _getAvatarImage() {
    if (widget.otherUser?.profileImageUrl != null) {
      return NetworkImage(widget.otherUser!.profileImageUrl!);
    } else if (widget.group?.groupImageUrl != null) {
      return NetworkImage(widget.group!.groupImageUrl!);
    }
    return null;
  }
}
