# ArzaTalk - تطبيق دردشة بسيط شبيه بواتساب

تطبيق دردشة متكامل مبني بـ Flutter يدعم الرسائل النصية والصور والرسائل الصوتية مع إمكانية المكالمات الصوتية والفيديو.

## 🚀 الميزات

### ✅ الميزات المتاحة حالياً:
- 📱 تسجيل الدخول برقم الهاتف (بدون كود تحقق)
- 💬 إرسال واستقبال الرسائل النصية في الوقت الفعلي
- 📷 إرسال الصور من المعرض أو الكاميرا
- 🎙️ تسجيل وإرسال الرسائل الصوتية
- 👥 عرض قائمة المستخدمين المسجلين
- 🔍 البحث عن المستخدمين
- 📱 واجهة مستخدم شبيهة بواتساب
- 🔄 حالة الاتصال (متصل/غير متصل)
- ✅ علامات قراءة الرسائل

### 🔄 قيد التطوير:
- ☎️ المكالمات الصوتية
- 🎥 مكالمات الفيديو
- 🔔 الإشعارات
- 🔒 التشفير

## 🛠️ التقنيات المستخدمة

- **Flutter** - إطار العمل الأساسي
- **Firebase Firestore** - قاعدة البيانات
- **Firebase Storage** - تخزين الملفات
- **Provider** - إدارة الحالة
- **flutter_sound** - تسجيل وتشغيل الصوت
- **image_picker** - اختيار الصور
- **cached_network_image** - عرض الصور
- **agora_rtc_engine** - المكالمات (قيد التطوير)

## 📋 متطلبات التشغيل

- Flutter SDK 3.7.2 أو أحدث
- Dart SDK 3.0.0 أو أحدث
- Android Studio أو VS Code
- حساب Firebase مع مشروع جديد

## ⚙️ إعداد المشروع

### 1. إعداد Firebase

1. إنشاء مشروع جديد في [Firebase Console](https://console.firebase.google.com/)
2. تفعيل Firestore Database
3. تفعيل Firebase Storage
4. إضافة تطبيق Android/iOS للمشروع
5. تحميل ملفات الإعداد:
   - `google-services.json` للـ Android (ضعه في `android/app/`)
   - `GoogleService-Info.plist` للـ iOS (ضعه في `ios/Runner/`)

### 2. تحديث إعدادات Firebase

قم بتحديث الملف `lib/firebase_options.dart` بمعلومات مشروعك:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'YOUR_ANDROID_API_KEY',
  appId: 'YOUR_ANDROID_APP_ID',
  messagingSenderId: 'YOUR_MESSAGING_SENDER_ID',
  projectId: 'YOUR_PROJECT_ID',
  storageBucket: 'YOUR_PROJECT_ID.appspot.com',
);
```

### 3. تثبيت المكتبات

```bash
flutter pub get
```

### 4. تشغيل التطبيق

```bash
flutter run
```

## 📱 كيفية الاستخدام

### تسجيل الدخول
1. أدخل اسمك ورقم هاتفك
2. اضغط "دخول"
3. سيتم حفظ معلوماتك محلياً للدخول السريع

### بدء محادثة
1. اضغط على زر الإضافة (+) في الشاشة الرئيسية
2. اختر مستخدماً من القائمة
3. ابدأ المحادثة

### إرسال الرسائل
- **نص**: اكتب في حقل النص واضغط إرسال
- **صورة**: اضغط على زر المرفقات واختر صورة
- **صوت**: اضغط مطولاً على زر الميكروفون

## 🏗️ بنية المشروع

```
lib/
├── main.dart                 # نقطة البداية
├── firebase_options.dart     # إعدادات Firebase
├── models/                   # نماذج البيانات
│   ├── user_model.dart
│   ├── message_model.dart
│   └── chat_model.dart
├── services/                 # الخدمات
│   ├── auth_service.dart
│   ├── chat_service.dart
│   └── storage_service.dart
├── screens/                  # الشاشات
│   ├── auth/
│   ├── home/
│   ├── chat/
│   └── users/
├── widgets/                  # المكونات المخصصة
│   ├── message_bubble.dart
│   ├── voice_recorder.dart
│   ├── image_message.dart
│   └── voice_message.dart
└── utils/                    # المساعدات والثوابت
    ├── constants.dart
    └── helpers.dart
```

## 🔧 إعداد قواعد Firestore

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if true;
    }

    // قواعد الرسائل
    match /messages/{messageId} {
      allow read, write: if true;
    }

    // قواعد المحادثات
    match /chats/{chatId} {
      allow read, write: if true;
    }
  }
}
```

## 🔧 إعداد قواعد Firebase Storage

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في Firebase**: تأكد من إعداد Firebase بشكل صحيح
2. **مشاكل الصلاحيات**: تأكد من إضافة الصلاحيات في AndroidManifest.xml
3. **مشاكل التسجيل الصوتي**: تأكد من منح صلاحية الميكروفون

## 📝 ملاحظات مهمة

- هذا التطبيق مخصص للتطوير والتعلم
- لا يستخدم تشفير end-to-end حالياً
- يتطلب اتصال إنترنت للعمل
- تأكد من إعداد Firebase بشكل صحيح

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

إذا واجهت أي مشاكل، يرجى إنشاء Issue في GitHub.
