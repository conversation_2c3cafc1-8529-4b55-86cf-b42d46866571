import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/story_service.dart';

/// شاشة إضافة قصة نصية
class AddTextStoryScreen extends StatefulWidget {
  const AddTextStoryScreen({super.key});

  @override
  State<AddTextStoryScreen> createState() => _AddTextStoryScreenState();
}

class _AddTextStoryScreenState extends State<AddTextStoryScreen> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  
  Color _selectedBackgroundColor = const Color(0xFFD32F2F);
  Color _selectedTextColor = Colors.white;
  String _selectedFont = 'Roboto';
  bool _isLoading = false;

  // ألوان الخلفية المتاحة
  final List<Color> _backgroundColors = [
    const Color(0xFFD32F2F), // أحمر
    const Color(0xFF4CAF50), // أخضر
    const Color(0xFF2196F3), // أزرق
    const Color(0xFF9C27B0), // بنفسجي
    const Color(0xFFFF9800), // برتقالي
    const Color(0xFF607D8B), // رمادي مزرق
    const Color(0xFF795548), // بني
    const Color(0xFF000000), // أسود
    const Color(0xFFE91E63), // وردي
    const Color(0xFF00BCD4), // سماوي
  ];

  // ألوان النص المتاحة
  final List<Color> _textColors = [
    Colors.white,
    Colors.black,
    const Color(0xFFFFEB3B), // أصفر
    const Color(0xFFFF5722), // أحمر برتقالي
    const Color(0xFF3F51B5), // أزرق داكن
  ];

  // الخطوط المتاحة
  final List<String> _fonts = [
    'Roboto',
    'Arial',
    'Times New Roman',
    'Courier New',
    'Georgia',
  ];

  @override
  void initState() {
    super.initState();
    // التركيز على حقل النص عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _selectedBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.white),
        ),
        title: const Text(
          'Text Story',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          if (_textController.text.trim().isNotEmpty)
            TextButton(
              onPressed: _isLoading ? null : _publishStory,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'Share',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
            ),
        ],
      ),
      body: Column(
        children: [
          // منطقة النص الرئيسية
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              child: Center(
                child: TextField(
                  controller: _textController,
                  focusNode: _focusNode,
                  maxLines: null,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: _selectedTextColor,
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                    fontFamily: _selectedFont,
                  ),
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    hintText: 'Type your story...',
                    hintStyle: TextStyle(
                      color: Colors.white70,
                      fontSize: 24,
                    ),
                  ),
                  onChanged: (text) {
                    setState(() {}); // لتحديث زر Share
                  },
                ),
              ),
            ),
          ),
          
          // أدوات التحكم
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // ألوان الخلفية
                _buildColorSection(
                  title: 'Background Color',
                  colors: _backgroundColors,
                  selectedColor: _selectedBackgroundColor,
                  onColorSelected: (color) {
                    setState(() {
                      _selectedBackgroundColor = color;
                    });
                  },
                ),
                
                const SizedBox(height: 16),
                
                // ألوان النص
                _buildColorSection(
                  title: 'Text Color',
                  colors: _textColors,
                  selectedColor: _selectedTextColor,
                  onColorSelected: (color) {
                    setState(() {
                      _selectedTextColor = color;
                    });
                  },
                ),
                
                const SizedBox(height: 16),
                
                // اختيار الخط
                _buildFontSection(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الألوان
  Widget _buildColorSection({
    required String title,
    required List<Color> colors,
    required Color selectedColor,
    required Function(Color) onColorSelected,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: colors.length,
            itemBuilder: (context, index) {
              final color = colors[index];
              final isSelected = color == selectedColor;
              
              return GestureDetector(
                onTap: () => onColorSelected(color),
                child: Container(
                  width: 40,
                  height: 40,
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Colors.white : Colors.transparent,
                      width: 3,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 20,
                        )
                      : null,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء قسم الخطوط
  Widget _buildFontSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Font Family',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _fonts.length,
            itemBuilder: (context, index) {
              final font = _fonts[index];
              final isSelected = font == _selectedFont;
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedFont = font;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? Colors.white : Colors.white.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    font,
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: font,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// نشر القصة
  Future<void> _publishStory() async {
    final text = _textController.text.trim();
    if (text.isEmpty) return;

    setState(() => _isLoading = true);

    try {
      final storyService = Provider.of<StoryService>(context, listen: false);
      final success = await storyService.addTextStory(
        content: text,
        backgroundColor: _selectedBackgroundColor,
        textColor: _selectedTextColor,
        fontFamily: _selectedFont,
      );

      if (success && mounted) {
        Navigator.of(context).pop(); // العودة لشاشة إضافة القصة
        Navigator.of(context).pop(); // العودة لشاشة القصص
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Text story shared successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to share story. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing story: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
