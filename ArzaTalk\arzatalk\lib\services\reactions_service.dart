import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';
import '../models/reaction_model.dart';

/// خدمة ردود الفعل على الرسائل
class ReactionsService extends ChangeNotifier {
  static final ReactionsService _instance = ReactionsService._internal();
  factory ReactionsService() => _instance;
  ReactionsService._internal();

  // قائمة ردود الفعل
  final List<MessageReaction> _reactions = [];

  // Controllers للبيانات المباشرة
  final StreamController<List<MessageReaction>> _reactionsController = StreamController.broadcast();

  // الإيموجي السريعة الافتراضية
  final List<String> _quickEmojis = [
    '👍', '❤️', '😂', '😮', '😢', '😡', '👏', '🔥',
    '💯', '🎉', '😍', '🤔', '👌', '🙏', '💪', '✨'
  ];

  // إحصائيات ردود الفعل
  final Map<String, Map<String, int>> _reactionStats = {};

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _loadReactions();
    await _loadStats();
  }

  /// إضافة رد فعل على رسالة
  Future<bool> addReaction({
    required String messageId,
    required String emoji,
    required String userId,
    required String userName,
  }) async {
    try {
      // التحقق من وجود رد فعل مسبق من نفس المستخدم على نفس الرسالة
      final existingReaction = _reactions.firstWhere(
        (r) => r.messageId == messageId && r.userId == userId,
        orElse: () => MessageReaction(
          id: '',
          messageId: '',
          userId: '',
          userName: '',
          emoji: '',
          createdAt: DateTime.now(),
        ),
      );

      if (existingReaction.id.isNotEmpty) {
        // إذا كان نفس الإيموجي، احذف الرد
        if (existingReaction.emoji == emoji) {
          return await removeReaction(existingReaction.id);
        } else {
          // إذا كان إيموجي مختلف، حدث الرد
          return await updateReaction(existingReaction.id, emoji);
        }
      }

      // إضافة رد فعل جديد
      final reaction = MessageReaction(
        id: 'reaction_${DateTime.now().millisecondsSinceEpoch}',
        messageId: messageId,
        userId: userId,
        userName: userName,
        emoji: emoji,
        createdAt: DateTime.now(),
      );

      _reactions.add(reaction);
      _updateStats(emoji, 1);
      await _saveReactions();
      _updateStreams();

      debugPrint('🎯 Reaction added: $emoji by $userName');
      return true;
    } catch (e) {
      debugPrint('❌ Error adding reaction: $e');
      return false;
    }
  }

  /// تحديث رد فعل
  Future<bool> updateReaction(String reactionId, String newEmoji) async {
    try {
      final index = _reactions.indexWhere((r) => r.id == reactionId);
      if (index == -1) return false;

      final oldEmoji = _reactions[index].emoji;
      _reactions[index] = _reactions[index].copyWith(emoji: newEmoji);

      // تحديث الإحصائيات
      _updateStats(oldEmoji, -1);
      _updateStats(newEmoji, 1);

      await _saveReactions();
      _updateStreams();

      debugPrint('🎯 Reaction updated: $oldEmoji -> $newEmoji');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating reaction: $e');
      return false;
    }
  }

  /// حذف رد فعل
  Future<bool> removeReaction(String reactionId) async {
    try {
      final index = _reactions.indexWhere((r) => r.id == reactionId);
      if (index == -1) return false;

      final reaction = _reactions[index];
      _reactions.removeAt(index);
      _updateStats(reaction.emoji, -1);

      await _saveReactions();
      _updateStreams();

      debugPrint('🎯 Reaction removed: ${reaction.emoji}');
      return true;
    } catch (e) {
      debugPrint('❌ Error removing reaction: $e');
      return false;
    }
  }

  /// الحصول على ردود الفعل لرسالة معينة
  List<MessageReaction> getReactionsForMessage(String messageId) {
    return _reactions.where((r) => r.messageId == messageId).toList();
  }

  /// الحصول على إحصائيات ردود الفعل لرسالة
  List<ReactionStats> getReactionStatsForMessage(String messageId, String currentUserId) {
    final messageReactions = getReactionsForMessage(messageId);
    final groupedReactions = <String, List<MessageReaction>>{};

    // تجميع ردود الفعل حسب الإيموجي
    for (final reaction in messageReactions) {
      if (!groupedReactions.containsKey(reaction.emoji)) {
        groupedReactions[reaction.emoji] = [];
      }
      groupedReactions[reaction.emoji]!.add(reaction);
    }

    // إنشاء إحصائيات
    final stats = <ReactionStats>[];
    for (final entry in groupedReactions.entries) {
      final emoji = entry.key;
      final reactions = entry.value;
      final userNames = reactions.map((r) => r.userName).toList();
      final currentUserReacted = reactions.any((r) => r.userId == currentUserId);

      stats.add(ReactionStats(
        emoji: emoji,
        count: reactions.length,
        userNames: userNames,
        currentUserReacted: currentUserReacted,
      ));
    }

    // ترتيب حسب العدد
    stats.sort((a, b) => b.count.compareTo(a.count));
    return stats;
  }

  /// التحقق من وجود رد فعل من المستخدم الحالي
  bool hasUserReacted(String messageId, String userId) {
    return _reactions.any((r) => r.messageId == messageId && r.userId == userId);
  }

  /// الحصول على رد فعل المستخدم الحالي
  MessageReaction? getUserReaction(String messageId, String userId) {
    try {
      return _reactions.firstWhere(
        (r) => r.messageId == messageId && r.userId == userId,
      );
    } catch (e) {
      return null;
    }
  }

  /// الحصول على الإيموجي السريعة
  List<String> getQuickEmojis() {
    return List.from(_quickEmojis);
  }

  /// إضافة إيموجي للقائمة السريعة
  void addQuickEmoji(String emoji) {
    if (!_quickEmojis.contains(emoji)) {
      _quickEmojis.add(emoji);
      if (_quickEmojis.length > 20) {
        _quickEmojis.removeAt(0); // إزالة الأقدم
      }
      _saveQuickEmojis();
      notifyListeners();
    }
  }

  /// إزالة إيموجي من القائمة السريعة
  void removeQuickEmoji(String emoji) {
    _quickEmojis.remove(emoji);
    _saveQuickEmojis();
    notifyListeners();
  }

  /// الحصول على أكثر الإيموجي استخداماً
  List<String> getMostUsedEmojis({int limit = 8}) {
    final sortedEmojis = _reactionStats.entries.toList()
      ..sort((a, b) => b.value.values.fold(0, (sum, count) => sum + count)
          .compareTo(a.value.values.fold(0, (sum, count) => sum + count)));

    return sortedEmojis.take(limit).map((e) => e.key).toList();
  }

  /// حذف جميع ردود الفعل لرسالة
  Future<void> clearReactionsForMessage(String messageId) async {
    final reactionsToRemove = _reactions.where((r) => r.messageId == messageId).toList();

    for (final reaction in reactionsToRemove) {
      _reactions.remove(reaction);
      _updateStats(reaction.emoji, -1);
    }

    await _saveReactions();
    _updateStreams();
    debugPrint('🎯 Cleared ${reactionsToRemove.length} reactions for message: $messageId');
  }

  /// حذف جميع ردود الفعل لدردشة
  Future<void> clearReactionsForChat(String chatId) async {
    // هذا يتطلب معرفة chatId من messageId
    // يمكن تحسينه لاحقاً بإضافة chatId للنموذج
    debugPrint('🎯 Clear reactions for chat: $chatId (not implemented yet)');
  }

  /// تحديث الإحصائيات
  void _updateStats(String emoji, int change) {
    if (!_reactionStats.containsKey(emoji)) {
      _reactionStats[emoji] = {'total': 0};
    }
    _reactionStats[emoji]!['total'] = (_reactionStats[emoji]!['total'] ?? 0) + change;

    // إزالة الإيموجي إذا وصل العدد لصفر
    if (_reactionStats[emoji]!['total']! <= 0) {
      _reactionStats.remove(emoji);
    }

    _saveStats();
  }

  /// تحديث البيانات المباشرة
  void _updateStreams() {
    _reactionsController.add(List.from(_reactions));
    notifyListeners();
  }

  /// تحميل ردود الفعل
  Future<void> _loadReactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reactionsJson = prefs.getString('message_reactions');

      if (reactionsJson != null) {
        final reactionsList = json.decode(reactionsJson) as List;
        _reactions.clear();
        for (final reactionMap in reactionsList) {
          _reactions.add(MessageReaction.fromMap(reactionMap));
        }
      }

      debugPrint('🎯 Loaded ${_reactions.length} reactions');
    } catch (e) {
      debugPrint('❌ Error loading reactions: $e');
    }
  }

  /// حفظ ردود الفعل
  Future<void> _saveReactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reactionsList = _reactions.map((r) => r.toMap()).toList();
      final reactionsJson = json.encode(reactionsList);
      await prefs.setString('message_reactions', reactionsJson);
    } catch (e) {
      debugPrint('❌ Error saving reactions: $e');
    }
  }

  /// تحميل الإحصائيات
  Future<void> _loadStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString('reaction_stats');

      if (statsJson != null) {
        final statsMap = json.decode(statsJson) as Map<String, dynamic>;
        _reactionStats.clear();
        for (final entry in statsMap.entries) {
          _reactionStats[entry.key] = Map<String, int>.from(entry.value);
        }
      }

      debugPrint('🎯 Loaded reaction stats for ${_reactionStats.length} emojis');
    } catch (e) {
      debugPrint('❌ Error loading reaction stats: $e');
    }
  }

  /// حفظ الإحصائيات
  Future<void> _saveStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = json.encode(_reactionStats);
      await prefs.setString('reaction_stats', statsJson);
    } catch (e) {
      debugPrint('❌ Error saving reaction stats: $e');
    }
  }

  /// حفظ الإيموجي السريعة
  Future<void> _saveQuickEmojis() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('quick_emojis', _quickEmojis);
    } catch (e) {
      debugPrint('❌ Error saving quick emojis: $e');
    }
  }

  /// تحميل الإيموجي السريعة
  Future<void> _loadQuickEmojis() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedEmojis = prefs.getStringList('quick_emojis');
      if (savedEmojis != null) {
        _quickEmojis.clear();
        _quickEmojis.addAll(savedEmojis);
      }
    } catch (e) {
      debugPrint('❌ Error loading quick emojis: $e');
    }
  }

  // Getters
  Stream<List<MessageReaction>> get reactionsStream => _reactionsController.stream;
  List<MessageReaction> get allReactions => List.from(_reactions);
  Map<String, Map<String, int>> get reactionStats => Map.from(_reactionStats);
  int get totalReactions => _reactions.length;

  @override
  void dispose() {
    _reactionsController.close();
    super.dispose();
  }
}
