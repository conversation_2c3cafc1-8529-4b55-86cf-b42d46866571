import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../services/auth_service.dart';
import '../../services/group_service.dart';
import '../../models/group_model.dart';
import '../groups/create_group_screen.dart';
import '../groups/group_chat_screen.dart';

class GroupsTab extends StatefulWidget {
  const GroupsTab({super.key});

  @override
  State<GroupsTab> createState() => _GroupsTabState();
}

class _GroupsTabState extends State<GroupsTab> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer2<AuthService, GroupService>(
        builder: (context, authService, groupService, child) {
          final currentUser = authService.currentUser;
          if (currentUser == null) {
            return const Center(child: Text('User not found'));
          }

          final groups = groupService.getUserGroups(currentUser.phoneNumber);

          if (groups.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.group,
                    size: 80,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No groups yet',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Create a group to start chatting with multiple people',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: groups.length,
            itemBuilder: (context, index) {
              final group = groups[index];
              return _buildGroupTile(group, currentUser.phoneNumber);
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const CreateGroupScreen(),
            ),
          );
        },
        backgroundColor: const Color(0xFFD32F2F),
        child: const Icon(Icons.group_add, color: Colors.white),
      ),
    );
  }

  Widget _buildGroupTile(GroupModel group, String currentUserPhone) {
    return Dismissible(
      key: Key(group.id),
      background: Container(
        color: Colors.green,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: const Icon(Icons.archive, color: Colors.white),
      ),
      secondaryBackground: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: const Icon(Icons.exit_to_app, color: Colors.white),
      ),
      onDismissed: (direction) {
        if (direction == DismissDirection.startToEnd) {
          // Archive group
          Provider.of<GroupService>(context, listen: false)
              .toggleGroupArchive(group.id);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(group.isArchived ? 'Group unarchived' : 'Group archived'),
            ),
          );
        } else {
          // Leave group
          Provider.of<GroupService>(context, listen: false)
              .leaveGroup(group.id, currentUserPhone);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Left group')),
          );
        }
      },
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: const Color(0xFFD32F2F),
          backgroundImage: group.groupImageUrl != null
              ? NetworkImage(group.groupImageUrl!)
              : null,
          child: group.groupImageUrl == null
              ? Text(
                  group.name.isNotEmpty ? group.name[0].toUpperCase() : 'G',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                group.name,
                style: TextStyle(
                  fontWeight: group.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (group.isMuted)
              const Icon(Icons.volume_off, size: 16, color: Colors.grey),
            if (group.isArchived)
              const Icon(Icons.archive, size: 16, color: Colors.grey),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              group.lastMessage ?? 'No messages yet',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: group.unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
            Text(
              '${group.members.length} members',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _formatTime(group.lastMessageTime),
              style: TextStyle(
                color: group.unreadCount > 0 ? const Color(0xFFD32F2F) : Colors.grey[500],
                fontSize: 12,
                fontWeight: group.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            if (group.unreadCount > 0)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFD32F2F),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  group.unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => GroupChatScreen(group: group),
            ),
          );
        },
        onLongPress: () {
          _showGroupOptions(context, group, currentUserPhone);
        },
      ),
    );
  }

  void _showGroupOptions(BuildContext context, GroupModel group, String currentUserPhone) {
    final isAdmin = group.isAdmin(currentUserPhone);
    
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('Group Info'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to group info screen
              },
            ),
            ListTile(
              leading: Icon(group.isArchived ? Icons.unarchive : Icons.archive),
              title: Text(group.isArchived ? 'Unarchive' : 'Archive'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<GroupService>(context, listen: false)
                    .toggleGroupArchive(group.id);
              },
            ),
            ListTile(
              leading: Icon(group.isMuted ? Icons.volume_up : Icons.volume_off),
              title: Text(group.isMuted ? 'Unmute' : 'Mute'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<GroupService>(context, listen: false)
                    .toggleGroupMute(group.id);
              },
            ),
            if (isAdmin) ...[
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Edit Group'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to edit group screen
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Delete Group'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteGroupDialog(context, group);
                },
              ),
            ],
            ListTile(
              leading: const Icon(Icons.exit_to_app),
              title: const Text('Leave Group'),
              onTap: () {
                Navigator.pop(context);
                _showLeaveGroupDialog(context, group, currentUserPhone);
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear),
              title: const Text('Clear Messages'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<GroupService>(context, listen: false)
                    .clearGroupMessages(group.id);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteGroupDialog(BuildContext context, GroupModel group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Group'),
        content: Text('Are you sure you want to delete "${group.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<GroupService>(context, listen: false)
                  .deleteGroup(group.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Group deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showLeaveGroupDialog(BuildContext context, GroupModel group, String currentUserPhone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Group'),
        content: Text('Are you sure you want to leave "${group.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<GroupService>(context, listen: false)
                  .leaveGroup(group.id, currentUserPhone);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Left group')),
              );
            },
            child: const Text('Leave', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(dateTime);
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE').format(dateTime);
    } else {
      return DateFormat('dd/MM/yyyy').format(dateTime);
    }
  }
}
