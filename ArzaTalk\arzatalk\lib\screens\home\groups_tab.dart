import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../services/auth_service.dart';
import '../../services/group_service.dart';
import '../../models/group_model.dart';
import '../groups/create_group_screen.dart';
import '../groups/group_chat_screen.dart';

class GroupsTab extends StatefulWidget {
  const GroupsTab({super.key});

  @override
  State<GroupsTab> createState() => _GroupsTabState();
}

class _GroupsTabState extends State<GroupsTab> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer2<AuthService, GroupService>(
        builder: (context, authService, groupService, child) {
          final currentUser = authService.currentUser;
          if (currentUser == null) {
            return const Center(child: Text('User not found'));
          }

          final groups = groupService.getUserGroups(currentUser.phoneNumber);

          // إذا لم يكن لديه مجموعات، اعرض اقتراحات المجموعات
          if (groups.isEmpty) {
            return _buildGroupSuggestions(groupService, currentUser.phoneNumber);
          }

          return Column(
            children: [
              // اقتراحات المجموعات (إذا كان لديه مجموعات)
              if (groups.isNotEmpty) ...[
                _buildSuggestedGroupsSection(groupService, currentUser.phoneNumber),
                const Divider(height: 1),
              ],

              // قائمة المجموعات الخاصة به
              Expanded(
                child: ListView.builder(
                  itemCount: groups.length,
                  itemBuilder: (context, index) {
                    final group = groups[index];
                    return _buildGroupTile(group, currentUser.phoneNumber);
                  },
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const CreateGroupScreen(),
            ),
          );
        },
        backgroundColor: const Color(0xFFD32F2F),
        child: const Icon(Icons.group_add, color: Colors.white),
      ),
    );
  }

  Widget _buildGroupTile(GroupModel group, String currentUserPhone) {
    return Dismissible(
      key: Key(group.id),
      background: Container(
        color: Colors.green,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: const Icon(Icons.archive, color: Colors.white),
      ),
      secondaryBackground: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: const Icon(Icons.exit_to_app, color: Colors.white),
      ),
      onDismissed: (direction) {
        if (direction == DismissDirection.startToEnd) {
          // Archive group
          Provider.of<GroupService>(context, listen: false)
              .toggleGroupArchive(group.id);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(group.isArchived ? 'Group unarchived' : 'Group archived'),
            ),
          );
        } else {
          // Leave group
          Provider.of<GroupService>(context, listen: false)
              .leaveGroup(group.id, currentUserPhone);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Left group')),
          );
        }
      },
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: const Color(0xFFD32F2F),
          backgroundImage: group.groupImageUrl != null
              ? NetworkImage(group.groupImageUrl!)
              : null,
          child: group.groupImageUrl == null
              ? Text(
                  group.name.isNotEmpty ? group.name[0].toUpperCase() : 'G',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                group.name,
                style: TextStyle(
                  fontWeight: group.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (group.isMuted)
              const Icon(Icons.volume_off, size: 16, color: Colors.grey),
            if (group.isArchived)
              const Icon(Icons.archive, size: 16, color: Colors.grey),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              group.lastMessage ?? 'No messages yet',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: group.unreadCount > 0 ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
            Text(
              '${group.members.length} members',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _formatTime(group.lastMessageTime),
              style: TextStyle(
                color: group.unreadCount > 0 ? const Color(0xFFD32F2F) : Colors.grey[500],
                fontSize: 12,
                fontWeight: group.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            if (group.unreadCount > 0)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFD32F2F),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  group.unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => GroupChatScreen(group: group),
            ),
          );
        },
        onLongPress: () {
          _showGroupOptions(context, group, currentUserPhone);
        },
      ),
    );
  }

  void _showGroupOptions(BuildContext context, GroupModel group, String currentUserPhone) {
    final isAdmin = group.isAdmin(currentUserPhone);

    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('Group Info'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to group info screen
              },
            ),
            ListTile(
              leading: Icon(group.isArchived ? Icons.unarchive : Icons.archive),
              title: Text(group.isArchived ? 'Unarchive' : 'Archive'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<GroupService>(context, listen: false)
                    .toggleGroupArchive(group.id);
              },
            ),
            ListTile(
              leading: Icon(group.isMuted ? Icons.volume_up : Icons.volume_off),
              title: Text(group.isMuted ? 'Unmute' : 'Mute'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<GroupService>(context, listen: false)
                    .toggleGroupMute(group.id);
              },
            ),
            if (isAdmin) ...[
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Edit Group'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to edit group screen
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('Delete Group'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteGroupDialog(context, group);
                },
              ),
            ],
            ListTile(
              leading: const Icon(Icons.exit_to_app),
              title: const Text('Leave Group'),
              onTap: () {
                Navigator.pop(context);
                _showLeaveGroupDialog(context, group, currentUserPhone);
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear),
              title: const Text('Clear Messages'),
              onTap: () {
                Navigator.pop(context);
                Provider.of<GroupService>(context, listen: false)
                    .clearGroupMessages(group.id);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteGroupDialog(BuildContext context, GroupModel group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Group'),
        content: Text('Are you sure you want to delete "${group.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<GroupService>(context, listen: false)
                  .deleteGroup(group.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Group deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showLeaveGroupDialog(BuildContext context, GroupModel group, String currentUserPhone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Leave Group'),
        content: Text('Are you sure you want to leave "${group.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<GroupService>(context, listen: false)
                  .leaveGroup(group.id, currentUserPhone);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Left group')),
              );
            },
            child: const Text('Leave', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return DateFormat('HH:mm').format(dateTime);
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return DateFormat('EEEE').format(dateTime);
    } else {
      return DateFormat('dd/MM/yyyy').format(dateTime);
    }
  }

  /// بناء اقتراحات المجموعات (عندما لا يكون لديه مجموعات)
  Widget _buildGroupSuggestions(GroupService groupService, String currentUserPhone) {
    final allGroups = groupService.getAllGroups();
    final suggestedGroups = allGroups.where((group) =>
      !group.members.contains(currentUserPhone) &&
      group.members.length < 50 // مجموعات ليست ممتلئة
    ).take(10).toList();

    return Column(
      children: [
        // رسالة ترحيبية
        Container(
          padding: const EdgeInsets.all(20),
          child: const Column(
            children: [
              Icon(
                Icons.group,
                size: 80,
                color: Color(0xFF1877F2),
              ),
              SizedBox(height: 16),
              Text(
                'Discover Groups',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1C1E21),
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Join groups to connect with people who share your interests',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF65676B),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        // اقتراحات المجموعات
        if (suggestedGroups.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                const Icon(Icons.recommend, color: Color(0xFF1877F2), size: 20),
                const SizedBox(width: 8),
                Text(
                  'Suggested for you',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1C1E21),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          Expanded(
            child: ListView.builder(
              itemCount: suggestedGroups.length,
              itemBuilder: (context, index) {
                final group = suggestedGroups[index];
                return _buildSuggestedGroupTile(group, groupService, currentUserPhone);
              },
            ),
          ),
        ] else ...[
          const Expanded(
            child: Center(
              child: Text(
                'No groups available to join at the moment.\nCreate your own group!',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF65676B),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// بناء قسم اقتراحات المجموعات (عندما يكون لديه مجموعات)
  Widget _buildSuggestedGroupsSection(GroupService groupService, String currentUserPhone) {
    final allGroups = groupService.getAllGroups();
    final suggestedGroups = allGroups.where((group) =>
      !group.members.contains(currentUserPhone) &&
      group.members.length < 50
    ).take(3).toList();

    if (suggestedGroups.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.recommend, color: Color(0xFF1877F2), size: 20),
              const SizedBox(width: 8),
              const Text(
                'Groups you might like',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1C1E21),
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _showAllSuggestedGroups(groupService, currentUserPhone),
                child: const Text(
                  'See All',
                  style: TextStyle(
                    color: Color(0xFF1877F2),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: suggestedGroups.length,
              itemBuilder: (context, index) {
                final group = suggestedGroups[index];
                return _buildHorizontalGroupCard(group, groupService, currentUserPhone);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة مجموعة مقترحة
  Widget _buildSuggestedGroupTile(GroupModel group, GroupService groupService, String currentUserPhone) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: const Color(0xFF1877F2),
          backgroundImage: group.groupImageUrl != null
              ? NetworkImage(group.groupImageUrl!)
              : null,
          child: group.groupImageUrl == null
              ? Text(
                  group.name.isNotEmpty ? group.name[0].toUpperCase() : 'G',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                )
              : null,
        ),
        title: Text(
          group.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (group.description != null) ...[
              Text(
                group.description!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 12),
              ),
              const SizedBox(height: 4),
            ],
            Text(
              '${group.members.length} members',
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF65676B),
              ),
            ),
          ],
        ),
        trailing: ElevatedButton(
          onPressed: () => _joinGroup(group, groupService, currentUserPhone),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF1877F2),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: const Text('Join', style: TextStyle(fontSize: 12)),
        ),
        onTap: () => _showGroupPreview(group, groupService, currentUserPhone),
      ),
    );
  }

  /// بناء بطاقة مجموعة أفقية
  Widget _buildHorizontalGroupCard(GroupModel group, GroupService groupService, String currentUserPhone) {
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 12),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: const Color(0xFF1877F2),
                    backgroundImage: group.groupImageUrl != null
                        ? NetworkImage(group.groupImageUrl!)
                        : null,
                    child: group.groupImageUrl == null
                        ? Text(
                            group.name.isNotEmpty ? group.name[0].toUpperCase() : 'G',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      group.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                '${group.members.length} members',
                style: const TextStyle(
                  fontSize: 10,
                  color: Color(0xFF65676B),
                ),
              ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _joinGroup(group, groupService, currentUserPhone),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1877F2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 4),
                  ),
                  child: const Text('Join', style: TextStyle(fontSize: 10)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الانضمام إلى مجموعة
  void _joinGroup(GroupModel group, GroupService groupService, String currentUserPhone) async {
    try {
      await groupService.addMemberToGroup(group.id, currentUserPhone);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Joined ${group.name} successfully!'),
            backgroundColor: const Color(0xFF42B883),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to join group: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض معاينة المجموعة
  void _showGroupPreview(GroupModel group, GroupService groupService, String currentUserPhone) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // معلومات المجموعة
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: const Color(0xFF1877F2),
                    backgroundImage: group.groupImageUrl != null
                        ? NetworkImage(group.groupImageUrl!)
                        : null,
                    child: group.groupImageUrl == null
                        ? Text(
                            group.name.isNotEmpty ? group.name[0].toUpperCase() : 'G',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 24,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    group.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${group.members.length} members',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF65676B),
                    ),
                  ),
                  if (group.description != null) ...[
                    const SizedBox(height: 12),
                    Text(
                      group.description!,
                      style: const TextStyle(fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _joinGroup(group, groupService, currentUserPhone);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1877F2),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('Join Group'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض جميع المجموعات المقترحة
  void _showAllSuggestedGroups(GroupService groupService, String currentUserPhone) {
    // يمكن إضافة شاشة منفصلة لاحق<|im_start|>
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All suggested groups feature coming soon!'),
        backgroundColor: Color(0xFF1877F2),
      ),
    );
  }
}
