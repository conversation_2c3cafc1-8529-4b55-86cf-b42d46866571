import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/story_model.dart';
import '../../services/story_service.dart';
import '../../services/auth_service.dart';
import '../stories/add_story_screen.dart';
import '../stories/story_viewer_screen.dart';
import '../stories/my_stories_screen.dart';

/// تبويب القصص في الشاشة الرئيسية
class StoriesTab extends StatefulWidget {
  const StoriesTab({super.key});

  @override
  State<StoriesTab> createState() => _StoriesTabState();
}

class _StoriesTabState extends State<StoriesTab> {
  @override
  Widget build(BuildContext context) {
    return Consumer<StoryService>(
      builder: (context, storyService, child) {
        return StreamBuilder<List<UserStoriesModel>>(
          stream: storyService.userStoriesStream,
          builder: (context, snapshot) {
            final userStories = snapshot.data ?? [];
            
            return CustomScrollView(
              slivers: [
                // قسم "قصتي"
                SliverToBoxAdapter(
                  child: _buildMyStorySection(context),
                ),
                
                // فاصل
                const SliverToBoxAdapter(
                  child: Divider(height: 1, color: Colors.grey),
                ),
                
                // عنوان القصص
                if (userStories.isNotEmpty)
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Text(
                        'Recent Stories',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ),
                
                // قائمة قصص الأصدقاء
                if (userStories.isEmpty)
                  SliverFillRemaining(
                    child: _buildEmptyState(),
                  )
                else
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final userStory = userStories[index];
                        return _buildStoryItem(context, userStory);
                      },
                      childCount: userStories.length,
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  /// بناء قسم "قصتي"
  Widget _buildMyStorySection(BuildContext context) {
    final authService = Provider.of<AuthService>(context, listen: false);
    final storyService = Provider.of<StoryService>(context, listen: false);
    final currentUser = authService.currentUser;
    
    if (currentUser == null) return const SizedBox.shrink();
    
    final myStories = storyService.getCurrentUserStories();
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // صورة المستخدم مع زر الإضافة
          Stack(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: myStories.isNotEmpty 
                        ? const Color(0xFFD32F2F) 
                        : Colors.grey,
                    width: 3,
                  ),
                ),
                child: CircleAvatar(
                  radius: 27,
                  backgroundImage: currentUser.profileImageUrl != null
                      ? NetworkImage(currentUser.profileImageUrl!)
                      : null,
                  backgroundColor: Colors.grey[300],
                  child: currentUser.profileImageUrl == null
                      ? Text(
                          currentUser.name.isNotEmpty 
                              ? currentUser.name[0].toUpperCase() 
                              : '?',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        )
                      : null,
                ),
              ),
              
              // زر الإضافة
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () => _showAddStoryOptions(context),
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: const BoxDecoration(
                      color: Color(0xFFD32F2F),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(width: 16),
          
          // معلومات القصة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'My Story',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  myStories.isNotEmpty 
                      ? 'Tap to view or add to your story'
                      : 'Tap to add to your story',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          // زر عرض قصصي
          if (myStories.isNotEmpty)
            IconButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const MyStoriesScreen(),
                  ),
                );
              },
              icon: const Icon(
                Icons.more_vert,
                color: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.auto_stories_outlined,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'No stories yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Stories from your contacts will appear here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddStoryOptions(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Your First Story'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFD32F2F),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر قصة
  Widget _buildStoryItem(BuildContext context, UserStoriesModel userStory) {
    final latestStory = userStory.latestStory;
    if (latestStory == null) return const SizedBox.shrink();
    
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Stack(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: userStory.hasUnviewedStories 
                    ? const Color(0xFFD32F2F) 
                    : Colors.grey,
                width: 3,
              ),
            ),
            child: CircleAvatar(
              radius: 27,
              backgroundImage: userStory.userProfileImage != null
                  ? NetworkImage(userStory.userProfileImage!)
                  : null,
              backgroundColor: Colors.grey[300],
              child: userStory.userProfileImage == null
                  ? Text(
                      userStory.userName.isNotEmpty 
                          ? userStory.userName[0].toUpperCase() 
                          : '?',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    )
                  : null,
            ),
          ),
          
          // مؤشر عدد القصص
          if (userStory.stories.length > 1)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Color(0xFFD32F2F),
                  shape: BoxShape.circle,
                ),
                child: Text(
                  '${userStory.stories.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
      title: Text(
        userStory.userName,
        style: TextStyle(
          fontWeight: userStory.hasUnviewedStories 
              ? FontWeight.w600 
              : FontWeight.normal,
        ),
      ),
      subtitle: Text(
        _getStoryPreview(latestStory),
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 13,
        ),
      ),
      trailing: Text(
        _getTimeAgo(latestStory.createdAt),
        style: TextStyle(
          color: Colors.grey[500],
          fontSize: 12,
        ),
      ),
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => StoryViewerScreen(
              userStories: userStory,
              initialIndex: 0,
            ),
          ),
        );
      },
    );
  }

  /// عرض خيارات إضافة قصة
  void _showAddStoryOptions(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddStoryScreen(),
      ),
    );
  }

  /// الحصول على معاينة القصة
  String _getStoryPreview(StoryModel story) {
    switch (story.type) {
      case StoryType.text:
        return story.content ?? '';
      case StoryType.image:
        return '📷 Photo';
      case StoryType.video:
        return '🎥 Video';
    }
  }

  /// الحصول على النص الزمني
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
