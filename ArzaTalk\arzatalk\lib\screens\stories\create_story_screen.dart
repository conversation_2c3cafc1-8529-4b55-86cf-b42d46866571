import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/stories_service.dart';
import '../../services/auth_service.dart';
import '../../services/media_service.dart';

/// شاشة إنشاء القصة
class CreateStoryScreen extends StatefulWidget {
  final String? initialMediaPath;
  final String? mediaType; // 'image' or 'video'

  const CreateStoryScreen({
    super.key,
    this.initialMediaPath,
    this.mediaType,
  });

  @override
  State<CreateStoryScreen> createState() => _CreateStoryScreenState();
}

class _CreateStoryScreenState extends State<CreateStoryScreen> {
  final TextEditingController _textController = TextEditingController();
  final MediaService _mediaService = MediaService();
  
  String? _selectedMediaPath;
  String? _selectedMediaType;
  Color _backgroundColor = const Color(0xFF1877F2);
  Color _textColor = Colors.white;
  String _selectedFont = 'Roboto';
  bool _isCreating = false;

  final List<Color> _backgroundColors = [
    const Color(0xFF1877F2), // Facebook Blue
    const Color(0xFFE91E63), // Pink
    const Color(0xFF9C27B0), // Purple
    const Color(0xFF673AB7), // Deep Purple
    const Color(0xFF3F51B5), // Indigo
    const Color(0xFF2196F3), // Blue
    const Color(0xFF03DAC6), // Teal
    const Color(0xFF4CAF50), // Green
    const Color(0xFF8BC34A), // Light Green
    const Color(0xFFCDDC39), // Lime
    const Color(0xFFFFC107), // Amber
    const Color(0xFFFF9800), // Orange
    const Color(0xFFFF5722), // Deep Orange
    const Color(0xFFF44336), // Red
    const Color(0xFF795548), // Brown
    const Color(0xFF607D8B), // Blue Grey
    Colors.black,
    Colors.white,
  ];

  final List<String> _fonts = [
    'Roboto',
    'Arial',
    'Times New Roman',
    'Helvetica',
    'Georgia',
    'Verdana',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialMediaPath != null) {
      _selectedMediaPath = widget.initialMediaPath;
      _selectedMediaType = widget.mediaType;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.white),
        ),
        title: const Text(
          'Create Story',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          if (_isCreating)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _createStory,
              child: const Text(
                'Share',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Stack(
        children: [
          // المحتوى الرئيسي
          Positioned.fill(
            child: _buildStoryPreview(),
          ),

          // أدوات التحكم السفلية
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomControls(),
          ),
        ],
      ),
    );
  }

  /// بناء معاينة القصة
  Widget _buildStoryPreview() {
    if (_selectedMediaPath != null) {
      // قصة وسائط (صورة أو فيديو)
      return _buildMediaStory();
    } else {
      // قصة نصية
      return _buildTextStory();
    }
  }

  /// بناء قصة الوسائط
  Widget _buildMediaStory() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: _selectedMediaType == 'image'
          ? Image.file(
              File(_selectedMediaPath!),
              fit: BoxFit.cover,
            )
          : Container(
              color: Colors.black,
              child: const Center(
                child: Icon(
                  Icons.play_circle_fill,
                  color: Colors.white,
                  size: 80,
                ),
              ),
            ),
    );
  }

  /// بناء قصة نصية
  Widget _buildTextStory() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _backgroundColor,
            _backgroundColor.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: TextField(
            controller: _textController,
            style: TextStyle(
              color: _textColor,
              fontSize: 24,
              fontWeight: FontWeight.bold,
              fontFamily: _selectedFont,
            ),
            textAlign: TextAlign.center,
            maxLines: null,
            decoration: const InputDecoration(
              border: InputBorder.none,
              hintText: 'Type your story...',
              hintStyle: TextStyle(
                color: Colors.white70,
                fontSize: 24,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء أدوات التحكم السفلية
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withValues(alpha: 0.7),
          ],
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أزرار الوسائط
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildControlButton(
                icon: Icons.photo_library,
                label: 'Photo',
                onTap: _pickImage,
              ),
              _buildControlButton(
                icon: Icons.videocam,
                label: 'Video',
                onTap: _pickVideo,
              ),
              _buildControlButton(
                icon: Icons.text_fields,
                label: 'Text',
                onTap: _createTextStory,
              ),
            ],
          ),

          // أدوات النص (تظهر فقط للقصص النصية)
          if (_selectedMediaPath == null) ...[
            const SizedBox(height: 16),
            
            // ألوان الخلفية
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _backgroundColors.length,
                itemBuilder: (context, index) {
                  final color = _backgroundColors[index];
                  return GestureDetector(
                    onTap: () => setState(() => _backgroundColor = color),
                    child: Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _backgroundColor == color 
                              ? Colors.white 
                              : Colors.transparent,
                          width: 3,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 12),

            // أزرار لون النص والخط
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildControlButton(
                  icon: Icons.format_color_text,
                  label: 'Text Color',
                  onTap: _changeTextColor,
                ),
                _buildControlButton(
                  icon: Icons.font_download,
                  label: 'Font',
                  onTap: _changeFont,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// بناء زر تحكم
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// اختيار صورة
  void _pickImage() async {
    final imagePath = await _mediaService.pickImage();
    if (imagePath != null) {
      setState(() {
        _selectedMediaPath = imagePath;
        _selectedMediaType = 'image';
      });
    }
  }

  /// اختيار فيديو
  void _pickVideo() async {
    final videoPath = await _mediaService.pickVideo();
    if (videoPath != null) {
      setState(() {
        _selectedMediaPath = videoPath;
        _selectedMediaType = 'video';
      });
    }
  }

  /// إنشاء قصة نصية
  void _createTextStory() {
    setState(() {
      _selectedMediaPath = null;
      _selectedMediaType = null;
    });
  }

  /// تغيير لون النص
  void _changeTextColor() {
    setState(() {
      _textColor = _textColor == Colors.white ? Colors.black : Colors.white;
    });
  }

  /// تغيير الخط
  void _changeFont() {
    final currentIndex = _fonts.indexOf(_selectedFont);
    final nextIndex = (currentIndex + 1) % _fonts.length;
    setState(() {
      _selectedFont = _fonts[nextIndex];
    });
  }

  /// إنشاء القصة
  void _createStory() async {
    if (_isCreating) return;

    final authService = Provider.of<AuthService>(context, listen: false);
    final storiesService = Provider.of<StoriesService>(context, listen: false);
    final currentUser = authService.currentUser;

    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please login first')),
      );
      return;
    }

    setState(() => _isCreating = true);

    bool success = false;

    try {
      if (_selectedMediaPath != null) {
        // قصة وسائط
        if (_selectedMediaType == 'image') {
          success = await storiesService.createImageStory(
            userId: currentUser.phoneNumber,
            userName: currentUser.name,
            userProfileImage: currentUser.profileImageUrl,
            imageFile: File(_selectedMediaPath!),
          );
        } else if (_selectedMediaType == 'video') {
          success = await storiesService.createVideoStory(
            userId: currentUser.phoneNumber,
            userName: currentUser.name,
            userProfileImage: currentUser.profileImageUrl,
            videoFile: File(_selectedMediaPath!),
          );
        }
      } else {
        // قصة نصية
        if (_textController.text.trim().isNotEmpty) {
          success = await storiesService.createTextStory(
            userId: currentUser.phoneNumber,
            userName: currentUser.name,
            userProfileImage: currentUser.profileImageUrl,
            content: _textController.text.trim(),
            backgroundColor: _backgroundColor,
            textColor: _textColor,
            fontFamily: _selectedFont,
          );
        }
      }

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Story created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(storiesService.error ?? 'Failed to create story'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}
