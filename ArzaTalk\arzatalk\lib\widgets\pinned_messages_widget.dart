import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/reaction_model.dart';
import '../services/pinned_messages_service.dart';

/// Widget لعرض الرسائل المثبتة
class PinnedMessagesWidget extends StatelessWidget {
  final String chatId;
  final Function(String)? onMessageTap;

  const PinnedMessagesWidget({
    super.key,
    required this.chatId,
    this.onMessageTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<PinnedMessagesService>(
      builder: (context, pinnedService, child) {
        final pinnedMessages = pinnedService.getPinnedMessagesForChat(chatId);
        
        if (pinnedMessages.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFD32F2F).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFD32F2F).withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الرسائل المثبتة
              Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    const Icon(
                      Icons.push_pin,
                      color: Color(0xFFD32F2F),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Pinned Messages (${pinnedMessages.length})',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Color(0xFFD32F2F),
                        fontSize: 14,
                      ),
                    ),
                    const Spacer(),
                    if (pinnedMessages.length > 1)
                      GestureDetector(
                        onTap: () => _showAllPinnedMessages(context, pinnedMessages),
                        child: const Text(
                          'View All',
                          style: TextStyle(
                            color: Color(0xFFD32F2F),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              
              // أحدث رسالة مثبتة
              _buildPinnedMessageItem(context, pinnedMessages.first, pinnedService),
            ],
          ),
        );
      },
    );
  }

  /// بناء عنصر رسالة مثبتة
  Widget _buildPinnedMessageItem(
    BuildContext context,
    PinnedMessage pinnedMessage,
    PinnedMessagesService pinnedService,
  ) {
    return GestureDetector(
      onTap: () => onMessageTap?.call(pinnedMessage.messageId),
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // محتوى الرسالة
            Text(
              pinnedMessage.content,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 8),
            
            // معلومات التثبيت
            Row(
              children: [
                Text(
                  'Pinned by ${pinnedMessage.pinnedByName}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                if (pinnedMessage.expiresAt != null) ...[
                  Icon(
                    Icons.schedule,
                    size: 12,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getTimeRemaining(pinnedMessage.expiresAt!),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () => _showPinnedMessageOptions(context, pinnedMessage, pinnedService),
                  child: Icon(
                    Icons.more_vert,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// عرض جميع الرسائل المثبتة
  void _showAllPinnedMessages(BuildContext context, List<PinnedMessage> pinnedMessages) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // عنوان
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(
                      Icons.push_pin,
                      color: Color(0xFFD32F2F),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Pinned Messages',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${pinnedMessages.length} messages',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              
              // قائمة الرسائل المثبتة
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: pinnedMessages.length,
                  itemBuilder: (context, index) {
                    final pinnedMessage = pinnedMessages[index];
                    return _buildFullPinnedMessageItem(context, pinnedMessage);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر رسالة مثبتة كامل
  Widget _buildFullPinnedMessageItem(BuildContext context, PinnedMessage pinnedMessage) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // محتوى الرسالة
          Text(
            pinnedMessage.content,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // معلومات التثبيت
          Row(
            children: [
              Text(
                'Pinned by ${pinnedMessage.pinnedByName}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '• ${_formatDate(pinnedMessage.pinnedAt)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              if (pinnedMessage.expiresAt != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: pinnedMessage.isExpired ? Colors.red[100] : Colors.orange[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    pinnedMessage.isExpired ? 'Expired' : _getTimeRemaining(pinnedMessage.expiresAt!),
                    style: TextStyle(
                      fontSize: 10,
                      color: pinnedMessage.isExpired ? Colors.red[700] : Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// عرض خيارات الرسالة المثبتة
  void _showPinnedMessageOptions(
    BuildContext context,
    PinnedMessage pinnedMessage,
    PinnedMessagesService pinnedService,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.visibility),
              title: const Text('Go to Message'),
              onTap: () {
                Navigator.of(context).pop();
                onMessageTap?.call(pinnedMessage.messageId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule),
              title: const Text('Update Expiration'),
              onTap: () {
                Navigator.of(context).pop();
                _showExpirationDialog(context, pinnedMessage, pinnedService);
              },
            ),
            ListTile(
              leading: const Icon(Icons.push_pin_outlined, color: Colors.red),
              title: const Text('Unpin Message', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.of(context).pop();
                pinnedService.unpinMessage(pinnedMessage.messageId);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار تحديث انتهاء الصلاحية
  void _showExpirationDialog(
    BuildContext context,
    PinnedMessage pinnedMessage,
    PinnedMessagesService pinnedService,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Expiration'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('1 Hour'),
              onTap: () {
                Navigator.of(context).pop();
                pinnedService.updateExpiration(pinnedMessage.id, const Duration(hours: 1));
              },
            ),
            ListTile(
              title: const Text('1 Day'),
              onTap: () {
                Navigator.of(context).pop();
                pinnedService.updateExpiration(pinnedMessage.id, const Duration(days: 1));
              },
            ),
            ListTile(
              title: const Text('1 Week'),
              onTap: () {
                Navigator.of(context).pop();
                pinnedService.updateExpiration(pinnedMessage.id, const Duration(days: 7));
              },
            ),
            ListTile(
              title: const Text('Never Expire'),
              onTap: () {
                Navigator.of(context).pop();
                pinnedService.updateExpiration(pinnedMessage.id, null);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على الوقت المتبقي
  String _getTimeRemaining(DateTime expiresAt) {
    final now = DateTime.now();
    final difference = expiresAt.difference(now);
    
    if (difference.isNegative) return 'Expired';
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d left';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h left';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m left';
    } else {
      return 'Expiring soon';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
