# Firebase Firestore Rules للتطبيق ArzaTalk
# انسخ هذه القواعد إلى Firebase Console

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد المستخدمين - يمكن للجميع القراءة والكتابة
    match /users/{userId} {
      allow read, write: if true;
    }
    
    // قواعد المنشورات - يمكن للجميع القراءة والكتابة
    match /posts/{postId} {
      allow read, write: if true;
      
      // التعليقات الفرعية
      match /comments/{commentId} {
        allow read, write: if true;
      }
    }
    
    // قواعد الرسائل - يمكن للجميع القراءة والكتابة
    match /chats/{chatId} {
      allow read, write: if true;
      
      // الرسائل الفرعية
      match /messages/{messageId} {
        allow read, write: if true;
      }
    }
    
    // قواعد المجموعات - يمكن للجميع القراءة والكتابة
    match /groups/{groupId} {
      allow read, write: if true;
      
      // رسائل المجموعات
      match /messages/{messageId} {
        allow read, write: if true;
      }
    }
    
    // قواعد القصص - يمكن للجميع القراءة والكتابة
    match /stories/{storyId} {
      allow read, write: if true;
    }
    
    // قواعد التفاعلات - يمكن للجميع القراءة والكتابة
    match /reactions/{reactionId} {
      allow read, write: if true;
    }
    
    // قواعد الإعدادات - يمكن للجميع القراءة والكتابة
    match /settings/{settingId} {
      allow read, write: if true;
    }
    
    // قاعدة عامة لأي مجموعة أخرى
    match /{document=**} {
      allow read, write: if true;
    }
  }
}

# Firebase Storage Rules للوسائط
# انسخ هذه القواعد إلى Firebase Storage Rules

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // قواعد وسائط الدردشة
    match /chat_media/{allPaths=**} {
      allow read, write: if true;
    }
    
    // قواعد الصور الشخصية
    match /profile_images/{allPaths=**} {
      allow read, write: if true;
    }
    
    // قواعد القصص
    match /stories/{allPaths=**} {
      allow read, write: if true;
    }
    
    // قواعد المنشورات
    match /posts/{allPaths=**} {
      allow read, write: if true;
    }
    
    // قاعدة عامة لأي ملف
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}

# خطوات تطبيق القواعد:

1. اذهب إلى Firebase Console: https://console.firebase.google.com
2. اختر مشروع arzatalk-chat
3. اذهب إلى Firestore Database
4. اضغط على "Rules"
5. انسخ قواعد Firestore أعلاه والصقها
6. اضغط "Publish"

7. اذهب إلى Storage
8. اضغط على "Rules"
9. انسخ قواعد Storage أعلاه والصقها
10. اضغط "Publish"

# ملاحظة مهمة:
هذه القواعد تسمح بالوصول الكامل للجميع لأغراض التطوير والاختبار.
في الإنتاج، يجب تقييد الوصول حسب المستخدم المصادق عليه.
