# دليل الإعداد التفصيلي - ArzaTalk

## 🔥 إعداد Firebase

### 1. إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع" أو "Create a project"
3. أدخل اسم المشروع (مثل: arzatalk-chat)
4. اختر إعدادات Google Analytics (اختياري)
5. انقر "إنشاء المشروع"

### 2. إعداد Firestore Database

1. في لوحة تحكم Firebase، اذهب إلى "Firestore Database"
2. انقر "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. اختر موقع قاعدة البيانات (الأقرب لك)
5. انقر "تم"

### 3. إعداد Firebase Storage

1. اذهب إلى "Storage" في القائمة الجانبية
2. انقر "البدء"
3. اختر "Start in test mode"
4. اختر موقع التخزين
5. انقر "تم"

### 4. إضافة تطبيق Android

1. في نظرة عامة المشروع، انقر على أيقونة Android
2. أدخل اسم الحزمة: `com.example.arzatalk`
3. أدخل اسم التطبيق: `ArzaTalk`
4. انقر "تسجيل التطبيق"
5. حمل ملف `google-services.json`
6. ضع الملف في `android/app/`

### 5. إضافة تطبيق iOS (اختياري)

1. انقر على أيقونة iOS
2. أدخل Bundle ID: `com.example.arzatalk`
3. أدخل اسم التطبيق: `ArzaTalk`
4. حمل ملف `GoogleService-Info.plist`
5. ضع الملف في `ios/Runner/`

## 📱 إعداد التطبيق

### 1. تحديث firebase_options.dart

بعد إنشاء التطبيقات في Firebase، ستحصل على معلومات الإعداد. قم بتحديث الملف:

```dart
// في lib/firebase_options.dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'AIzaSyC...', // من إعدادات Firebase
  appId: '1:123456789:android:...', // من إعدادات Firebase
  messagingSenderId: '123456789', // من إعدادات Firebase
  projectId: 'your-project-id', // اسم مشروعك
  storageBucket: 'your-project-id.appspot.com',
);
```

### 2. إعداد قواعد Firestore

1. اذهب إلى Firestore Database > Rules
2. استبدل القواعد الموجودة بالتالي:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if true;
    }
    
    // قواعد الرسائل
    match /messages/{messageId} {
      allow read, write: if true;
    }
    
    // قواعد المحادثات
    match /chats/{chatId} {
      allow read, write: if true;
    }
  }
}
```

### 3. إعداد قواعد Storage

1. اذهب إلى Storage > Rules
2. استبدل القواعد بالتالي:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

## 🛠️ إعداد بيئة التطوير

### متطلبات النظام

- Flutter SDK 3.7.2+
- Dart SDK 3.0.0+
- Android Studio أو VS Code
- Git

### تثبيت Flutter

#### Windows:
1. حمل Flutter SDK من [flutter.dev](https://flutter.dev)
2. استخرج الملف إلى `C:\flutter`
3. أضف `C:\flutter\bin` إلى PATH
4. شغل `flutter doctor` للتحقق

#### macOS:
```bash
# باستخدام Homebrew
brew install flutter

# أو حمل مباشرة من الموقع
```

#### Linux:
```bash
# حمل وتثبيت Flutter
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.7.2-stable.tar.xz
tar xf flutter_linux_3.7.2-stable.tar.xz
export PATH="$PATH:`pwd`/flutter/bin"
```

### إعداد Android Studio

1. حمل وثبت Android Studio
2. ثبت Android SDK
3. ثبت Flutter plugin
4. ثبت Dart plugin

## 🚀 تشغيل المشروع

### 1. استنساخ المشروع

```bash
git clone <repository-url>
cd arzatalk
```

### 2. تثبيت المكتبات

```bash
flutter pub get
```

### 3. إعداد Firebase

- ضع `google-services.json` في `android/app/`
- ضع `GoogleService-Info.plist` في `ios/Runner/` (للـ iOS)
- حدث `firebase_options.dart` بمعلومات مشروعك

### 4. تشغيل التطبيق

```bash
# للتطوير
flutter run

# للإنتاج
flutter run --release
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ Firebase
```
Error: Firebase project not found
```
**الحل:** تأكد من وضع ملفات الإعداد في المكان الصحيح

#### 2. مشاكل الصلاحيات
```
Permission denied for microphone
```
**الحل:** تأكد من إضافة الصلاحيات في AndroidManifest.xml

#### 3. مشاكل التسجيل الصوتي
```
Flutter Sound initialization failed
```
**الحل:** تأكد من منح صلاحية الميكروفون في الجهاز

#### 4. مشاكل الصور
```
Image picker not working
```
**الحل:** تأكد من صلاحيات الكاميرا والتخزين

### أوامر مفيدة:

```bash
# فحص حالة Flutter
flutter doctor

# تنظيف المشروع
flutter clean

# إعادة تثبيت المكتبات
flutter pub get

# تشغيل الاختبارات
flutter test

# بناء APK
flutter build apk

# بناء للـ iOS
flutter build ios
```

## 📊 بنية قاعدة البيانات

### مجموعة Users
```json
{
  "phoneNumber": "+966501234567",
  "name": "أحمد محمد",
  "profileImageUrl": "https://...",
  "lastSeen": "2024-01-01T12:00:00Z",
  "isOnline": true
}
```

### مجموعة Messages
```json
{
  "senderId": "+966501234567",
  "receiverId": "+966507654321",
  "message": "مرحبا",
  "type": "text",
  "timestamp": "2024-01-01T12:00:00Z",
  "isRead": false,
  "mediaUrl": null,
  "duration": null
}
```

### مجموعة Chats
```json
{
  "participants": ["+966501234567", "+966507654321"],
  "lastMessage": { /* آخر رسالة */ },
  "lastMessageTime": "2024-01-01T12:00:00Z",
  "unreadCount": 2
}
```

## 🔐 الأمان

### ملاحظات مهمة:
- هذا التطبيق للتطوير والتعلم
- لا يستخدم تشفير end-to-end
- قواعد Firebase مفتوحة للجميع (للتطوير فقط)

### للإنتاج:
- أضف قواعد أمان صارمة
- استخدم Firebase Auth
- أضف تشفير للرسائل
- أضف التحقق من الهوية

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من `flutter doctor`
2. تأكد من إعداد Firebase
3. راجع ملفات الصلاحيات
4. أنشئ Issue في GitHub
