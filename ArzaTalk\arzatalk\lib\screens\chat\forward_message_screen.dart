import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/message_model.dart';
import '../../services/auth_service.dart';
import '../../services/chat_service.dart';
import '../../services/group_service.dart';
import '../../services/contact_service.dart';

class ForwardMessageScreen extends StatefulWidget {
  final MessageModel message;

  const ForwardMessageScreen({
    super.key,
    required this.message,
  });

  @override
  State<ForwardMessageScreen> createState() => _ForwardMessageScreenState();
}

class _ForwardMessageScreenState extends State<ForwardMessageScreen> {
  final Set<String> _selectedContacts = {};
  final Set<String> _selectedGroups = {};
  bool _isForwarding = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Forward Message'),
        backgroundColor: const Color(0xFFD32F2F),
        foregroundColor: Colors.white,
        actions: [
          if (_selectedContacts.isNotEmpty || _selectedGroups.isNotEmpty)
            IconButton(
              icon: _isForwarding
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.send),
              onPressed: _isForwarding ? null : _forwardMessage,
            ),
        ],
      ),
      body: Column(
        children: [
          // Message Preview
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Row(
              children: [
                const Icon(Icons.forward, color: Color(0xFFD32F2F)),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Forward message:',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getMessagePreview(),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Selection Count
          if (_selectedContacts.isNotEmpty || _selectedGroups.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(12),
              color: const Color(0xFFD32F2F),
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    '${_selectedContacts.length + _selectedGroups.length} selected',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

          // Contacts and Groups List
          Expanded(
            child: DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  const TabBar(
                    labelColor: Color(0xFFD32F2F),
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: Color(0xFFD32F2F),
                    tabs: [
                      Tab(text: 'Contacts'),
                      Tab(text: 'Groups'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildContactsList(),
                        _buildGroupsList(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactsList() {
    return Consumer<ContactService>(
      builder: (context, contactService, child) {
        final contacts = contactService.contacts;

        if (contacts.isEmpty) {
          return const Center(
            child: Text('No contacts available'),
          );
        }

        return ListView.builder(
          itemCount: contacts.length,
          itemBuilder: (context, index) {
            final contact = contacts[index];
            final isSelected = _selectedContacts.contains(contact.phoneNumber);

            return ListTile(
              leading: CircleAvatar(
                backgroundColor: const Color(0xFFD32F2F),
                backgroundImage: contact.profileImageUrl != null
                    ? NetworkImage(contact.profileImageUrl!)
                    : null,
                child: contact.profileImageUrl == null
                    ? Text(
                        contact.name.isNotEmpty
                            ? contact.name[0].toUpperCase()
                            : '?',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              title: Text(contact.name),
              subtitle: Text(contact.phoneNumber),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Color(0xFFD32F2F))
                  : const Icon(Icons.radio_button_unchecked),
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedContacts.remove(contact.phoneNumber);
                  } else {
                    _selectedContacts.add(contact.phoneNumber);
                  }
                });
              },
            );
          },
        );
      },
    );
  }

  Widget _buildGroupsList() {
    return Consumer<GroupService>(
      builder: (context, groupService, child) {
        final groups = groupService.groups;

        if (groups.isEmpty) {
          return const Center(
            child: Text('No groups available'),
          );
        }

        return ListView.builder(
          itemCount: groups.length,
          itemBuilder: (context, index) {
            final group = groups[index];
            final isSelected = _selectedGroups.contains(group.id);

            return ListTile(
              leading: CircleAvatar(
                backgroundColor: const Color(0xFFD32F2F),
                backgroundImage: group.groupImageUrl != null
                    ? NetworkImage(group.groupImageUrl!)
                    : null,
                child: group.groupImageUrl == null
                    ? Text(
                        group.name.isNotEmpty
                            ? group.name[0].toUpperCase()
                            : '?',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              title: Text(group.name),
              subtitle: Text('${group.members.length} members'),
              trailing: isSelected
                  ? const Icon(Icons.check_circle, color: Color(0xFFD32F2F))
                  : const Icon(Icons.radio_button_unchecked),
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedGroups.remove(group.id);
                  } else {
                    _selectedGroups.add(group.id);
                  }
                });
              },
            );
          },
        );
      },
    );
  }

  String _getMessagePreview() {
    switch (widget.message.type) {
      case MessageType.text:
        return widget.message.message;
      case MessageType.image:
        return '📷 Image';
      case MessageType.voice:
        return '🎵 Voice message';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.file:
        return '📄 File';
      case MessageType.sticker:
        return '🎭 Sticker';
    }
  }

  Future<void> _forwardMessage() async {
    if (_selectedContacts.isEmpty && _selectedGroups.isEmpty) return;

    setState(() {
      _isForwarding = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final chatService = Provider.of<ChatService>(context, listen: false);
      final groupService = Provider.of<GroupService>(context, listen: false);
      final currentUser = authService.currentUser!;

      // Forward to selected contacts
      for (final contactId in _selectedContacts) {
        await chatService.sendMessage(
          senderId: currentUser.phoneNumber,
          receiverId: contactId,
          message: widget.message.message,
          type: widget.message.type,
          mediaUrl: widget.message.mediaUrl,
          duration: widget.message.duration,
        );
      }

      // Forward to selected groups
      for (final groupId in _selectedGroups) {
        await groupService.sendGroupMessage(
          groupId: groupId,
          senderId: currentUser.phoneNumber,
          message: widget.message.message,
          type: widget.message.type,
          mediaUrl: widget.message.mediaUrl,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Message forwarded to ${_selectedContacts.length + _selectedGroups.length} ${_selectedContacts.length + _selectedGroups.length == 1 ? 'chat' : 'chats'}',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error forwarding message: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isForwarding = false;
        });
      }
    }
  }
}
