/// نموذج رد الفعل على الرسائل
class MessageReaction {
  final String id;
  final String messageId;
  final String userId;
  final String userName;
  final String emoji;
  final DateTime createdAt;

  const MessageReaction({
    required this.id,
    required this.messageId,
    required this.userId,
    required this.userName,
    required this.emoji,
    required this.createdAt,
  });

  MessageReaction copyWith({
    String? id,
    String? messageId,
    String? userId,
    String? userName,
    String? emoji,
    DateTime? createdAt,
  }) {
    return MessageReaction(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      emoji: emoji ?? this.emoji,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'messageId': messageId,
      'userId': userId,
      'userName': userName,
      'emoji': emoji,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory MessageReaction.fromMap(Map<String, dynamic> map) {
    return MessageReaction(
      id: map['id'] ?? '',
      messageId: map['messageId'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      emoji: map['emoji'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageReaction &&
        other.id == id &&
        other.messageId == messageId &&
        other.userId == userId &&
        other.emoji == emoji;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        messageId.hashCode ^
        userId.hashCode ^
        emoji.hashCode;
  }
}

/// إحصائيات ردود الفعل
class ReactionStats {
  final String emoji;
  final int count;
  final List<String> userNames;
  final bool currentUserReacted;

  const ReactionStats({
    required this.emoji,
    required this.count,
    required this.userNames,
    required this.currentUserReacted,
  });

  ReactionStats copyWith({
    String? emoji,
    int? count,
    List<String>? userNames,
    bool? currentUserReacted,
  }) {
    return ReactionStats(
      emoji: emoji ?? this.emoji,
      count: count ?? this.count,
      userNames: userNames ?? this.userNames,
      currentUserReacted: currentUserReacted ?? this.currentUserReacted,
    );
  }
}

/// نموذج الرسالة المثبتة
class PinnedMessage {
  final String id;
  final String messageId;
  final String chatId;
  final String content;
  final String pinnedBy;
  final String pinnedByName;
  final DateTime pinnedAt;
  final DateTime? expiresAt;
  final bool isActive;

  const PinnedMessage({
    required this.id,
    required this.messageId,
    required this.chatId,
    required this.content,
    required this.pinnedBy,
    required this.pinnedByName,
    required this.pinnedAt,
    this.expiresAt,
    this.isActive = true,
  });

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  PinnedMessage copyWith({
    String? id,
    String? messageId,
    String? chatId,
    String? content,
    String? pinnedBy,
    String? pinnedByName,
    DateTime? pinnedAt,
    DateTime? expiresAt,
    bool? isActive,
  }) {
    return PinnedMessage(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      chatId: chatId ?? this.chatId,
      content: content ?? this.content,
      pinnedBy: pinnedBy ?? this.pinnedBy,
      pinnedByName: pinnedByName ?? this.pinnedByName,
      pinnedAt: pinnedAt ?? this.pinnedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'messageId': messageId,
      'chatId': chatId,
      'content': content,
      'pinnedBy': pinnedBy,
      'pinnedByName': pinnedByName,
      'pinnedAt': pinnedAt.millisecondsSinceEpoch,
      'expiresAt': expiresAt?.millisecondsSinceEpoch,
      'isActive': isActive,
    };
  }

  factory PinnedMessage.fromMap(Map<String, dynamic> map) {
    return PinnedMessage(
      id: map['id'] ?? '',
      messageId: map['messageId'] ?? '',
      chatId: map['chatId'] ?? '',
      content: map['content'] ?? '',
      pinnedBy: map['pinnedBy'] ?? '',
      pinnedByName: map['pinnedByName'] ?? '',
      pinnedAt: DateTime.fromMillisecondsSinceEpoch(map['pinnedAt'] ?? 0),
      expiresAt: map['expiresAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['expiresAt'])
          : null,
      isActive: map['isActive'] ?? true,
    );
  }
}

/// نموذج ثيم الدردشة
class ChatTheme {
  final String id;
  final String chatId;
  final String backgroundImage;
  final String backgroundColor;
  final String bubbleColor;
  final String textColor;
  final String name;
  final bool isDefault;
  final DateTime createdAt;

  const ChatTheme({
    required this.id,
    required this.chatId,
    required this.backgroundImage,
    required this.backgroundColor,
    required this.bubbleColor,
    required this.textColor,
    required this.name,
    this.isDefault = false,
    required this.createdAt,
  });

  ChatTheme copyWith({
    String? id,
    String? chatId,
    String? backgroundImage,
    String? backgroundColor,
    String? bubbleColor,
    String? textColor,
    String? name,
    bool? isDefault,
    DateTime? createdAt,
  }) {
    return ChatTheme(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      backgroundImage: backgroundImage ?? this.backgroundImage,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      bubbleColor: bubbleColor ?? this.bubbleColor,
      textColor: textColor ?? this.textColor,
      name: name ?? this.name,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'chatId': chatId,
      'backgroundImage': backgroundImage,
      'backgroundColor': backgroundColor,
      'bubbleColor': bubbleColor,
      'textColor': textColor,
      'name': name,
      'isDefault': isDefault,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory ChatTheme.fromMap(Map<String, dynamic> map) {
    return ChatTheme(
      id: map['id'] ?? '',
      chatId: map['chatId'] ?? '',
      backgroundImage: map['backgroundImage'] ?? '',
      backgroundColor: map['backgroundColor'] ?? '#FFFFFF',
      bubbleColor: map['bubbleColor'] ?? '#D32F2F',
      textColor: map['textColor'] ?? '#000000',
      name: map['name'] ?? 'Default',
      isDefault: map['isDefault'] ?? false,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }
}

/// نموذج الملصق
class StickerModel {
  final String id;
  final String name;
  final String imageUrl;
  final String category;
  final List<String> tags;
  final bool isAnimated;
  final DateTime createdAt;

  const StickerModel({
    required this.id,
    required this.name,
    required this.imageUrl,
    required this.category,
    required this.tags,
    this.isAnimated = false,
    required this.createdAt,
  });

  StickerModel copyWith({
    String? id,
    String? name,
    String? imageUrl,
    String? category,
    List<String>? tags,
    bool? isAnimated,
    DateTime? createdAt,
  }) {
    return StickerModel(
      id: id ?? this.id,
      name: name ?? this.name,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      isAnimated: isAnimated ?? this.isAnimated,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'imageUrl': imageUrl,
      'category': category,
      'tags': tags,
      'isAnimated': isAnimated,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory StickerModel.fromMap(Map<String, dynamic> map) {
    return StickerModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      imageUrl: map['imageUrl'] ?? '',
      category: map['category'] ?? '',
      tags: List<String>.from(map['tags'] ?? []),
      isAnimated: map['isAnimated'] ?? false,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
    );
  }
}
