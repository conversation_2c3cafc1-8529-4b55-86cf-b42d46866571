import 'package:flutter/material.dart';

class VoiceMessage extends StatefulWidget {
  final String audioUrl;
  final int duration;
  final bool isMe;

  const VoiceMessage({
    super.key,
    required this.audioUrl,
    required this.duration,
    required this.isMe,
  });

  @override
  State<VoiceMessage> createState() => _VoiceMessageState();
}

class _VoiceMessageState extends State<VoiceMessage> {
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  @override
  void initState() {
    super.initState();
    _totalDuration = Duration(seconds: widget.duration);
  }

  Future<void> _togglePlayback() async {
    if (_isPlaying) {
      // إيقاف التشغيل
      setState(() {
        _isPlaying = false;
        _currentPosition = Duration.zero;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إيقاف تشغيل الرسالة الصوتية'),
          duration: Duration(seconds: 1),
        ),
      );
    } else {
      // بدء التشغيل (محاكاة)
      setState(() {
        _isLoading = true;
      });

      try {
        // محاكاة تحميل وتشغيل الملف الصوتي
        await Future.delayed(const Duration(seconds: 1));
        
        setState(() {
          _isPlaying = true;
          _isLoading = false;
        });

        // محاكاة تقدم التشغيل
        _simulatePlayback();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تشغيل الرسالة الصوتية (محاكاة)'),
            duration: Duration(seconds: 1),
          ),
        );
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تشغيل الملف الصوتي: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _simulatePlayback() async {
    final totalSeconds = _totalDuration.inSeconds;
    for (int i = 0; i <= totalSeconds && _isPlaying; i++) {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted && _isPlaying) {
        setState(() {
          _currentPosition = Duration(seconds: i);
        });
      }
    }
    
    // انتهاء التشغيل
    if (mounted && _isPlaying) {
      setState(() {
        _isPlaying = false;
        _currentPosition = Duration.zero;
      });
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر التشغيل/الإيقاف
          GestureDetector(
            onTap: _togglePlayback,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: widget.isMe ? Colors.white.withValues(alpha: 0.2) : Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: _isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.isMe ? Colors.white : Colors.grey[600]!,
                        ),
                      ),
                    )
                  : Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: widget.isMe ? Colors.white : Colors.grey[600],
                      size: 24,
                    ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // شريط التقدم
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الموجات الصوتية (تصميم بسيط)
                SizedBox(
                  height: 30,
                  child: Row(
                    children: List.generate(20, (index) {
                      final progress = _totalDuration.inMilliseconds > 0
                          ? _currentPosition.inMilliseconds / _totalDuration.inMilliseconds
                          : 0.0;
                      final isActive = index < (20 * progress);
                      
                      return Expanded(
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 1),
                          height: (index % 3 == 0) ? 20 : (index % 2 == 0) ? 15 : 10,
                          decoration: BoxDecoration(
                            color: isActive
                                ? (widget.isMe ? Colors.white : const Color(0xFFD32F2F))
                                : (widget.isMe ? Colors.white.withValues(alpha: 0.3) : Colors.grey[400]),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // مدة التشغيل
                Text(
                  _isPlaying
                      ? '${_formatDuration(_currentPosition)} / ${_formatDuration(_totalDuration)}'
                      : _formatDuration(_totalDuration),
                  style: TextStyle(
                    fontSize: 11,
                    color: widget.isMe ? Colors.white70 : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
