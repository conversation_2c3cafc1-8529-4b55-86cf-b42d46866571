import 'package:flutter/material.dart';

/// خدمة الذكاء الاصطناعي للميزات الذكية
class AIService extends ChangeNotifier {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  // قاموس الردود الذكية
  final Map<String, List<String>> _smartReplies = {
    // تحيات
    'hello': ['Hello! 👋', 'Hi there! 😊', 'Hey! How are you?', 'Good to hear from you!'],
    'hi': ['Hi! 👋', 'Hello! 😊', 'Hey there!', 'Hi! How\'s it going?'],
    'good morning': ['Good morning! ☀️', 'Morning! Have a great day!', 'Good morning! 🌅'],
    'good evening': ['Good evening! 🌆', 'Evening! Hope you had a good day!', 'Good evening! 🌙'],
    'good night': ['Good night! 😴', 'Sweet dreams! 🌙', 'Sleep well! 💤'],
    
    // أسئلة شائعة
    'how are you': ['I\'m good, thanks! 😊', 'Doing well! How about you?', 'Great! And you?'],
    'what\'s up': ['Not much! You?', 'Just chilling! 😎', 'All good here!'],
    'how\'s it going': ['Going well! 👍', 'Pretty good! How about you?', 'All good! 😊'],
    
    // موافقة ورفض
    'yes': ['Great! 👍', 'Awesome! 🎉', 'Perfect! ✅', 'Sounds good!'],
    'no': ['No problem! 👌', 'That\'s okay!', 'No worries! 😊', 'Understood!'],
    'ok': ['👍', 'Got it!', 'Alright!', 'Perfect!'],
    'okay': ['👍', 'Got it!', 'Sounds good!', 'Perfect!'],
    
    // شكر واعتذار
    'thank you': ['You\'re welcome! 😊', 'No problem!', 'Happy to help! 👍', 'Anytime!'],
    'thanks': ['You\'re welcome! 😊', 'No worries!', 'Glad to help!', 'Anytime!'],
    'sorry': ['No problem!', 'It\'s okay! 😊', 'Don\'t worry about it!', 'All good!'],
    
    // أسئلة عن الوقت والمكان
    'where are you': ['I\'m at home', 'At work', 'On my way', 'Just arrived'],
    'when': ['Soon!', 'In a few minutes', 'Give me 5 minutes', 'Almost ready!'],
    
    // طعام ومشروبات
    'hungry': ['Let\'s eat! 🍕', 'I know a good place!', 'What do you want to eat?'],
    'coffee': ['Coffee sounds great! ☕', 'I need coffee too!', 'Let\'s grab coffee!'],
    
    // عمل ودراسة
    'work': ['How\'s work going?', 'Busy day?', 'Hope work is good!'],
    'meeting': ['Good luck with the meeting!', 'Hope it goes well!', 'Let me know how it goes!'],
    
    // مشاعر إيجابية
    'happy': ['That\'s great! 😊', 'So happy for you! 🎉', 'Awesome news!'],
    'excited': ['That\'s exciting! 🎉', 'Can\'t wait!', 'So cool! 😎'],
    'love': ['❤️', '😍', 'Love you too!', '💕'],
    
    // مشاعر سلبية
    'sad': ['Hope you feel better! 🤗', 'I\'m here for you!', 'Sending hugs! 💙'],
    'tired': ['Get some rest! 😴', 'You deserve a break!', 'Take care of yourself!'],
    'sick': ['Feel better soon! 🤒', 'Take care!', 'Hope you recover quickly!'],
    
    // خطط ومواعيد
    'tomorrow': ['See you tomorrow!', 'Looking forward to it!', 'Sounds like a plan!'],
    'tonight': ['Sounds fun!', 'What time?', 'Count me in!'],
    'weekend': ['Have a great weekend!', 'Any fun plans?', 'Enjoy your weekend!'],
    
    // طقس
    'weather': ['Nice weather today!', 'Hope the weather is good!', 'Perfect day!'],
    'rain': ['Stay dry! ☔', 'Cozy weather!', 'Good day to stay inside!'],
    'sunny': ['Beautiful day! ☀️', 'Perfect weather!', 'Enjoy the sunshine!'],
  };

  // قاموس المشاعر
  final Map<String, String> _emotions = {
    // مشاعر إيجابية
    'happy': '😊', 'excited': '🎉', 'love': '❤️', 'great': '👍', 'awesome': '🔥',
    'amazing': '🤩', 'perfect': '✨', 'wonderful': '🌟', 'fantastic': '🎊', 'excellent': '💯',
    'good': '👌', 'nice': '😌', 'cool': '😎', 'fun': '🎈', 'beautiful': '😍',
    
    // مشاعر سلبية
    'sad': '😢', 'angry': '😠', 'tired': '😴', 'sick': '🤒', 'worried': '😟',
    'stressed': '😰', 'confused': '😕', 'disappointed': '😞', 'frustrated': '😤', 'upset': '😔',
    'bad': '👎', 'terrible': '😱', 'awful': '😖', 'horrible': '😨', 'annoying': '😒',
    
    // مشاعر محايدة
    'okay': '🙂', 'fine': '😐', 'normal': '😶', 'busy': '💼', 'working': '💻',
    'thinking': '🤔', 'wondering': '💭', 'maybe': '🤷', 'probably': '🤞', 'hopefully': '🙏',
    
    // أنشطة
    'eating': '🍽️', 'cooking': '👨‍🍳', 'sleeping': '😴', 'studying': '📚', 'reading': '📖',
    'watching': '📺', 'listening': '🎵', 'playing': '🎮', 'shopping': '🛍️', 'traveling': '✈️',
    'driving': '🚗', 'walking': '🚶', 'running': '🏃', 'exercise': '💪', 'gym': '🏋️',
  };

  // قاموس التوقعات النصية
  final Map<String, List<String>> _textPredictions = {
    'how': ['how are you?', 'how\'s it going?', 'how was your day?', 'how about you?'],
    'what': ['what\'s up?', 'what are you doing?', 'what time?', 'what do you think?'],
    'where': ['where are you?', 'where should we meet?', 'where did you go?'],
    'when': ['when are you free?', 'when should we meet?', 'when did you arrive?'],
    'why': ['why not?', 'why do you think so?', 'why did you do that?'],
    'can': ['can you help me?', 'can we meet?', 'can you call me?', 'can you send me?'],
    'will': ['will you be there?', 'will you come?', 'will you help me?', 'will you call me?'],
    'do': ['do you want to?', 'do you have time?', 'do you like it?', 'do you know?'],
    'did': ['did you see?', 'did you hear?', 'did you finish?', 'did you arrive?'],
    'are': ['are you okay?', 'are you free?', 'are you coming?', 'are you there?'],
    'is': ['is everything okay?', 'is it ready?', 'is it working?', 'is it good?'],
    'have': ['have a good day!', 'have fun!', 'have you seen?', 'have you heard?'],
    'let': ['let me know!', 'let\'s go!', 'let\'s meet!', 'let\'s do it!'],
    'see': ['see you later!', 'see you tomorrow!', 'see you soon!', 'see you there!'],
    'talk': ['talk to you later!', 'talk soon!', 'talk to you tomorrow!'],
    'good': ['good morning!', 'good evening!', 'good night!', 'good luck!', 'good job!'],
    'thank': ['thank you!', 'thank you so much!', 'thanks!', 'thanks a lot!'],
    'i': ['I\'m good', 'I\'m here', 'I\'m coming', 'I\'m ready', 'I love you', 'I miss you'],
  };

  // قاموس الترجمة البسيطة (إنجليزي-عربي)
  final Map<String, String> _translations = {
    'hello': 'مرحبا',
    'hi': 'أهلا',
    'good morning': 'صباح الخير',
    'good evening': 'مساء الخير',
    'good night': 'تصبح على خير',
    'how are you': 'كيف حالك؟',
    'thank you': 'شكرا لك',
    'thanks': 'شكرا',
    'sorry': 'آسف',
    'yes': 'نعم',
    'no': 'لا',
    'okay': 'حسنا',
    'love': 'حب',
    'happy': 'سعيد',
    'sad': 'حزين',
    'good': 'جيد',
    'bad': 'سيء',
    'beautiful': 'جميل',
    'work': 'عمل',
    'home': 'منزل',
    'family': 'عائلة',
    'friend': 'صديق',
    'time': 'وقت',
    'day': 'يوم',
    'night': 'ليل',
    'today': 'اليوم',
    'tomorrow': 'غدا',
    'yesterday': 'أمس',
    'week': 'أسبوع',
    'month': 'شهر',
    'year': 'سنة',
    'eat': 'أكل',
    'drink': 'شرب',
    'sleep': 'نوم',
    'study': 'دراسة',
    'read': 'قراءة',
    'write': 'كتابة',
    'call': 'اتصال',
    'message': 'رسالة',
    'phone': 'هاتف',
    'computer': 'حاسوب',
    'car': 'سيارة',
    'house': 'بيت',
    'school': 'مدرسة',
    'hospital': 'مستشفى',
    'restaurant': 'مطعم',
    'shop': 'متجر',
    'money': 'مال',
    'food': 'طعام',
    'water': 'ماء',
    'coffee': 'قهوة',
    'tea': 'شاي',
    'book': 'كتاب',
    'movie': 'فيلم',
    'music': 'موسيقى',
    'game': 'لعبة',
    'sport': 'رياضة',
    'travel': 'سفر',
    'weather': 'طقس',
    'sun': 'شمس',
    'rain': 'مطر',
    'hot': 'حار',
    'cold': 'بارد',
    'big': 'كبير',
    'small': 'صغير',
    'new': 'جديد',
    'old': 'قديم',
    'fast': 'سريع',
    'slow': 'بطيء',
    'easy': 'سهل',
    'hard': 'صعب',
    'important': 'مهم',
    'interesting': 'مثير للاهتمام',
  };

  /// اقتراح ردود ذكية
  List<String> getSuggestedReplies(String message) {
    final lowerMessage = message.toLowerCase().trim();
    
    // البحث عن كلمات مفتاحية في الرسالة
    for (final entry in _smartReplies.entries) {
      if (lowerMessage.contains(entry.key)) {
        final replies = List<String>.from(entry.value);
        replies.shuffle(); // خلط الردود لتنويع
        return replies.take(3).toList(); // إرجاع أول 3 ردود
      }
    }
    
    // ردود افتراضية إذا لم توجد كلمات مفتاحية
    final defaultReplies = [
      'Got it! 👍',
      'Thanks for letting me know!',
      'Sounds good! 😊',
      'I understand!',
      'Okay! 👌',
      'Sure thing!',
      'Alright!',
      'Perfect! ✨',
    ];
    
    defaultReplies.shuffle();
    return defaultReplies.take(3).toList();
  }

  /// ترجمة فورية للرسائل
  String translateMessage(String message, {bool toArabic = true}) {
    final words = message.toLowerCase().split(' ');
    final translatedWords = <String>[];
    
    for (final word in words) {
      final cleanWord = word.replaceAll(RegExp(r'[^\w\s]'), ''); // إزالة علامات الترقيم
      
      if (toArabic) {
        // ترجمة من الإنجليزية للعربية
        if (_translations.containsKey(cleanWord)) {
          translatedWords.add(_translations[cleanWord]!);
        } else {
          translatedWords.add(word); // الاحتفاظ بالكلمة الأصلية إذا لم توجد ترجمة
        }
      } else {
        // ترجمة من العربية للإنجليزية (عكس القاموس)
        final englishWord = _translations.entries
            .firstWhere((entry) => entry.value == cleanWord, orElse: () => MapEntry(word, word))
            .key;
        translatedWords.add(englishWord);
      }
    }
    
    return translatedWords.join(' ');
  }

  /// تحليل المشاعر في الرسائل
  Map<String, dynamic> analyzeEmotion(String message) {
    final lowerMessage = message.toLowerCase();
    final words = lowerMessage.split(' ');
    
    final emotionCounts = <String, int>{};
    final detectedEmotions = <String>[];
    
    // البحث عن كلمات المشاعر
    for (final word in words) {
      final cleanWord = word.replaceAll(RegExp(r'[^\w\s]'), '');
      
      if (_emotions.containsKey(cleanWord)) {
        final emotion = _getEmotionCategory(cleanWord);
        emotionCounts[emotion] = (emotionCounts[emotion] ?? 0) + 1;
        detectedEmotions.add('$cleanWord ${_emotions[cleanWord]}');
      }
    }
    
    // تحديد المشاعر الغالبة
    String dominantEmotion = 'neutral';
    String emotionEmoji = '😐';
    double confidence = 0.0;
    
    if (emotionCounts.isNotEmpty) {
      final maxEntry = emotionCounts.entries.reduce((a, b) => a.value > b.value ? a : b);
      dominantEmotion = maxEntry.key;
      confidence = maxEntry.value / words.length;
      
      // اختيار إيموجي مناسب
      switch (dominantEmotion) {
        case 'positive':
          emotionEmoji = '😊';
          break;
        case 'negative':
          emotionEmoji = '😔';
          break;
        case 'neutral':
          emotionEmoji = '😐';
          break;
      }
    }
    
    return {
      'emotion': dominantEmotion,
      'emoji': emotionEmoji,
      'confidence': confidence,
      'detectedWords': detectedEmotions,
      'wordCount': words.length,
    };
  }

  /// توقع النص أثناء الكتابة
  List<String> predictText(String currentText) {
    if (currentText.isEmpty) return [];
    
    final words = currentText.toLowerCase().trim().split(' ');
    final lastWord = words.last;
    
    // البحث عن توقعات بناءً على آخر كلمة
    for (final entry in _textPredictions.entries) {
      if (entry.key.startsWith(lastWord) || lastWord.startsWith(entry.key)) {
        return entry.value.take(5).toList();
      }
    }
    
    // توقعات عامة بناءً على السياق
    final predictions = <String>[];
    
    // إذا كانت الرسالة تبدأ بسؤال
    if (currentText.startsWith('how') || currentText.startsWith('what') || 
        currentText.startsWith('where') || currentText.startsWith('when')) {
      predictions.addAll(['are you?', 'do you think?', 'about it?', 'should we do?']);
    }
    
    // إذا كانت الرسالة تحتوي على كلمات شائعة
    if (currentText.contains('good')) {
      predictions.addAll(['morning!', 'evening!', 'night!', 'luck!', 'job!']);
    }
    
    if (currentText.contains('thank')) {
      predictions.addAll(['you!', 'you so much!', 's a lot!']);
    }
    
    // توقعات افتراضية
    if (predictions.isEmpty) {
      predictions.addAll([
        'you!', 'me!', 'it!', 'that!', 'this!',
        'soon!', 'later!', 'tomorrow!', 'today!',
        'please!', 'thanks!', 'okay!', 'sure!',
      ]);
    }
    
    return predictions.take(5).toList();
  }

  /// تصنيف المشاعر
  String _getEmotionCategory(String word) {
    final positiveWords = ['happy', 'excited', 'love', 'great', 'awesome', 'amazing', 
                          'perfect', 'wonderful', 'fantastic', 'excellent', 'good', 
                          'nice', 'cool', 'fun', 'beautiful'];
    
    final negativeWords = ['sad', 'angry', 'tired', 'sick', 'worried', 'stressed', 
                          'confused', 'disappointed', 'frustrated', 'upset', 'bad', 
                          'terrible', 'awful', 'horrible', 'annoying'];
    
    if (positiveWords.contains(word)) return 'positive';
    if (negativeWords.contains(word)) return 'negative';
    return 'neutral';
  }

  /// الحصول على إحصائيات الذكاء الاصطناعي
  Map<String, dynamic> getAIStats() {
    return {
      'smartRepliesCount': _smartReplies.length,
      'emotionsCount': _emotions.length,
      'translationsCount': _translations.length,
      'predictionsCount': _textPredictions.length,
      'supportedLanguages': ['English', 'Arabic'],
      'features': [
        'Smart Replies',
        'Emotion Analysis',
        'Text Prediction',
        'Translation',
      ],
    };
  }
}
