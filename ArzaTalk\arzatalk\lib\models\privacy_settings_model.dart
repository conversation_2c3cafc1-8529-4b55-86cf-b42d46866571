class PrivacySettings {
  final bool showPhoneNumber;
  final bool showAge;
  final bool showGender;
  final bool showLocation;
  final bool showRegistrationDate;
  final bool showLastSeen;
  final bool showOnlineStatus;

  const PrivacySettings({
    this.showPhoneNumber = true,
    this.showAge = true,
    this.showGender = true,
    this.showLocation = true,
    this.showRegistrationDate = true,
    this.showLastSeen = true,
    this.showOnlineStatus = true,
  });

  PrivacySettings copyWith({
    bool? showPhoneNumber,
    bool? showAge,
    bool? showGender,
    bool? showLocation,
    bool? showRegistrationDate,
    bool? showLastSeen,
    bool? showOnlineStatus,
  }) {
    return PrivacySettings(
      showPhoneNumber: showPhoneNumber ?? this.showPhoneNumber,
      showAge: showAge ?? this.showAge,
      showGender: showGender ?? this.showGender,
      showLocation: showLocation ?? this.showLocation,
      showRegistrationDate: showRegistrationDate ?? this.showRegistrationDate,
      showLastSeen: showLastSeen ?? this.showLastSeen,
      showOnlineStatus: showOnlineStatus ?? this.showOnlineStatus,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'showPhoneNumber': showPhoneNumber,
      'showAge': showAge,
      'showGender': showGender,
      'showLocation': showLocation,
      'showRegistrationDate': showRegistrationDate,
      'showLastSeen': showLastSeen,
      'showOnlineStatus': showOnlineStatus,
    };
  }

  factory PrivacySettings.fromJson(Map<String, dynamic> json) {
    return PrivacySettings(
      showPhoneNumber: json['showPhoneNumber'] ?? true,
      showAge: json['showAge'] ?? true,
      showGender: json['showGender'] ?? true,
      showLocation: json['showLocation'] ?? true,
      showRegistrationDate: json['showRegistrationDate'] ?? true,
      showLastSeen: json['showLastSeen'] ?? true,
      showOnlineStatus: json['showOnlineStatus'] ?? true,
    );
  }
}
