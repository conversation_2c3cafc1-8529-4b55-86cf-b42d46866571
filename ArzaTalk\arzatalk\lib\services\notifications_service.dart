import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';

/// خدمة الإشعارات للشبكة الاجتماعية
class NotificationsService extends ChangeNotifier {
  final List<NotificationModel> _notifications = [];
  int _unreadCount = 0;

  /// الحصول على جميع الإشعارات
  List<NotificationModel> get notifications => List.unmodifiable(_notifications);

  /// الحصول على عدد الإشعارات غير المقروءة
  int get unreadCount => _unreadCount;

  /// الحصول على الإشعارات غير المقروءة فقط
  List<NotificationModel> get unreadNotifications => 
      _notifications.where((n) => !n.isRead).toList();

  /// إضافة إشعار جديد
  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification); // إضافة في المقدمة
    if (!notification.isRead) {
      _unreadCount++;
    }
    notifyListeners();
  }

  /// إنشاء إشعار تفاعل (إعجاب، حب، إلخ)
  void createReactionNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
    required String reactionType,
  }) {
    // تجنب إرسال إشعار للمستخدم نفسه
    if (userId == fromUserId) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      type: reactionType,
      title: 'New Reaction',
      message: '$fromUserName reacted to your post',
      postId: postId,
      createdAt: DateTime.now(),
    );

    addNotification(notification);
  }

  /// إنشاء إشعار تعليق
  void createCommentNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
    required String commentId,
  }) {
    // تجنب إرسال إشعار للمستخدم نفسه
    if (userId == fromUserId) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      type: NotificationTypes.comment,
      title: 'New Comment',
      message: '$fromUserName commented on your post',
      postId: postId,
      commentId: commentId,
      createdAt: DateTime.now(),
    );

    addNotification(notification);
  }

  /// إنشاء إشعار رد على تعليق
  void createReplyNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
    required String commentId,
  }) {
    // تجنب إرسال إشعار للمستخدم نفسه
    if (userId == fromUserId) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      type: NotificationTypes.reply,
      title: 'New Reply',
      message: '$fromUserName replied to your comment',
      postId: postId,
      commentId: commentId,
      createdAt: DateTime.now(),
    );

    addNotification(notification);
  }

  /// إنشاء إشعار مشاركة
  void createShareNotification({
    required String userId,
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
  }) {
    // تجنب إرسال إشعار للمستخدم نفسه
    if (userId == fromUserId) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      fromUserId: fromUserId,
      fromUserName: fromUserName,
      fromUserAvatar: fromUserAvatar,
      type: NotificationTypes.share,
      title: 'Post Shared',
      message: '$fromUserName shared your post',
      postId: postId,
      createdAt: DateTime.now(),
    );

    addNotification(notification);
  }

  /// إنشاء إشعار منشور جديد
  void createNewPostNotification({
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String postId,
    required List<String> followerIds,
  }) {
    for (final followerId in followerIds) {
      // تجنب إرسال إشعار للمستخدم نفسه
      if (followerId == fromUserId) continue;

      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: followerId,
        fromUserId: fromUserId,
        fromUserName: fromUserName,
        fromUserAvatar: fromUserAvatar,
        type: NotificationTypes.post,
        title: 'New Post',
        message: '$fromUserName shared a new post',
        postId: postId,
        createdAt: DateTime.now(),
      );

      addNotification(notification);
    }
  }

  /// إنشاء إشعار قصة جديدة
  void createNewStoryNotification({
    required String fromUserId,
    required String fromUserName,
    required String fromUserAvatar,
    required String storyId,
    required List<String> followerIds,
  }) {
    for (final followerId in followerIds) {
      // تجنب إرسال إشعار للمستخدم نفسه
      if (followerId == fromUserId) continue;

      final notification = NotificationModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: followerId,
        fromUserId: fromUserId,
        fromUserName: fromUserName,
        fromUserAvatar: fromUserAvatar,
        type: NotificationTypes.story,
        title: 'New Story',
        message: '$fromUserName added a new story',
        data: {'storyId': storyId},
        createdAt: DateTime.now(),
      );

      addNotification(notification);
    }
  }

  /// تحديد إشعار كمقروء
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1 && !_notifications[index].isRead) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _unreadCount--;
      notifyListeners();
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      if (!_notifications[i].isRead) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    }
    _unreadCount = 0;
    notifyListeners();
  }

  /// حذف إشعار
  void deleteNotification(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      if (!_notifications[index].isRead) {
        _unreadCount--;
      }
      _notifications.removeAt(index);
      notifyListeners();
    }
  }

  /// مسح جميع الإشعارات
  void clearAllNotifications() {
    _notifications.clear();
    _unreadCount = 0;
    notifyListeners();
  }

  /// الحصول على الإشعارات حسب النوع
  List<NotificationModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  /// إضافة إشعارات تجريبية
  void addDemoNotifications() {
    final demoNotifications = [
      NotificationModel(
        id: '1',
        userId: 'current_user',
        fromUserId: 'user1',
        fromUserName: 'أحمد محمد',
        fromUserAvatar: '',
        type: NotificationTypes.like,
        title: 'New Like',
        message: 'أحمد محمد liked your post',
        postId: 'post1',
        createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      NotificationModel(
        id: '2',
        userId: 'current_user',
        fromUserId: 'user2',
        fromUserName: 'فاطمة علي',
        fromUserAvatar: '',
        type: NotificationTypes.comment,
        title: 'New Comment',
        message: 'فاطمة علي commented on your post',
        postId: 'post2',
        commentId: 'comment1',
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      NotificationModel(
        id: '3',
        userId: 'current_user',
        fromUserId: 'user3',
        fromUserName: 'محمد حسن',
        fromUserAvatar: '',
        type: NotificationTypes.reply,
        title: 'New Reply',
        message: 'محمد حسن replied to your comment',
        postId: 'post3',
        commentId: 'comment2',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      NotificationModel(
        id: '4',
        userId: 'current_user',
        fromUserId: 'user4',
        fromUserName: 'سارة أحمد',
        fromUserAvatar: '',
        type: NotificationTypes.share,
        title: 'Post Shared',
        message: 'سارة أحمد shared your post',
        postId: 'post4',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      NotificationModel(
        id: '5',
        userId: 'current_user',
        fromUserId: 'user5',
        fromUserName: 'عبدالله خالد',
        fromUserAvatar: '',
        type: NotificationTypes.post,
        title: 'New Post',
        message: 'عبدالله خالد shared a new post',
        postId: 'post5',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    for (final notification in demoNotifications) {
      addNotification(notification);
    }
  }
}
